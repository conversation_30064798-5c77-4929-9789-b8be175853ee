[{"name": "oracle.jdbc.internal.ACProxyable", "allDeclaredConstructors": true, "allDeclaredMethods": true}, {"name": "oracle.jdbc.driver.T4CDriverExtension", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.jdbc.driver.T2CDriverExtension", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.jdbc.driver.ShardingDriverExtension", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.net.ano.Ano", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.net.ano.AuthenticationService", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.net.ano.DataIntegrityService", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.net.ano.EncryptionService", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.net.ano.SupervisorService", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "oracle.sql.TypeDescriptor", "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "oracle.sql.TypeDescriptorFactory", "allDeclaredConstructors": true}, {"name": "oracle.sql.AnyDataFactory", "allDeclaredConstructors": true}]