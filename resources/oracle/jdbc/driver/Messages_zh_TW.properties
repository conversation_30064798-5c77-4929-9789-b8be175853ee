#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=\u63A7\u5236 Oracle JDBC \u9A45\u52D5\u7A0B\u5F0F\u7684\u8A3A\u65B7\u529F\u80FD.

DiagnosabilityMBeanConstructor()=\u300COracle JDBC \u8A3A\u65B7\u529F\u80FD MBean\u300D\u7684\u96F6\u5F15\u6578\u5EFA\u69CB\u5B50

DiagnosabilityMBeanLoggingEnabledDescription=\u6B64\u5E03\u6797\u503C\u5C6C\u6027\u63A7\u5236\u6240\u6709\u7684 Oracle JDBC \u65E5\u8A8C\u8A18\u9304\u4EE3\u78BC. \u82E5\u70BA false, \u5247\u4E0D\u6703\u7522\u751F\u65E5\u8A8C\u8A0A\u606F. \u82E5\u70BA true, \u5247\u7531 java.util.logging \u5C64\u7D1A\u548C\u7BE9\u9078\u63A7\u5236\u65E5\u8A8C\u8A0A\u606F.

DiagnosabilityMBeanStateManageableDescription=\u7121\u6CD5\u555F\u52D5\u53CA\u505C\u6B62 Oracle JDBC \u9A45\u52D5\u7A0B\u5F0F. \u4E00\u5F8B\u50B3\u56DE false.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC \u9A45\u52D5\u7A0B\u5F0F\u672A\u900F\u904E\u300C\u8A3A\u65B7\u529F\u80FD MBean\u300D\u63D0\u4F9B\u7D71\u8A08\u8CC7\u6599.

DiagnosabilityMBeanTraceControllerDescription=Clio \u8FFD\u8E64\u63A7\u5236\u5668.

DiagnosabilityMBeanSuspendDescription=Clio \u66AB\u505C\u4F5C\u696D.

DiagnosabilityMBeanResumeDescription=Clio \u7E7C\u7E8C\u4F5C\u696D.

DiagnosabilityMBeanTraceDescription=Clio \u8FFD\u8E64\u4F5C\u696D.

DiagnosabilityMBeanEnableContinousLoggingDescription=\u555F\u7528\u4FDD\u8B77\u7684\u9023\u7E8C\u6A94\u6848\u65E5\u8A8C\u8A18\u9304. \u53EF\u4EE5\u5957\u7528 ServiceName \u548C UserName \u7BE9\u9078\u689D\u4EF6 (\u82E5\u8A2D\u5B9A\u7684\u8A71). \u9810\u8A2D\u70BA\u505C\u7528\u72C0\u614B.

DiagnosabilityMBeanDisableContinousLoggingDescription=\u505C\u7528\u4FDD\u8B77\u7684\u9023\u7E8C\u6A94\u6848\u65E5\u8A8C\u8A18\u9304. \u9810\u8A2D\u70BA\u505C\u7528\u72C0\u614B.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u82E5\u8A2D\u5B9A, \u53EF\u4EE5\u5957\u7528 ServiceName \u548C UserName \u7BE9\u9078\u689D\u4EF6. \u9810\u8A2D\u70BA\u555F\u7528\u72C0\u614B.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=\u505C\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u9810\u8A2D\u70BA\u555F\u7528\u72C0\u614B.

DiagnosabilityMBeanServiceNameFilterDescription=\u82E5\u8A2D\u5B9A ServiceName \u7BE9\u9078\u689D\u4EF6, \u5C31\u53EA\u6703\u5C0D\u5DF2\u8A2D\u5B9A\u7684\u670D\u52D9\u9023\u7DDA\u555F\u7528\u8A18\u61B6\u9AD4\u5F0F/\u9023\u7E8C\u65E5\u8A8C\u8A18\u9304.

DiagnosabilityMBeanUserFilterDescription=\u82E5\u8A2D\u5B9A User \u7BE9\u9078\u689D\u4EF6, \u5C31\u53EA\u6703\u5C0D\u5DF2\u8A2D\u5B9A\u7684\u4F7F\u7528\u8005\u9023\u7DDA\u555F\u7528\u8A18\u61B6\u9AD4\u5F0F/\u9023\u7E8C\u65E5\u8A8C\u8A18\u9304.

ReplayStatisticsMBeanDescription=\u986F\u793A\u61C9\u7528\u7A0B\u5F0F\u9023\u7E8C\u6027 (AC) \u529F\u80FD\u7684\u7D71\u8A08\u8CC7\u6599.

ReplayStatisticsMBeanConstructor=Oracle JDBC AC \u7D71\u8A08\u8CC7\u6599 MBean \u7684\u96F6\u5F15\u6578\u5EFA\u69CB\u5B50

ReplayStatisticsMBeanAllStatisticsDescription=\u53D6\u5F97\u6240\u6709\u5DF2\u77E5\u9A45\u52D5\u7A0B\u5F0F\u8CC7\u6599\u4F86\u6E90\u57F7\u884C\u8655\u7406\u7684 AC \u7D71\u8A08\u8CC7\u6599.

ReplayStatisticsMBeanGetDSStatisticsDescription=\u53D6\u5F97\u7279\u5B9A\u9A45\u52D5\u7A0B\u5F0F\u8CC7\u6599\u4F86\u6E90\u57F7\u884C\u8655\u7406\u7684 AC \u7D71\u8A08\u8CC7\u6599.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=\u63A7\u5236 Oracle JDBC \u9A45\u52D5\u7A0B\u5F0F\u7684\u8A3A\u65B7\u529F\u80FD.

DiagnosticsMBeanLoggingEnabledAttributeDescription=\u6B64\u5E03\u6797\u503C\u5C6C\u6027\u63A7\u5236\u6240\u6709\u7684 Oracle JDBC \u65E5\u8A8C\u8A18\u9304\u4EE3\u78BC. \u9810\u8A2D\u503C\u70BA false. \u82E5\u958B\u555F, \u7531 java.util.logging \u5C64\u7D1A\u548C\u7BE9\u9078\u63A7\u5236\u65E5\u8A8C\u8A0A\u606F. \u82E5\u95DC\u9589, \u5247\u4E0D\u6703\u7522\u751F\u65E5\u8A8C\u8A0A\u606F.

DiagnosticsMBeanMetricsEnabledAttributeDescription=JDBC \u9A45\u52D5\u7A0B\u5F0F\u64F7\u53D6\u7684\u6240\u6709\u4E8B\u4EF6\u5EA6\u91CF\u90FD\u662F\u7531\u6B64\u5E03\u6797\u503C\u5C6C\u6027\u63A7\u5236. \u9810\u8A2D\u70BA false. \u82E5\u555F\u7528, \u5C07\u6703\u7531\u9A45\u52D5\u7A0B\u5F0F\u64F7\u53D6\u4E8B\u4EF6\u5EA6\u91CF, \u76F4\u5230\u505C\u7528\u70BA\u6B62.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=\u82E5\u555F\u7528\u5C07\u65E5\u8A8C\u5BEB\u5165\u8A18\u61B6\u9AD4\u5F0F\u8FFD\u8E64\u7DE9\u885D\u5340, \u6703\u50B3\u56DE true. \u9810\u8A2D\u503C\u70BA false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u82E5\u505C\u7528, \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u505C\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u82E5\u505C\u7528, \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u505C\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u82E5\u505C\u7528, \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u6B64\u4F5C\u696D\u5C07\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u505C\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u82E5\u505C\u7528, \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=\u9810\u8A2D\u6703\u555F\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D. \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u6B64\u4F5C\u696D\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u555F\u7528\u65E5\u8A8C\u8A18\u9304.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u9032\u884C\u65E5\u8A8C\u8A18\u9304.

DiagnosticsEnableLoggingByTenantNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u6B64\u4F5C\u696D\u6703\u555F\u7528\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u9032\u884C\u65E5\u8A8C\u8A18\u9304.

DiagnosticsDisableLoggingByTenantNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u9032\u884C\u65E5\u8A8C\u8A18\u9304.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u9032\u884C\u65E5\u8A8C\u8A18\u9304.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u9032\u884C\u65E5\u8A8C\u8A18\u9304.

DiagnosticsEnableLoggingOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u65E5\u8A8C\u8A18\u9304.

DiagnosticsDisableLoggingOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u65E5\u8A8C\u8A18\u9304. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u65E5\u8A8C\u8A18\u9304.

DiagnosticsEnableMetricsOperationDescription=\u958B\u59CB\u6536\u96C6\u9023\u7DDA\u671F\u9593\u7684\u6642\u9593\u5EA6\u91CF.

DiagnosticsDisableMetricsOperationDescription=\u505C\u6B62\u6536\u96C6\u9023\u7DDA\u671F\u9593\u7684\u6642\u9593\u5EA6\u91CF.

DiagnosticsShowMetricsOperationDescription=\u986F\u793A\u9023\u7DDA\u671F\u9593\u6240\u6536\u96C6\u7684\u6642\u9593\u5EA6\u91CF.

DiagnosticsClearMetricsOperationDescription=\u6E05\u9664\u9023\u7DDA\u671F\u9593\u6240\u6536\u96C6\u7684\u6642\u9593\u5EA6\u91CF.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u9032\u884C\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u6B64\u4F5C\u696D\u5C07\u555F\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=\u9810\u8A2D\u6703\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7. \u82E5\u555F\u7528, \u6B64\u4F5C\u696D\u5C07\u505C\u7528\u6A5F\u5BC6\u8CC7\u6599\u8A3A\u65B7.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u66F4\u65B0\u8A3A\u65B7\u5C64\u7D1A. \u5F15\u6578\u5B57\u4E32\u53EF\u80FD\u5305\u542B\u5C64\u7D1A\u540D\u7A31\u6216\u6574\u6578\u503C. \u4F8B\u5982: "SEVERE" \u6216 "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u66F4\u65B0\u8A3A\u65B7\u5C64\u7D1A. \u5F15\u6578\u5B57\u4E32\u53EF\u80FD\u5305\u542B\u5C64\u7D1A\u540D\u7A31\u6216\u6574\u6578\u503C. \u4F8B\u5982: "SEVERE" \u6216 "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u66F4\u65B0\u8A3A\u65B7\u5C64\u7D1A. \u5F15\u6578\u5B57\u4E32\u53EF\u80FD\u5305\u542B\u5C64\u7D1A\u540D\u7A31\u6216\u6574\u6578\u503C. \u4F8B\u5982: "SEVERE" \u6216 "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=\u66F4\u65B0\u8A3A\u65B7\u5C64\u7D1A. \u5F15\u6578\u5B57\u4E32\u53EF\u80FD\u5305\u542B\u5C64\u7D1A\u540D\u7A31\u6216\u6574\u6578\u503C. \u4F8B\u5982: "SEVERE" \u6216 "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=\u4F9D\u6307\u5B9A\u7684\u9023\u7DDA ID \u524D\u7F6E\u78BC\u66F4\u65B0\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u7DE9\u885D\u5340\u5927\u5C0F. \u9810\u8A2D\u7DE9\u885D\u5340\u5927\u5C0F\u70BA 4000 \u7B46\u65E5\u8A8C\u8A18\u9304.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=\u4F9D\u6307\u5B9A\u7684\u79DF\u7528\u6236\u540D\u7A31\u66F4\u65B0\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u7DE9\u885D\u5340\u5927\u5C0F. \u9810\u8A2D\u7DE9\u885D\u5340\u5927\u5C0F\u70BA 4000 \u7B46\u65E5\u8A8C\u8A18\u9304.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=\u4F9D\u6307\u5B9A\u7684\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31\u66F4\u65B0\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u7DE9\u885D\u5340\u5927\u5C0F. \u9810\u8A2D\u7DE9\u885D\u5340\u5927\u5C0F\u70BA 4000 \u7B46\u65E5\u8A8C\u8A18\u9304.

DiagnosticsUpdateBufferSizeOperationDescription=\u66F4\u65B0\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u7DE9\u885D\u5340\u5927\u5C0F. \u9810\u8A2D\u7DE9\u885D\u5340\u5927\u5C0F\u70BA 4000 \u7B46\u65E5\u8A8C\u8A18\u9304.

DiagnosticsReadLoggingConfigFileOperationDescription=\u91CD\u65B0\u521D\u59CB\u5316\u65E5\u8A8C\u8A18\u9304\u7279\u6027, \u4E26\u4E14\u5F9E\u6307\u5B9A\u7684\u6A94\u6848\u91CD\u65B0\u8B80\u53D6\u65E5\u8A8C\u8A18\u9304\u7D44\u614B, \u8A72\u6A94\u6848\u7684\u683C\u5F0F\u61C9\u70BA java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=\u65B0\u589E\u4E0B\u6B21\u767C\u751F\u932F\u8AA4\u6642\u8981\u76E3\u770B\u7684\u932F\u8AA4\u4EE3\u78BC (\u683C\u5F0F\u70BA ORA-XXXXX). \u767C\u751F\u76E3\u770B\u6E05\u55AE\u4E2D\u7684\u932F\u8AA4\u6642, JDBC \u9A45\u52D5\u7A0B\u5F0F\u6703\u5C07\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u5BEB\u5165\u8A2D\u5B9A\u7684\u65E5\u8A8C\u8655\u7406\u7A0B\u5F0F.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=\u5F9E\u76E3\u770B\u6E05\u55AE\u4E2D\u79FB\u9664\u6307\u5B9A\u7684\u932F\u8AA4\u4EE3\u78BC. \u932F\u8AA4\u4EE3\u78BC\u7684\u683C\u5F0F\u61C9\u70BA ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=\u986F\u793A\u6B63\u5728\u76E3\u770B\u7684\u932F\u8AA4\u4EE3\u78BC.

DiagnosticsResetErrorCodesWatchListOperationDescription=\u4EE5\u9810\u8A2D\u932F\u8AA4\u4EE3\u78BC\u8A2D\u5B9A\u76E3\u770B\u6E05\u55AE.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=\u5C07\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u50BE\u5370\u81F3\u76EE\u6A19\u8655\u7406\u7A0B\u5F0F.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=\u672A\u4F86\u767C\u751F\u7684\u7570\u5E38\u72C0\u6CC1\u82E5\u5305\u542B\u5176\u4E2D\u4E00\u9805\u6307\u5B9A\u7684\u95DC\u9375\u5B57, \u5247\u50BE\u5370\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7. \u7BC4\u4F8B\u95DC\u9375\u5B57\u70BA reset \u548C violation.

DiagnosticsShowExceptionKeywords=\u5148\u524D\u65B0\u589E\u7684\u7570\u5E38\u72C0\u6CC1\u95DC\u9375\u5B57.

DiagnosticsShowRecentOperations=\u4F7F\u7528\u8005\u6700\u8FD1\u5728\u6B64 MBean \u4E0A\u57F7\u884C\u7684\u4F5C\u696D.

DiagnosticsClearExceptionKeywordsOperationDescription=\u6E05\u9664\u5148\u524D\u65B0\u589E\u7684\u95DC\u9375\u5B57.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=\u65E5\u8A8C\u9810\u8A2D\u6703\u5BEB\u5165\u65E5\u8A8C\u8655\u7406\u7A0B\u5F0F. \u6B64\u4F5C\u696D\u6703\u555F\u7528\u6216\u505C\u7528\u5C07\u65E5\u8A8C\u5BEB\u5165\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7.

DiagnosticsConnectionIdPrefixParameterDescription=\u9023\u7DDA ID \u524D\u7F6E\u78BC.

DiagnosticsTenantNameParameterDescription=\u79DF\u7528\u6236\u540D\u7A31.

DiagnosticsLoggerNameParameterDescription=\u65E5\u8A8C\u8A18\u9304\u5668\u540D\u7A31.

DiagnosticsLevelParameterDescription=\u65E5\u8A8C\u8A18\u9304\u5668\u5C64\u7D1A.

DiagnosticsBufferSizeParameterDescription=\u300C\u8A3A\u65B7\u7B2C\u4E00\u6B21\u5931\u6557\u300D\u8A3A\u65B7\u7DE9\u885D\u5340\u5927\u5C0F.

DiagnosticsConfigFileParameterDescription=\u65E5\u8A8C\u8A18\u9304\u7D44\u614B\u6A94.

DiagnosticsErrorCodesParameterDescription=\u932F\u8AA4\u4EE3\u78BC, \u683C\u5F0F\u70BA ORA-XXXXX.

DiagnosticsEnabledParameterDescription=true \u6216 false \u503C.

DiagnosticsCommaSeparatedKeywordsParameterDescription=\u95DC\u9375\u5B57, \u4EE5\u9017\u865F\u5340\u9694\u7684\u503C.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






