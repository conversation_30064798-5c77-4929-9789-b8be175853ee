#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controla las funciones de diagn\u00F3stico de los controladores JDBC de Oracle.

DiagnosabilityMBeanConstructor()=Constructor de argumento cero para el MBean de diagn\u00F3stico de Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Este atributo booleano controla todo el c\u00F3digo de registro de Oracle JDBC. Si es false, no se producir\u00E1 ning\u00FAn mensaje de log. Si es true, los niveles y los filtros de java.util.logging controlar\u00E1n los mensajes de log.

DiagnosabilityMBeanStateManageableDescription=Los controladores JDBC de Oracle no se pueden iniciar y parar. Siempre devuelven false.

DiagnosabilityMBeanStatisticsProviderDescription=Los controladores JDBC de Oracle no proporcionan estad\u00EDsticas mediante el MBean de diagn\u00F3stico.

DiagnosabilityMBeanTraceControllerDescription=Controlador de rastreo Clio.

DiagnosabilityMBeanSuspendDescription=Operaci\u00F3n de supensi\u00F3n Clio.

DiagnosabilityMBeanResumeDescription=Operaci\u00F3n de reanudaci\u00F3n Clio.

DiagnosabilityMBeanTraceDescription=Operaci\u00F3n de rastreo Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Activa el registro de archivos continuo y seguro. Hay que definir los filtros ServiceName y UserName para aplicarlos. Est\u00E1 desactivado por defecto.

DiagnosabilityMBeanDisableContinousLoggingDescription=Desactiva el registro de archivos continuo y seguro. Est\u00E1 desactivado por defecto.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Activa el primer diagn\u00F3stico de fallo. Hay que definir los filtros ServiceName y UserName para aplicarlos. Est\u00E1 activado por defecto.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Desactiva el primer diagn\u00F3stico de fallo. Est\u00E1 activado por defecto.

DiagnosabilityMBeanServiceNameFilterDescription=Si se define el filtro ServiceName, el registro en memoria/continuo se activa solo para las conexiones del servicio configurado.

DiagnosabilityMBeanUserFilterDescription=Si se define el filtro de usuario, el registro en memoria/continuo se activa solo para las conexiones del usuario configurado.

ReplayStatisticsMBeanDescription=Muestra las estad\u00EDsticas de la funci\u00F3n Continuidad de la aplicaci\u00F3n (AC).

ReplayStatisticsMBeanConstructor=Constructor de argumento cero para el MBean de estad\u00EDstica AC de Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Obtiene las estad\u00EDsticas de AC en todas las instancias conocidas del origen de datos del controlador.

ReplayStatisticsMBeanGetDSStatisticsDescription=Obtiene las estad\u00EDsticas de AC para una instancia concreta del origen de datos del controlador.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controla las funciones de diagn\u00F3stico de los controladores JDBC de Oracle.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Este atributo booleano controla todo el c\u00F3digo de registro de Oracle JDBC. Su valor por defecto es False. Si est\u00E1 activado, los niveles y los filtros de java.util.logging controlar\u00E1n los mensajes de log. Si est\u00E1 desactivado, no se producir\u00E1 ning\u00FAn mensaje de log.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Todas las m\u00E9tricas de eventos capturadas por el controlador JDBC las controla este atributo booleano. Su valor por defecto es False. Si est\u00E1 activado, el controlador captura las m\u00E9tricas de los eventos hasta que se desactiven.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Devuelve True si la escritura de logs del buffer de rastreo en memoria est\u00E1 activada. Su valor por defecto es False.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n activa el primer diagn\u00F3stico de fallo (si est\u00E1 desactivado) por el prefijo de ID de conexi\u00F3n especificado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n desactiva el primer diagn\u00F3stico de fallo por el prefijo de ID de conexi\u00F3n especificado.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n activa el primer diagn\u00F3stico de fallo (si est\u00E1 desactivado) por el nombre de inquilino especificado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n desactiva el primer diagn\u00F3stico de fallo por el nombre de inquilino especificado.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n activa el primer diagn\u00F3stico de fallo (si est\u00E1 desactivado) por el nombre de registrador especificado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n desactiva el primer diagn\u00F3stico de fallo por el nombre de registrador especificado.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n activa el primer diagn\u00F3stico de fallo (si est\u00E1 desactivado).

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=El primer diagn\u00F3stico de fallo est\u00E1 activado por defecto. Esta operaci\u00F3n desactiva el primer diagn\u00F3stico de fallo.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n activa el registro mediante el prefijo del ID de conexi\u00F3n proporcionado.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n desactiva el registro (si est\u00E1 activado) por el prefijo del ID de conexi\u00F3n proporcionado.

DiagnosticsEnableLoggingByTenantNameOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n activa el registro por el nombre de inquilino proporcionado.

DiagnosticsDisableLoggingByTenantNameOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n desactiva el registro (si est\u00E1 activado) por el nombre de inquilino.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n activa el registro por el nombre de registrador proporcionado.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n desactiva el registro (si est\u00E1 activado) por el nombre de registrador.

DiagnosticsEnableLoggingOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n activa el registro.

DiagnosticsDisableLoggingOperationDescription=El registro est\u00E1 desactivado por defecto. Esta operaci\u00F3n desactiva el registro si est\u00E1 activado.

DiagnosticsEnableMetricsOperationDescription=Comienza a recopilar las m\u00E9tricas de temporizaci\u00F3n durante el tiempo de conexi\u00F3n.

DiagnosticsDisableMetricsOperationDescription=Para la recopilaci\u00F3n de las m\u00E9tricas de temporizaci\u00F3n durante el tiempo de conexi\u00F3n.

DiagnosticsShowMetricsOperationDescription=Muestra las m\u00E9tricas de temporizaci\u00F3n recopiladas durante el tiempo de conexi\u00F3n.

DiagnosticsClearMetricsOperationDescription=Borra las m\u00E9tricas de temporizaci\u00F3n recopiladas durante el tiempo de conexi\u00F3n.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n activa los diagn\u00F3sticos con datos confidenciales por el prefijo del ID de conexi\u00F3n dada proporcionado.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n desactiva los diagn\u00F3sticos con datos confidenciales (si est\u00E1n activados) por el prefijo del ID de conexi\u00F3n dada proporcionado.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n activa los diagn\u00F3sticos con datos confidenciales por el nombre de inquilino proporcionado.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n desactiva los diagn\u00F3sticos con datos confidenciales (si est\u00E1n activados) por el nombre de inquilino proporcionado.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n activa los diagn\u00F3sticos con datos confidenciales por el nombre de registrador proporcionado.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n desactiva los diagn\u00F3sticos con datos confidenciales (si est\u00E1n activados) por el nombre de registrador proporcionado.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n activa los diagn\u00F3sticos con datos confidenciales.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Los diagn\u00F3sticos con datos confidenciales est\u00E1n desactivados por defecto. Esta operaci\u00F3n desactiva los diagn\u00F3sticos con datos confidenciales si est\u00E1n activados.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Actualice el nivel de diagn\u00F3stico por el prefijo del ID de conexi\u00F3n proporcionado. La cadena de argumento puede estar formada por un nombre de nivel o un valor entero. Por ejemplo: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Actualice el nivel de diagn\u00F3stico por el nombre de inquilino proporcionado. La cadena de argumento puede estar formada por un nombre de nivel o un valor entero. Por ejemplo: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Actualice el nivel de diagn\u00F3stico por el nombre de registrador proporcionado. La cadena de argumento puede estar formada por un nombre de nivel o un valor entero. Por ejemplo: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Actualice el nivel de diagn\u00F3stico. La cadena de argumento puede estar formada por un nombre de nivel o un valor entero. Por ejemplo: "SEVERE" o "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Actualice el tama\u00F1o del buffer de primer diagn\u00F3stico de fallo por el prefijo del ID de conexi\u00F3n especificado. El tama\u00F1o de buffer por defecto es de 4000 registros de log.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Actualice el tama\u00F1o del buffer de primer diagn\u00F3stico de fallo por el nombre de inquilino especificado. El tama\u00F1o de buffer por defecto es de 4000 registros de log.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Actualice el tama\u00F1o del buffer de primer diagn\u00F3stico de fallo por el nombre de registrador especificado. El tama\u00F1o de buffer por defecto es de 4000 registros de log.

DiagnosticsUpdateBufferSizeOperationDescription=Actualice el tama\u00F1o del buffer de primer diagn\u00F3stico de fallo. El tama\u00F1o de buffer por defecto es de 4000 registros de log.

DiagnosticsReadLoggingConfigFileOperationDescription=Reinicie las propiedades de registro y vuelva a leer la configuraci\u00F3n de registro del archivo proporcionado, que debe estar en formato java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Agregue el c\u00F3digo de error que se debe comprobar en la siguiente ocurrencia con el formato ORA-XXXXX. El controlador JDBC escribe el primer diagn\u00F3stico de fallo en el manejador de log configurado cuando se produce un error en la lista de comprobaciones.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Elimine el c\u00F3digo de error proporcionado de la lista de comprobaciones. El c\u00F3digo de error debe tener el formato ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Muestra los c\u00F3digos de error que se est\u00E1n comprobando.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configure la lista de comprobaciones con los c\u00F3digos de error por defecto.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Volcado del primer diagn\u00F3stico de fallo en el manejador de destino.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Vuelca el primer diagn\u00F3stico de fallo cuando la excepci\u00F3n futura contenga una de las palabras clave especificadas. Las palabras clave de ejemplo son restablecimiento, infracci\u00F3n.

DiagnosticsShowExceptionKeywords=Las palabras clave de excepci\u00F3n se han agregado anteriormente.

DiagnosticsShowRecentOperations=Operaciones m\u00E1s recientes realizadas por el usuario en este MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Borre las palabras clave agregadas anteriormente.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Por defecto, los logs se escriben en el manejador de logs. Esta operaci\u00F3n puede activar o desactivar la escritura de los logs en el primer diagn\u00F3stico de fallo.

DiagnosticsConnectionIdPrefixParameterDescription=Prefijo del ID de conexi\u00F3n.

DiagnosticsTenantNameParameterDescription=Nombre de inquilino.

DiagnosticsLoggerNameParameterDescription=Nombre de registrador.

DiagnosticsLevelParameterDescription=Nivel de registrador.

DiagnosticsBufferSizeParameterDescription=Tama\u00F1o de buffer del primer diagn\u00F3stico de fallo.

DiagnosticsConfigFileParameterDescription=Archivo de configuraci\u00F3n de registro.

DiagnosticsErrorCodesParameterDescription=C\u00F3digo de error con formato ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Valor True o False.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Las palabras clave son valores separados por comas.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






