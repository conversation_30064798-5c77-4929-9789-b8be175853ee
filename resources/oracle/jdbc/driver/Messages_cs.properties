#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Ur\u010Duje funkce prov\u00E1d\u011Bn\u00ED diagnostiky ovlada\u010D\u016F aplikace Oracle JDBC.

DiagnosabilityMBeanConstructor()=Konstruktor komponenty MBean pro prov\u00E1d\u011Bn\u00ED diagnostiky aplikace Oracle JDBC s nulov\u00FDm po\u010Dtem argument\u016F

DiagnosabilityMBeanLoggingEnabledDescription=Ve\u0161ker\u00FD k\u00F3d protokolov\u00E1n\u00ED aplikace Oracle JDBC se \u0159\u00EDd\u00ED t\u00EDmto booleovsk\u00FDm atributem. Pokud m\u00E1 hodnotu false, nebudou vytvo\u0159eny \u017E\u00E1dn\u00E9 zpr\u00E1vy protokolu, pokud m\u00E1 hodnotu true, budou zpr\u00E1vy protokolu \u0159\u00EDzeny \u00FArovn\u011Bmi a filtry java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Ovlada\u010De aplikace Oracle JDBC nelze spustit a zastavit. V\u017Edy bude vr\u00E1cena hodnota false.

DiagnosabilityMBeanStatisticsProviderDescription=Ovlada\u010De Oracle JDBC neposkytuj\u00ED statistiku prost\u0159ednictv\u00EDm komponenty MBean pro prov\u00E1d\u011Bn\u00ED diagnostiky.

DiagnosabilityMBeanTraceControllerDescription=Clio - ovlada\u010D trasov\u00E1n\u00ED.

DiagnosabilityMBeanSuspendDescription=Clio - operace pozastaven\u00ED.

DiagnosabilityMBeanResumeDescription=Clio - operace pokra\u010Dov\u00E1n\u00ED.

DiagnosabilityMBeanTraceDescription=Clio - operace trasov\u00E1n\u00ED.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktivuje zabezpe\u010Den\u00E9 souvisl\u00E9 protokolov\u00E1n\u00ED soubor\u016F. Filtry ServiceName a UserName se daj\u00ED pou\u017E\u00EDt, jsou-li nastaveny. Ve v\u00FDchoz\u00EDm nastaven\u00ED je deaktivov\u00E1no.

DiagnosabilityMBeanDisableContinousLoggingDescription=Deaktivuje zabezpe\u010Den\u00E9 souvisl\u00E9 protokolov\u00E1n\u00ED soubor\u016F. Ve v\u00FDchoz\u00EDm nastaven\u00ED je deaktivov\u00E1no.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED. Pokud jsou nastaveny filtry ServiceName a UserName, lze je pou\u017E\u00EDt. P\u0159i v\u00FDchoz\u00EDm nastaven\u00ED je funkce aktivov\u00E1na.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Deaktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED. P\u0159i v\u00FDchoz\u00EDm nastaven\u00ED je funkce aktivov\u00E1na.

DiagnosabilityMBeanServiceNameFilterDescription=Jestli\u017Ee je nastaven filtr ServiceName, je trasov\u00E1n\u00ED v pam\u011Bti / souvisl\u00E9 protokolov\u00E1n\u00ED aktivov\u00E1no jen u p\u0159ipojen\u00ED konfigurovan\u00E9 slu\u017Eby.

DiagnosabilityMBeanUserFilterDescription=Jestli\u017Ee je nastaven filtr UserName, je trasov\u00E1n\u00ED v pam\u011Bti / souvisl\u00E9 protokolov\u00E1n\u00ED aktivov\u00E1no jen u p\u0159ipojen\u00ED konfigurovan\u00E9 slu\u017Eby.

ReplayStatisticsMBeanDescription=Zp\u0159\u00EDstupn\u00ED statistiku funkce Kontinuita aplikace (AC).

ReplayStatisticsMBeanConstructor=Konstruktor komponenty MBean pro prov\u00E1d\u011Bn\u00ED statistiky AC aplikace Oracle JDBC s nulov\u00FDm po\u010Dtem argument\u016F

ReplayStatisticsMBeanAllStatisticsDescription=Z\u00EDsk\u00E1 statistiku AC ze v\u0161ech zn\u00E1m\u00FDch instanc\u00ED datov\u00E9ho zdroje ovlada\u010De.

ReplayStatisticsMBeanGetDSStatisticsDescription=Z\u00EDsk\u00E1 statistiku AC pro konkr\u00E9tn\u00ED instanci datov\u00E9ho zdroje ovlada\u010De.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Ur\u010Duje funkce prov\u00E1d\u011Bn\u00ED diagnostiky ovlada\u010D\u016F aplikace Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Ve\u0161ker\u00FD k\u00F3d protokolov\u00E1n\u00ED aplikace Oracle JDBC se \u0159\u00EDd\u00ED t\u00EDmto booleovsk\u00FDm atributem. V\u00FDchoz\u00ED hodnota je false. Pokud je tato funkce zapnut\u00E1, budou zpr\u00E1vy protokolu \u0159\u00EDzeny \u00FArovn\u011Bmi a filtry java.util.logging. Pokud je vypnut\u00E1, nebudou se vytv\u00E1\u0159et \u017E\u00E1dn\u00E9 zpr\u00E1vy protokolu.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Tento booleovsk\u00FD atribut \u0159\u00EDd\u00ED v\u0161echny metriky ud\u00E1lost\u00ED zachyt\u00E1van\u00E9 ovlada\u010Dem JDBC. V\u00FDchoz\u00ED hodnota je false. Pokud je funkce aktivov\u00E1na, ovlada\u010D bude zachyt\u00E1vat metriky ud\u00E1lost\u00ED, dokud nebude deaktivov\u00E1na.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Vrac\u00ED hodnotu true, pokud je povolen z\u00E1pis protokol\u016F do vyrovn\u00E1vac\u00ED pam\u011Bti trasov\u00E1n\u00ED v pam\u011Bti. V\u00FDchoz\u00ED hodnota je false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace aktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED, pokud byla deaktivov\u00E1na.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace deaktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace aktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu z\u00E1kazn\u00EDka, pokud byla deaktivov\u00E1na.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace deaktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu z\u00E1kazn\u00EDka.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace aktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu protokolovac\u00EDho n\u00E1stroje, pokud byla deaktivov\u00E1na.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace deaktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu protokolovac\u00EDho n\u00E1stroje.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace aktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED, pokud byla deaktivov\u00E1na.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED aktivov\u00E1na. Tato operace deaktivuje funkci Diagnostikovat prvn\u00ED selh\u00E1n\u00ED.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace aktivuje protokolov\u00E1n\u00ED podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace deaktivuje protokolov\u00E1n\u00ED podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED, pokud bylo aktivov\u00E1no.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace aktivuje protokolov\u00E1n\u00ED podle dan\u00E9ho jm\u00E9na klientu.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace deaktivuje protokolov\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu klientu, pokud bylo aktivov\u00E1no.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace aktivuje protokolov\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu slu\u017Eby protokolov\u00E1n\u00ED logger.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace deaktivuje protokolov\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu slu\u017Eby protokolov\u00E1n\u00ED logger, pokud byla aktivov\u00E1na.

DiagnosticsEnableLoggingOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace aktivuje protokolov\u00E1n\u00ED.

DiagnosticsDisableLoggingOperationDescription=Protokolov\u00E1n\u00ED je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuto. Tato operace deaktivuje protokolov\u00E1n\u00ED, pokud bylo aktivov\u00E1no.

DiagnosticsEnableMetricsOperationDescription=Spus\u0165te shroma\u017E\u010Fov\u00E1n\u00ED metrik \u010Dasov\u00E1n\u00ED b\u011Bhem doby p\u0159ipojen\u00ED.

DiagnosticsDisableMetricsOperationDescription=Zastavte shroma\u017E\u010Fov\u00E1n\u00ED metrik \u010Dasov\u00E1n\u00ED b\u011Bhem doby p\u0159ipojen\u00ED.

DiagnosticsShowMetricsOperationDescription=Zobrazte metriky \u010Dasov\u00E1n\u00ED shrom\u00E1\u017Ed\u011Bn\u00E9 b\u011Bhem doby p\u0159ipojen\u00ED.

DiagnosticsClearMetricsOperationDescription=Vyma\u017Ete metriky \u010Dasov\u00E1n\u00ED shrom\u00E1\u017Ed\u011Bn\u00E9 b\u011Bhem doby p\u0159ipojen\u00ED.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace aktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace deaktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED, pokud byla aktivov\u00E1na.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace aktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9ho jm\u00E9na klientu.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace deaktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9ho jm\u00E9na klientu, pokud byla aktivov\u00E1na.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace aktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9ho n\u00E1zvu slu\u017Eby protokolov\u00E1n\u00ED logger.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace deaktivuje diagnostiku citliv\u00FDch dat podle dan\u00E9ho n\u00E1zvu slu\u017Eby protokolov\u00E1n\u00ED logger, pokud byla aktivov\u00E1na.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace aktivuje diagnostiku citliv\u00FDch dat.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Diagnostika citliv\u00FDch dat je ve v\u00FDchoz\u00EDm nastaven\u00ED vypnuta. Tato operace deaktivuje diagnostiku citliv\u00FDch dat, pokud byla aktivov\u00E1na.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Aktualizuje \u00FArove\u0148 diagnostiky podle zadan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED. \u0158et\u011Bzec argumentu se m\u016F\u017Ee skl\u00E1dat bu\u010F z\u00A0n\u00E1zvu \u00FArovn\u011B, nebo z\u00A0celo\u010D\u00EDseln\u00E9 hodnoty. Nap\u0159\u00EDklad: "SEVERE" nebo "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Aktualizuje \u00FArove\u0148 diagnostiky podle zadan\u00E9ho n\u00E1zvu z\u00E1kazn\u00EDka. \u0158et\u011Bzec argumentu se m\u016F\u017Ee skl\u00E1dat bu\u010F z\u00A0n\u00E1zvu \u00FArovn\u011B, nebo z\u00A0celo\u010D\u00EDseln\u00E9 hodnoty. Nap\u0159\u00EDklad: "SEVERE" nebo "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Aktualizuje \u00FArove\u0148 diagnostiky podle zadan\u00E9ho n\u00E1zvu protokolovac\u00EDho n\u00E1stroje. \u0158et\u011Bzec argumentu se m\u016F\u017Ee skl\u00E1dat bu\u010F z\u00A0n\u00E1zvu \u00FArovn\u011B, nebo z\u00A0celo\u010D\u00EDseln\u00E9 hodnoty. Nap\u0159\u00EDklad: "SEVERE" nebo "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Aktualizuje \u00FArove\u0148 diagnostiky. \u0158et\u011Bzec argumentu se m\u016F\u017Ee skl\u00E1dat bu\u010F z\u00A0n\u00E1zvu \u00FArovn\u011B, nebo z\u00A0celo\u010D\u00EDseln\u00E9 hodnoty. Nap\u0159\u00EDklad: "SEVERE" nebo "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Aktualizujte velikost diagnostick\u00E9 vyrovn\u00E1vac\u00ED pam\u011Bti funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9 p\u0159edpony ID p\u0159ipojen\u00ED. V\u00FDchoz\u00ED velikost vyrovn\u00E1vac\u00ED pam\u011Bti je 4000 z\u00E1znam\u016F protokolu.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Aktualizujte velikost diagnostick\u00E9 vyrovn\u00E1vac\u00ED pam\u011Bti funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu z\u00E1kazn\u00EDka. V\u00FDchoz\u00ED velikost vyrovn\u00E1vac\u00ED pam\u011Bti je 4000 z\u00E1znam\u016F protokolu.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Aktualizujte velikost diagnostick\u00E9 vyrovn\u00E1vac\u00ED pam\u011Bti funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED podle dan\u00E9ho n\u00E1zvu protokolovac\u00EDho n\u00E1stroje. V\u00FDchoz\u00ED velikost vyrovn\u00E1vac\u00ED pam\u011Bti je 4000 z\u00E1znam\u016F protokolu.

DiagnosticsUpdateBufferSizeOperationDescription=Aktualizujte velikost diagnostick\u00E9 vyrovn\u00E1vac\u00ED pam\u011Bti funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED. V\u00FDchoz\u00ED velikost vyrovn\u00E1vac\u00ED pam\u011Bti je 4000 z\u00E1znam\u016F protokolu.

DiagnosticsReadLoggingConfigFileOperationDescription=Znovu inicializujte vlastnosti protokolov\u00E1n\u00ED a znovu na\u010Dt\u011Bte konfiguraci protokolov\u00E1n\u00ED z dan\u00E9ho souboru, kter\u00FD by m\u011Bl b\u00FDt ve form\u00E1tu java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=P\u0159idejte chybov\u00FD k\u00F3d, pro\u00A0kter\u00FD m\u00E1 b\u00FDt sledov\u00E1n p\u0159\u00ED\u0161t\u00ED v\u00FDskyt, a to ve form\u00E1tu ORA-XXXXX. Ovlada\u010D JDBC zap\u00ED\u0161e diagnostiku funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED do konfigurovan\u00E9ho deskriptoru protokolu, kdy\u017E dojde k\u00A0chyb\u011B uveden\u00E9 v\u00A0seznamu sledov\u00E1n\u00ED.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Odstra\u0148te dan\u00FD chybov\u00FD k\u00F3d ze seznamu sledovan\u00FDch. K\u00F3d chyby by m\u011Bl b\u00FDt ve form\u00E1tu ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Zobrazte sledovan\u00E9 chybov\u00E9 k\u00F3dy.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurujte seznam sledov\u00E1n\u00ED pomoc\u00ED v\u00FDchoz\u00EDch chybov\u00FDch k\u00F3d\u016F.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Prove\u010Fte v\u00FDpis diagnostiky funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED do c\u00EDlov\u00E9ho obslu\u017En\u00E9ho programu.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Pokud budouc\u00ED v\u00FDjimka obsahuje jedno z dan\u00FDch kl\u00ED\u010Dov\u00FDch slov, prove\u010Fte v\u00FDpis diagnostiky funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED. Vzorov\u00E1 kl\u00ED\u010Dov\u00E1 slova jsou reset, violation.

DiagnosticsShowExceptionKeywords=Kl\u00ED\u010Dov\u00E1 slova v\u00FDjimky p\u0159idan\u00E1 d\u0159\u00EDve.

DiagnosticsShowRecentOperations=Nejnov\u011Bj\u0161\u00ED operace proveden\u00E9 u\u017Eivatelem na tomto objektu MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Vyma\u017Ete d\u0159\u00EDve p\u0159idan\u00E1 kl\u00ED\u010Dov\u00E1 slova.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=P\u0159i v\u00FDchoz\u00EDm nastaven\u00ED jsou protokoly zapisov\u00E1ny do obslu\u017En\u00E9ho programu protokol\u016F. Tato operace aktivuje nebo deaktivuje zapisov\u00E1n\u00ED protokol\u016F do diagnostiky funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED.

DiagnosticsConnectionIdPrefixParameterDescription=P\u0159edpona ID p\u0159ipojen\u00ED.

DiagnosticsTenantNameParameterDescription=N\u00E1zev klientu.

DiagnosticsLoggerNameParameterDescription=N\u00E1zev slu\u017Eby protokolov\u00E1n\u00ED Logger.

DiagnosticsLevelParameterDescription=\u00DArove\u0148 slu\u017Eby protokolov\u00E1n\u00ED Logger.

DiagnosticsBufferSizeParameterDescription=Velikost diagnostick\u00E9 vyrovn\u00E1vac\u00ED pam\u011Bti funkce Diagnostikovat prvn\u00ED selh\u00E1n\u00ED.

DiagnosticsConfigFileParameterDescription=Soubor konfigurace protokolov\u00E1n\u00ED.

DiagnosticsErrorCodesParameterDescription=K\u00F3d chyby ve form\u00E1tu ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Hodnota true nebo false.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Kl\u00ED\u010Dov\u00E1 slova jako hodnoty odd\u011Blen\u00E9 \u010D\u00E1rkou.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






