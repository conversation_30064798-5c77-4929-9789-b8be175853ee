#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controlla le funzioni di diagnostica dei driver JDBC Oracle.

DiagnosabilityMBeanConstructor()=Il costruttore senza argomenti per Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=Tutto il codice di log JDBC \u00E8 controllato da questo attributo booleano. Se false, non verr\u00E0 prodotto alcun messaggio di log. Se true, i messaggi di log verranno controllati dai livelli e dai filtri di java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Impossibile avviare e arrestare i driver JDBC Oracle. Restituito sempre false.

DiagnosabilityMBeanStatisticsProviderDescription=I driver JDBC Oracle non forniscono statistiche mediante Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Controller di trace Clio.

DiagnosabilityMBeanSuspendDescription=Operazione di sospensione Clio.

DiagnosabilityMBeanResumeDescription=Operazione di ripresa Clio.

DiagnosabilityMBeanTraceDescription=Operazione di trace Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Abilita il log file continuo protetto. Se impostati, i filtri ServiceName e UserName possono essere applicati. Disabilitata per impostazione predefinita.

DiagnosabilityMBeanDisableContinousLoggingDescription=Disabilita il log file continuo protetto. Disabilitata per impostazione predefinita.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Abilita la diagnosi al primo errore. Se impostati, i filtri ServiceName e UserName possono essere applicati. Abilitata per impostazione predefinita.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Disabilita la diagnosi al primo errore. Abilitata per impostazione predefinita.

DiagnosabilityMBeanServiceNameFilterDescription=Se il filtro ServiceName \u00E8 impostato, il log in memoria/continuo \u00E8 abilitato solo per le connessioni del servizio configurato.

DiagnosabilityMBeanUserFilterDescription=Se il filtro UserName \u00E8 impostato, il log in memoria/continuo \u00E8 abilitato solo per le connessioni dell'utente configurato.

ReplayStatisticsMBeanDescription=Espone le statistiche della funzione di continuit\u00E0 dell'applicazione (AC).

ReplayStatisticsMBeanConstructor=Il costruttore senza argomenti per l'MBean delle statistiche AC Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Ottiene le statistiche AC in tutte le istanze note dell'origine dati del driver.

ReplayStatisticsMBeanGetDSStatisticsDescription=Ottiene le statistiche AC per una determinata istanza dell'origine dati del driver.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controlla le funzioni di diagnostica dei driver JDBC Oracle.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Tutto il codice di log Oracle JDBC \u00E8 controllato da questo attributo booleano. Il valore predefinito \u00E8 false. Se attivato, i messaggi di log verranno controllati dai livelli e dai filtri di java.util.logging. Se disattivato, non verr\u00E0 prodotto alcun messaggio di log.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Tutte le metriche degli eventi acquisite dal driver JDBC vengono controllate da questo attributo booleano. L'impostazione predefinita \u00E8 false. Se abilitato, le metriche degli eventi vengono acquisite dal driver fino alla disabilitazione.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Restituisce true se la scrittura dei log nel buffer di trace In-Memory \u00E8 abilitata. Il valore predefinito \u00E8 false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione abilita la diagnosi al primo errore in base al prefisso ID di connessione specificato se era stata disabilitata.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione disabilita la diagnosi al primo errore in base al prefisso ID di connessione specificato.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione abilita la diagnosi al primo errore in base al nome tenant specificato se era stata disabilitata.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione disabilita la diagnosi al primo errore in base al nome tenant specificato.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione abilita la diagnosi al primo errore in base al nome logger specificato se era stata disabilitata.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione disabilita la diagnosi al primo errore in base al nome logger specificato.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione abilita la diagnosi al primo errore se era stata disabilitata.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=La diagnosi al primo errore \u00E8 abilitata per impostazione predefinita. Questa operazione disabilita la diagnosi al primo errore.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione abilita il log in base al prefisso ID connessione specificato.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione disabilita il log in base al prefisso ID connessione specificato se era stato abilitato.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione abilita il log in base al nome tenant specificato.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione disabilita il log in base al nome tenant specificato se era stato abilitato.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione abilita il log in base al nome logger specificato.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione disabilita il log in base al nome logger specificato se era stato abilitato.

DiagnosticsEnableLoggingOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione abilita il log.

DiagnosticsDisableLoggingOperationDescription=Il log \u00E8 disabilitato per impostazione predefinita. Questa operazione disabilita il log se era stato abilitato.

DiagnosticsEnableMetricsOperationDescription=Avvia la raccolta delle metriche di tempificazione durante il tempo di connessione.

DiagnosticsDisableMetricsOperationDescription=Arresta la raccolta delle metriche di tempificazione durante il tempo di connessione.

DiagnosticsShowMetricsOperationDescription=Mostra le metriche di tempificazione raccolte durante il tempo di connessione.

DiagnosticsClearMetricsOperationDescription=Cancella le metriche di tempificazione raccolte durante il tempo di connessione.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione abilita la diagnostica con l'inclusione di informazioni riservate in base al prefisso ID connessione specificato.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione disabilita la diagnostica con l'inclusione di informazioni riservate in base al prefisso ID connessione specificato se era stata abilitata.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione abilita la diagnostica con l'inclusione di informazioni riservate in base al nome tenant specificato.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione disabilita la diagnostica con l'inclusione di informazioni riservate in base al nome tenant specificato se era stata abilitata.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione abilita la diagnostica con l'inclusione di informazioni riservate in base al nome logger specificato.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione disabilita la diagnostica con l'inclusione di informazioni riservate in base al nome logger specificato se era stata abilitata.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione abilita la diagnostica con l'inclusione di informazioni riservate.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=La diagnostica con l'inclusione di informazioni riservate \u00E8 disabilitata per impostazione predefinita. Questa operazione disabilita la diagnostica con l'inclusione di informazioni riservate se era stata abilitata.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Aggiorna il livello di diagnostica in base al prefisso ID connessione specificato. La stringa dell'argomento pu\u00F2 essere composta da un nome livello o da un valore intero, ad esempio: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Aggiorna il livello di diagnostica in base al nome tenant specificato. La stringa dell'argomento pu\u00F2 essere composta da un nome livello o da un valore intero, ad esempio: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Aggiorna il livello di diagnostica in base al nome logger specificato. La stringa dell'argomento pu\u00F2 essere composta da un nome livello o da un valore intero, ad esempio: "SEVERE" o "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Aggiorna il livello di diagnostica. La stringa dell'argomento pu\u00F2 essere composta da un nome livello o da un valore intero, ad esempio: "SEVERE" o "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Aggiorna la dimensione del buffer di diagnostica per la diagnosi al primo errore in base al prefisso ID connessione specificato. La dimensione predefinita del buffer \u00E8 di 4000 record di log.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Aggiorna la dimensione del buffer di diagnostica per la diagnosi al primo errore in base al nome tenant specificato. La dimensione predefinita del buffer \u00E8 di 4000 record di log.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Aggiorna la dimensione del buffer di diagnostica per la diagnosi al primo errore in base al nome logger specificato. La dimensione predefinita del buffer \u00E8 di 4000 record di log.

DiagnosticsUpdateBufferSizeOperationDescription=Aggiorna la dimensione del buffer di diagnostica per la diagnosi al primo errore. La dimensione predefinita del buffer \u00E8 di 4000 record di log.

DiagnosticsReadLoggingConfigFileOperationDescription=Reinizializza le propriet\u00E0 di log e rilegge la configurazione del log dal file specificato, che deve essere in formato java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Aggiunge il codice di errore da controllare alla ricorrenza successiva nel formato ORA-XXXXX. Il driver JDBC effettua la scrittura della diagnostica per la diagnosi al primo errore nell'handler di log configurato quando si verifica un errore incluso nella lista di controllo.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Rimuove il codice di errore specificato dall'elenco di controllo. Il codice di errore deve essere nel formato ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Mostra i codici di errore controllati.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configura l'elenco di controllo con i codici di errore predefiniti.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Esegue il dump della diagnostica per la diagnosi al primo errore nell'handler di destinazione.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Esegue il dump della diagnostica per la diagnosi al primo errore quando l'eccezione futura contiene una delle parole chiave fornite. Le parole chiave di esempio sono reset, violation.

DiagnosticsShowExceptionKeywords=Le parole chiave di eccezione aggiunte in precedenza.

DiagnosticsShowRecentOperations=Le operazioni pi\u00F9 recenti effettuate dall'utente su questo MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Cancella le parole chiave aggiunte in precedenza.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Per impostazione predefinita, i log vengono scritti nell'handler di log. Questa operazione abilita o disabilita la scrittura dei log relativi alla diagnostica per la diagnosi al primo errore.

DiagnosticsConnectionIdPrefixParameterDescription=Prefisso ID connessione.

DiagnosticsTenantNameParameterDescription=Nome tenant.

DiagnosticsLoggerNameParameterDescription=Nome logger.

DiagnosticsLevelParameterDescription=Livello di log.

DiagnosticsBufferSizeParameterDescription=Dimensione del buffer della diagnostica per la diagnosi al primo errore.

DiagnosticsConfigFileParameterDescription=File di configurazione del log.

DiagnosticsErrorCodesParameterDescription=Codice di errore nel formato ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Valore true o false.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Parole chiave come valori separati da virgola.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






