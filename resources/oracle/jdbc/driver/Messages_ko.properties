#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Oracle JDBC \uB4DC\uB77C\uC774\uBC84\uC758 \uC9C4\uB2E8 \uAE30\uB2A5\uC744 \uC81C\uC5B4\uD569\uB2C8\uB2E4.

DiagnosabilityMBeanConstructor()=Oracle JDBC \uC9C4\uB2E8 MBean\uC5D0 \uB300\uD55C \uC778\uC218 \uC5C6\uB294 \uC0DD\uC131\uC790

DiagnosabilityMBeanLoggingEnabledDescription=\uBAA8\uB4E0 Oracle JDBC \uB85C\uAE45 \uCF54\uB4DC\uB294 \uC774 \uBD80\uC6B8 \uC18D\uC131\uC5D0 \uC758\uD574 \uC81C\uC5B4\uB429\uB2C8\uB2E4. false\uC77C \uACBD\uC6B0 \uB85C\uADF8 \uBA54\uC2DC\uC9C0\uAC00 \uC0DD\uC131\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. true\uC77C \uACBD\uC6B0 \uB85C\uADF8 \uBA54\uC2DC\uC9C0\uAC00 java.util.logging \uB808\uBCA8 \uBC0F \uD544\uD130\uC5D0 \uC758\uD574 \uC81C\uC5B4\uB429\uB2C8\uB2E4.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC \uB4DC\uB77C\uC774\uBC84\uB97C \uC2DC\uC791\uD558\uACE0 \uC815\uC9C0\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uD56D\uC0C1 false\uB97C \uBC18\uD658\uD569\uB2C8\uB2E4.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC \uB4DC\uB77C\uC774\uBC84\uB294 \uC9C4\uB2E8 MBean\uC744 \uD1B5\uD574 \uD1B5\uACC4\uB97C \uC81C\uACF5\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.

DiagnosabilityMBeanTraceControllerDescription=Clio \uCD94\uC801 \uCEE8\uD2B8\uB864\uB7EC\uC785\uB2C8\uB2E4.

DiagnosabilityMBeanSuspendDescription=Clio \uC77C\uC2DC \uC911\uC9C0 \uC791\uC5C5\uC785\uB2C8\uB2E4.

DiagnosabilityMBeanResumeDescription=Clio \uC7AC\uAC1C \uC791\uC5C5\uC785\uB2C8\uB2E4.

DiagnosabilityMBeanTraceDescription=Clio \uCD94\uC801 \uC791\uC5C5\uC785\uB2C8\uB2E4.

DiagnosabilityMBeanEnableContinousLoggingDescription=\uBCF4\uC548 \uC5F0\uC18D \uD30C\uC77C \uB85C\uAE45\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4. ServiceName \uBC0F UserName \uD544\uD130(\uC124\uC815\uB41C \uACBD\uC6B0)\uB97C \uC801\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.

DiagnosabilityMBeanDisableContinousLoggingDescription=\uBCF4\uC548 \uC5F0\uC18D \uD30C\uC77C \uB85C\uAE45\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4. \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4. ServiceName \uBC0F UserName \uD544\uD130(\uC124\uC815\uB41C \uACBD\uC6B0)\uB97C \uC801\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4. \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.

DiagnosabilityMBeanServiceNameFilterDescription=ServiceName \uD544\uD130\uAC00 \uC124\uC815\uB41C \uACBD\uC6B0 \uAD6C\uC131\uB41C \uC11C\uBE44\uC2A4\uC758 \uC811\uC18D\uC5D0 \uB300\uD574\uC11C\uB9CC \uC778\uBA54\uBAA8\uB9AC/\uC5F0\uC18D \uB85C\uAE45\uC774 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4.

DiagnosabilityMBeanUserFilterDescription=\uC0AC\uC6A9\uC790 \uD544\uD130\uAC00 \uC124\uC815\uB41C \uACBD\uC6B0 \uAD6C\uC131\uB41C \uC0AC\uC6A9\uC790\uC758 \uC811\uC18D\uC5D0 \uB300\uD574\uC11C\uB9CC \uC778\uBA54\uBAA8\uB9AC/\uC5F0\uC18D \uB85C\uAE45\uC774 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4.

ReplayStatisticsMBeanDescription=\uC560\uD50C\uB9AC\uCF00\uC774\uC158 \uC5F0\uC18D\uC131(AC) \uAE30\uB2A5\uC758 \uD1B5\uACC4\uB97C \uB178\uCD9C\uD569\uB2C8\uB2E4.

ReplayStatisticsMBeanConstructor=Oracle JDBC AC \uD1B5\uACC4 MBean\uC5D0 \uB300\uD55C \uC778\uC218 \uC5C6\uB294 \uC0DD\uC131\uC790\uC785\uB2C8\uB2E4.

ReplayStatisticsMBeanAllStatisticsDescription=\uBAA8\uB4E0 \uC54C\uB824\uC9C4 \uB4DC\uB77C\uC774\uBC84 \uB370\uC774\uD130 \uC18C\uC2A4 \uC778\uC2A4\uD134\uC2A4\uC5D0\uC11C AC \uD1B5\uACC4\uB97C \uAC00\uC838\uC635\uB2C8\uB2E4.

ReplayStatisticsMBeanGetDSStatisticsDescription=\uD2B9\uC815 \uB4DC\uB77C\uC774\uBC84 \uB370\uC774\uD130 \uC18C\uC2A4 \uC778\uC2A4\uD134\uC2A4\uC5D0 \uB300\uD55C AC \uD1B5\uACC4\uB97C \uAC00\uC838\uC635\uB2C8\uB2E4.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Oracle JDBC \uB4DC\uB77C\uC774\uBC84\uC758 \uC9C4\uB2E8 \uAE30\uB2A5\uC744 \uC81C\uC5B4\uD569\uB2C8\uB2E4.

DiagnosticsMBeanLoggingEnabledAttributeDescription=\uBAA8\uB4E0 Oracle JDBC \uB85C\uAE45 \uCF54\uB4DC\uB294 \uC774 \uBD80\uC6B8 \uC18D\uC131\uC5D0 \uC758\uD574 \uC81C\uC5B4\uB429\uB2C8\uB2E4. \uAE30\uBCF8\uAC12\uC740 false\uC785\uB2C8\uB2E4. \uCF1C\uC838 \uC788\uC73C\uBA74 \uB85C\uADF8 \uBA54\uC2DC\uC9C0\uAC00 java.util.logging \uB808\uBCA8 \uBC0F \uD544\uD130\uC5D0 \uC758\uD574 \uC81C\uC5B4\uB429\uB2C8\uB2E4. \uAEBC\uC838 \uC788\uC73C\uBA74 \uB85C\uADF8 \uBA54\uC2DC\uC9C0\uAC00 \uC0DD\uC131\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.

DiagnosticsMBeanMetricsEnabledAttributeDescription=JDBC \uB4DC\uB77C\uC774\uBC84\uC5D0\uC11C \uCEA1\uCC98\uB41C \uC774\uBCA4\uD2B8\uC758 \uBAA8\uB4E0 \uCE21\uC815\uD56D\uBAA9\uC740 \uC774 \uBD80\uC6B8 \uC18D\uC131\uC5D0 \uC758\uD574 \uC81C\uC5B4\uB429\uB2C8\uB2E4. \uAE30\uBCF8\uAC12\uC740 false\uC785\uB2C8\uB2E4. \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0, \uC774\uBCA4\uD2B8 \uCE21\uC815\uD56D\uBAA9\uC740 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB420 \uB54C\uAE4C\uC9C0 \uB4DC\uB77C\uC774\uBC84\uC5D0\uC11C \uCEA1\uCC98\uB429\uB2C8\uB2E4.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=\uC778\uBA54\uBAA8\uB9AC \uCD94\uC801 \uBC84\uD37C\uC5D0 \uB85C\uADF8 \uC4F0\uAE30\uAC00 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 true\uB97C \uBC18\uD658\uD569\uB2C8\uB2E4. \uAE30\uBCF8\uAC12\uC740 false\uC785\uB2C8\uB2E4.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8"\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableLoggingByTenantNameOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableLoggingByTenantNameOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 \uB85C\uAE45\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableLoggingOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uB85C\uAE45\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableLoggingOperationDescription=\uB85C\uAE45\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uB85C\uAE45\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableMetricsOperationDescription=\uC811\uC18D \uC2DC\uAC04 \uB3D9\uC548 \uD0C0\uC774\uBC0D \uCE21\uC815\uD56D\uBAA9 \uC218\uC9D1\uC744 \uC2DC\uC791\uD569\uB2C8\uB2E4.

DiagnosticsDisableMetricsOperationDescription=\uC811\uC18D \uC2DC\uAC04 \uB3D9\uC548 \uD0C0\uC774\uBC0D \uCE21\uC815\uD56D\uBAA9 \uC218\uC9D1\uC744 \uC815\uC9C0\uD569\uB2C8\uB2E4.

DiagnosticsShowMetricsOperationDescription=\uC811\uC18D \uC2DC\uAC04 \uB3D9\uC548 \uC218\uC9D1\uB41C \uD0C0\uC774\uBC0D \uCE21\uC815\uD56D\uBAA9\uC744 \uBCF4\uC5EC\uC90D\uB2C8\uB2E4.

DiagnosticsClearMetricsOperationDescription=\uC811\uC18D \uC2DC\uAC04 \uB3D9\uC548 \uC218\uC9D1\uB41C \uD0C0\uC774\uBC0D \uCE21\uC815\uD56D\uBAA9\uC744 \uC9C0\uC6C1\uB2C8\uB2E4.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=\uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC740 \uAE30\uBCF8\uC801\uC73C\uB85C \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 \uC0AC\uC6A9\uC73C\uB85C \uC124\uC815\uB41C \uACBD\uC6B0 \uBBFC\uAC10\uD55C \uC9C4\uB2E8\uC744 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=\uC81C\uACF5\uB41C \uC811\uC18D ID \uC811\uB450\uC5B4\uB85C \uC9C4\uB2E8 \uB808\uBCA8\uC744 \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uC778\uC218 \uBB38\uC790\uC5F4\uC740 \uB808\uBCA8 \uC774\uB984 \uB610\uB294 \uC815\uC218 \uAC12\uC73C\uB85C \uAD6C\uC131\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4(\uC608: "SEVERE" \uB610\uB294 "1000").

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=\uC81C\uACF5\uB41C \uD14C\uB10C\uD2B8 \uC774\uB984\uC73C\uB85C \uC9C4\uB2E8 \uB808\uBCA8\uC744 \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uC778\uC218 \uBB38\uC790\uC5F4\uC740 \uB808\uBCA8 \uC774\uB984 \uB610\uB294 \uC815\uC218 \uAC12\uC73C\uB85C \uAD6C\uC131\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4(\uC608: "SEVERE" \uB610\uB294 "1000").

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=\uC81C\uACF5\uB41C \uB85C\uAC70 \uC774\uB984\uC73C\uB85C \uC9C4\uB2E8 \uB808\uBCA8\uC744 \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uC778\uC218 \uBB38\uC790\uC5F4\uC740 \uB808\uBCA8 \uC774\uB984 \uB610\uB294 \uC815\uC218 \uAC12\uC73C\uB85C \uAD6C\uC131\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4(\uC608: "SEVERE" \uB610\uB294 "1000").

DiagnosticsUpdateDiagnosticLevelOperationDescription=\uC9C4\uB2E8 \uB808\uBCA8\uC744 \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uC778\uC218 \uBB38\uC790\uC5F4\uC740 \uB808\uBCA8 \uC774\uB984 \uB610\uB294 \uC815\uC218 \uAC12\uC73C\uB85C \uAD6C\uC131\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4(\uC608: "SEVERE" \uB610\uB294 "1000").

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=\uC8FC\uC5B4\uC9C4 \uC811\uC18D ID \uC811\uB450\uC5B4\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8 \uBC84\uD37C \uD06C\uAE30\uB97C \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uAE30\uBCF8 \uBC84\uD37C \uD06C\uAE30\uB294 4000\uAC1C\uC758 \uB85C\uADF8 \uB808\uCF54\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=\uC8FC\uC5B4\uC9C4 \uD14C\uB10C\uD2B8 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8 \uBC84\uD37C \uD06C\uAE30\uB97C \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uAE30\uBCF8 \uBC84\uD37C \uD06C\uAE30\uB294 4000\uAC1C\uC758 \uB85C\uADF8 \uB808\uCF54\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=\uC8FC\uC5B4\uC9C4 \uB85C\uAC70 \uC774\uB984\uC5D0 \uC758\uD574 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8 \uBC84\uD37C \uD06C\uAE30\uB97C \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uAE30\uBCF8 \uBC84\uD37C \uD06C\uAE30\uB294 4000\uAC1C\uC758 \uB85C\uADF8 \uB808\uCF54\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsUpdateBufferSizeOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8 \uBC84\uD37C \uD06C\uAE30\uB97C \uC5C5\uB370\uC774\uD2B8\uD569\uB2C8\uB2E4. \uAE30\uBCF8 \uBC84\uD37C \uD06C\uAE30\uB294 4000\uAC1C\uC758 \uB85C\uADF8 \uB808\uCF54\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsReadLoggingConfigFileOperationDescription=\uB85C\uAE45 \uC18D\uC131\uC744 \uB2E4\uC2DC \uCD08\uAE30\uD654\uD558\uACE0 java.util.Properties \uD615\uC2DD\uC73C\uB85C \uC81C\uACF5\uB41C \uD30C\uC77C\uC5D0\uC11C \uB85C\uAE45 \uAD6C\uC131\uC744 \uB2E4\uC2DC \uC77D\uC2B5\uB2C8\uB2E4.

DiagnosticsAddErrorCodeToWatchListOperationDescription=\uB2E4\uC74C \uBC1C\uC0DD \uC2DC \uAC10\uC2DC\uD560 \uC624\uB958 \uCF54\uB4DC\uB97C ORA-XXXXX \uD615\uC2DD\uC73C\uB85C \uCD94\uAC00\uD569\uB2C8\uB2E4. JDBC \uB4DC\uB77C\uC774\uBC84\uB294 \uAC10\uC2DC \uBAA9\uB85D\uC758 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD560 \uB54C \uAD6C\uC131\uB41C \uB85C\uADF8 \uCC98\uB9AC\uAE30\uC5D0 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8\uC744 \uAE30\uB85D\uD569\uB2C8\uB2E4.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=\uC81C\uACF5\uB41C \uC624\uB958 \uCF54\uB4DC\uB97C \uAC10\uC2DC \uBAA9\uB85D\uC5D0\uC11C \uC81C\uAC70\uD569\uB2C8\uB2E4. \uC624\uB958 \uCF54\uB4DC\uB294 ORA-XXXXX \uD615\uC2DD\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.

DiagnosticsShowErrorCodesWatchListOperationDescription=\uAC10\uC2DC \uC911\uC778 \uC624\uB958 \uCF54\uB4DC\uB97C \uBCF4\uC5EC\uC90D\uB2C8\uB2E4.

DiagnosticsResetErrorCodesWatchListOperationDescription=\uAE30\uBCF8 \uC624\uB958 \uCF54\uB4DC\uB85C \uAC10\uC2DC \uBAA9\uB85D\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8\uC744 \uB300\uC0C1 \uCC98\uB9AC\uAE30\uB85C \uB364\uD504\uD569\uB2C8\uB2E4.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=\uC81C\uACF5\uB41C \uD0A4\uC6CC\uB4DC \uC911 \uD558\uB098\uAC00 \uBBF8\uB798 \uC608\uC678\uC0AC\uD56D\uC5D0 \uD3EC\uD568\uB418\uB294 \uACBD\uC6B0 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8\uC744 \uB364\uD504\uD569\uB2C8\uB2E4. reset, violation\uC744 \uD0A4\uC6CC\uB4DC \uC608\uB85C \uB4E4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.

DiagnosticsShowExceptionKeywords=\uC774\uC804\uC5D0 \uCD94\uAC00\uB41C \uC608\uC678\uC0AC\uD56D \uD0A4\uC6CC\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsShowRecentOperations=\uC774 MBean\uC5D0\uC11C \uC0AC\uC6A9\uC790\uAC00 \uAC00\uC7A5 \uCD5C\uADFC \uC218\uD589\uD55C \uC791\uC5C5\uC785\uB2C8\uB2E4.

DiagnosticsClearExceptionKeywordsOperationDescription=\uC774\uC804\uC5D0 \uCD94\uAC00\uB41C \uD0A4\uC6CC\uB4DC\uB97C \uC9C0\uC6C1\uB2C8\uB2E4.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=\uAE30\uBCF8\uC801\uC73C\uB85C \uB85C\uADF8\uB294 \uB85C\uADF8 \uCC98\uB9AC\uAE30\uC5D0 \uAE30\uB85D\uB429\uB2C8\uB2E4. \uC774 \uC791\uC5C5\uC740 "\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8\uC5D0 \uB85C\uADF8 \uC4F0\uAE30\uB97C \uC0AC\uC6A9 \uB610\uB294 \uC0AC\uC6A9 \uC548\uD568\uC73C\uB85C \uC124\uC815\uD569\uB2C8\uB2E4.

DiagnosticsConnectionIdPrefixParameterDescription=\uC811\uC18D ID \uC811\uB450\uC5B4.

DiagnosticsTenantNameParameterDescription=\uD14C\uB10C\uD2B8 \uC774\uB984.

DiagnosticsLoggerNameParameterDescription=\uB85C\uAC70 \uC774\uB984.

DiagnosticsLevelParameterDescription=\uB85C\uAC70 \uB808\uBCA8.

DiagnosticsBufferSizeParameterDescription="\uCCAB\uBC88\uC9F8 \uC2E4\uD328 \uC9C4\uB2E8" \uC9C4\uB2E8 \uBC84\uD37C \uD06C\uAE30\uC785\uB2C8\uB2E4.

DiagnosticsConfigFileParameterDescription=\uB85C\uAE45 \uAD6C\uC131 \uD30C\uC77C.

DiagnosticsErrorCodesParameterDescription=ORA-XXXXX \uD615\uC2DD\uC758 \uC624\uB958 \uCF54\uB4DC\uC785\uB2C8\uB2E4.

DiagnosticsEnabledParameterDescription=true \uB610\uB294 false \uAC12.

DiagnosticsCommaSeparatedKeywordsParameterDescription=\uCF64\uB9C8\uB85C \uAD6C\uBD84\uB41C \uAC12 \uD615\uC2DD\uC758 \uD0A4\uC6CC\uB4DC\uC785\uB2C8\uB2E4.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






