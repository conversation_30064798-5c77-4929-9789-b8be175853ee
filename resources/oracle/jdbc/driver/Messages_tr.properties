#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Oracle JDBC s\u00FCr\u00FCc\u00FClerinin tan\u0131lanabilme \u00F6zelliklerini kontrol eder.

DiagnosabilityMBeanConstructor()=Oracle JDBC Tan\u0131lanabilme MBean i\u00E7in s\u0131f\u0131r ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken yap\u0131land\u0131ran\u0131

DiagnosabilityMBeanLoggingEnabledDescription=T\u00FCm Oracle JDBC g\u00FCnl\u00FCk kodlar\u0131 bu mant\u0131ksal de\u011Fer \u00F6zelli\u011Fi taraf\u0131ndan kontrol edilir. Yanl\u0131\u015F ise, g\u00FCnl\u00FCk mesaj\u0131 \u00FCretilmez. Do\u011Fru ise, g\u00FCnl\u00FCk mesaj\u0131 java.util.logging D\u00FCzeyleri ve Filtreleri ile kontrol edilir.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC s\u00FCr\u00FCc\u00FCleri ba\u015Flat\u0131lamaz ve durdurulamaz. Her zaman yanl\u0131\u015F de\u011Ferini d\u00F6nd\u00FCr\u00FCr.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC s\u00FCr\u00FCc\u00FCleri Tan\u0131lanabilirlik MBean ile istatistik sa\u011Flamaz.

DiagnosabilityMBeanTraceControllerDescription=Clio \u0130z Denetleyici.

DiagnosabilityMBeanSuspendDescription=Clio i\u015Flemi ask\u0131ya al.

DiagnosabilityMBeanResumeDescription=Clio i\u015Flemi s\u00FCrd\u00FCr.

DiagnosabilityMBeanTraceDescription=Clio i\u015Flemi izle.

DiagnosabilityMBeanEnableContinousLoggingDescription=G\u00FCvenli s\u00FCrekli dosya g\u00FCnl\u00FCk kayd\u0131n\u0131 etkinle\u015Ftirir. ServiceName ve UserName filtreleri ayarlan\u0131rlarsa uygulanabilirler. \u00D6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r.

DiagnosabilityMBeanDisableContinousLoggingDescription=G\u00FCvenli s\u00FCrekli dosya g\u00FCnl\u00FCk kayd\u0131n\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r. \u00D6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=\u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 etkinle\u015Ftirir. ServiceName ve UserName filtreleri, ayarlan\u0131rlarsa uygulanabilirler. \u00D6nde\u011Fer olarak etkindir.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=\u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r. \u00D6nde\u011Fer olarak etkindir.

DiagnosabilityMBeanServiceNameFilterDescription=ServiceName filtresi ayarlanm\u0131\u015Fsa bellek i\u00E7i / s\u00FCrekli g\u00FCnl\u00FC\u011Fe kaydetme sadece konfig\u00FCre edilen hizmetin ba\u011Flant\u0131lar\u0131 i\u00E7in etkindir.

DiagnosabilityMBeanUserFilterDescription=Kullan\u0131c\u0131 filtresi ayarlanm\u0131\u015Fsa, bellek i\u00E7i / s\u00FCrekli g\u00FCnl\u00FC\u011Fe kaydetme sadece konfig\u00FCre edilen Kullan\u0131c\u0131n\u0131n ba\u011Flant\u0131lar\u0131 i\u00E7in etkindir.

ReplayStatisticsMBeanDescription=Uygulama S\u00FCreklili\u011Fi \u00F6zelli\u011Finin istatisti\u011Fini g\u00F6sterir.

ReplayStatisticsMBeanConstructor=Oracle JDBC Uygulama S\u00FCreklili\u011Fi istatisti\u011Fi MBean i\u00E7in s\u0131f\u0131r ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken yap\u0131land\u0131r\u0131c\u0131s\u0131

ReplayStatisticsMBeanAllStatisticsDescription=Bilinen t\u00FCm s\u00FCr\u00FCc\u00FC veri kayna\u011F\u0131 \u00F6rneklerinde Uygulama S\u00FCreklili\u011Fi istatisti\u011Fini al\u0131r.

ReplayStatisticsMBeanGetDSStatisticsDescription=Belirli bir s\u00FCr\u00FCc\u00FC veri kayna\u011F\u0131 \u00F6rne\u011Fi i\u00E7in Uygulama S\u00FCreklili\u011Fi istatisti\u011Fini al\u0131r.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Oracle JDBC s\u00FCr\u00FCc\u00FClerinin tan\u0131lanabilme \u00F6zelliklerini kontrol eder.

DiagnosticsMBeanLoggingEnabledAttributeDescription=T\u00FCm Oracle JDBC g\u00FCnl\u00FCk kodlar\u0131 bu mant\u0131ksal de\u011Fer \u00F6zniteli\u011Fi taraf\u0131ndan kontrol edilir. \u00D6nde\u011Fer yanl\u0131\u015Ft\u0131r. A\u00E7\u0131l\u0131rsa, g\u00FCnl\u00FCk mesajlar\u0131 java.util.logging D\u00FCzeyleri ve Filtreleri ile kontrol edilir. Kapat\u0131l\u0131rsa g\u00FCnl\u00FCk mesaj\u0131 \u00FCretilmez.

DiagnosticsMBeanMetricsEnabledAttributeDescription=JDBC s\u00FCr\u00FCc\u00FCs\u00FC taraf\u0131ndan yakalanan etkinliklerin metrikleri bu mant\u0131ksal de\u011Fer \u00F6zelli\u011Fi ile kontrol edilir. \u00D6nde\u011Fer: yanl\u0131\u015F. Etkinse devre d\u0131\u015F\u0131 b\u0131rak\u0131lana kadar etkinlik metrikleri s\u00FCr\u00FCc\u00FC taraf\u0131ndan yakalan\u0131r.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=G\u00FCnl\u00FCklerin bellek i\u00E7i izleme arabelle\u011Fine yaz\u0131lmas\u0131 etkinse do\u011Fru d\u00F6nd\u00FCr\u00FCr. \u00D6nde\u011Fer yanl\u0131\u015Ft\u0131r.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem devre d\u0131\u015F\u0131 b\u0131rak\u0131lm\u0131\u015Fsa belirtilen ba\u011Flant\u0131 no \u00F6n ekine g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 etkinle\u015Ftirir.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem belirtilen ba\u011Flant\u0131 no \u00F6n ekine g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem devre d\u0131\u015F\u0131 b\u0131rak\u0131lm\u0131\u015Fsa belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 etkinle\u015Ftirir.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem devre d\u0131\u015F\u0131 b\u0131rak\u0131lm\u0131\u015Fsa belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 etkinle\u015Ftirir.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem devre d\u0131\u015F\u0131 b\u0131rak\u0131lm\u0131\u015Fsa \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 etkinle\u015Ftirir.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma \u00F6nde\u011Fer olarak etkindir. Bu i\u015Flem \u0130lk Hata Tan\u0131s\u0131 Koymay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen ba\u011Flant\u0131 kimli\u011Fi \u00F6n ekini etkinle\u015Ftirir.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen ba\u011Flant\u0131 kimli\u011Fi \u00F6n ekine g\u00F6re g\u00FCnl\u00FC\u011Fe kaydetmeyi devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableLoggingByTenantNameOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re g\u00FCnl\u00FC\u011Fe kaydetmeyi etkinle\u015Ftirir.

DiagnosticsDisableLoggingByTenantNameOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re g\u00FCnl\u00FC\u011Fe kaydetmeyi devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re g\u00FCnl\u00FC\u011Fe kaydetmeyi etkinle\u015Ftirir.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re g\u00FCnl\u00FC\u011Fe kaydetmeyi devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableLoggingOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, g\u00FCnl\u00FC\u011Fe kaydetmeyi etkinle\u015Ftirir.

DiagnosticsDisableLoggingOperationDescription=G\u00FCnl\u00FCk kayd\u0131 \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse g\u00FCnl\u00FC\u011Fe kaydetmeyi devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableMetricsOperationDescription=Ba\u011Flant\u0131 s\u00FCresi s\u0131ras\u0131nda zamanlama metriklerini toplamay\u0131 ba\u015Flat\u0131n.

DiagnosticsDisableMetricsOperationDescription=Ba\u011Flant\u0131 s\u00FCresi s\u0131ras\u0131nda zamanlama metriklerini toplamay\u0131 durdurun.

DiagnosticsShowMetricsOperationDescription=Ba\u011Flant\u0131 s\u00FCresi s\u0131ras\u0131nda toplanan zamanlama metriklerini g\u00F6sterin.

DiagnosticsClearMetricsOperationDescription=Ba\u011Flant\u0131 s\u00FCresi s\u0131ras\u0131nda toplanan zamanlama metriklerini temizleyin.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen ba\u011Flant\u0131 kimli\u011Fi \u00F6n ekine g\u00F6re hassas tan\u0131lamay\u0131 etkinle\u015Ftirir.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen ba\u011Flant\u0131 kimli\u011Fi \u00F6n ekine g\u00F6re hassas tan\u0131lamay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re hassas tan\u0131lamay\u0131 etkinle\u015Ftirir.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re hassas tan\u0131lamay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re hassas tan\u0131lamay\u0131 etkinle\u015Ftirir.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re hassas tan\u0131lamay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, hassas tan\u0131lamay\u0131 etkinle\u015Ftirir.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Hassas tan\u0131lama \u00F6nde\u011Fer olarak devre d\u0131\u015F\u0131d\u0131r. Bu i\u015Flem, etkinle\u015Ftirilmi\u015Fse hassas tan\u0131lamay\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Tan\u0131lama d\u00FCzeyini, belirtilen ba\u011Flant\u0131 no'su \u00F6n ekiyle g\u00FCncelleyin. Ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken dizesi bir d\u00FCzey ad\u0131ndan veya bir tam say\u0131 de\u011Ferinden olu\u015Fabilir. \u00D6rne\u011Fin: "SEVERE" veya "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Tan\u0131lama d\u00FCzeyini, belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131yla g\u00FCncelleyin. Ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken dizesi bir d\u00FCzey ad\u0131ndan veya bir tam say\u0131 de\u011Ferinden olu\u015Fabilir. \u00D6rne\u011Fin: "SEVERE" veya "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Tan\u0131lama d\u00FCzeyini, belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131yla g\u00FCncelleyin. Ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken dizesi bir d\u00FCzey ad\u0131ndan veya bir tam say\u0131 de\u011Ferinden olu\u015Fabilir. \u00D6rne\u011Fin: "SEVERE" veya "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Tan\u0131lama d\u00FCzeyini g\u00FCncelleyin. Ba\u011F\u0131ms\u0131z de\u011Fi\u015Fken dizesi bir d\u00FCzey ad\u0131ndan veya bir tam say\u0131 de\u011Ferinden olu\u015Fabilir. \u00D6rne\u011Fin: "SEVERE" veya "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lama arabellek boyutunu belirtilen ba\u011Flant\u0131 no \u00F6n ekine g\u00F6re g\u00FCncelleyin. Arabellek boyutu \u00F6nde\u011Feri 4000 g\u00FCnl\u00FCk kayd\u0131d\u0131r.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lama arabellek boyutunu belirtilen ge\u00E7ici kullan\u0131c\u0131 ad\u0131na g\u00F6re g\u00FCncelleyin. Arabellek boyutu \u00F6nde\u011Feri 4000 g\u00FCnl\u00FCk kayd\u0131d\u0131r.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lama arabellek boyutunu belirtilen g\u00FCnl\u00FCk kay\u0131t\u00E7\u0131s\u0131 ad\u0131na g\u00F6re g\u00FCncelleyin. Arabellek boyutu \u00F6nde\u011Feri 4000 g\u00FCnl\u00FCk kayd\u0131d\u0131r.

DiagnosticsUpdateBufferSizeOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lama boyutunu g\u00FCncelleyin. Arabellek boyutu \u00F6nde\u011Feri 4000 g\u00FCnl\u00FCk kayd\u0131d\u0131r.

DiagnosticsReadLoggingConfigFileOperationDescription=G\u00FCnl\u00FCk niteliklerini yeniden ba\u015Flat\u0131n ve belirtilen dosyada (java.util.Properties format\u0131nda olmal\u0131d\u0131r) g\u00FCnl\u00FCk konfig\u00FCrasyonunu yeniden okuyun.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Bir sonraki tekrar i\u00E7in izlenecek hata kodunu ORA-XXXXX format\u0131nda ekleyin. JDBC s\u00FCr\u00FCc\u00FCs\u00FC, izleme listesinde bir hata olu\u015Ftu\u011Funda \u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lamas\u0131n\u0131 konfig\u00FCre edilmi\u015F g\u00FCnl\u00FCk i\u015Fleyicisine yazar.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Belirtilen hata kodunu izleme listesinden kald\u0131r\u0131n. Hata kodu ORA-XXXXX format\u0131nda olmal\u0131d\u0131r.

DiagnosticsShowErrorCodesWatchListOperationDescription=\u0130zlenen hata kodlar\u0131n\u0131 g\u00F6sterin.

DiagnosticsResetErrorCodesWatchListOperationDescription=\u0130zleme listesini hata kodu \u00F6nde\u011Ferleriyle konfig\u00FCre edin.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lamas\u0131n\u0131 hedef i\u015Fleyiciye d\u00F6k\u00FCn.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Gelecekteki istisna belirtilen anahtar s\u00F6zc\u00FCklerden birini i\u00E7erdi\u011Finde \u0130lk Hata Bulma Tan\u0131s\u0131 Koyma tan\u0131lama d\u00F6k\u00FCm\u00FCn\u00FC al\u0131n. \u00D6rnek anahtar s\u00F6zc\u00FCkler: s\u0131f\u0131rla, ihlal.

DiagnosticsShowExceptionKeywords=Daha \u00F6nce eklenen istisna anahtar s\u00F6zc\u00FCkleri.

DiagnosticsShowRecentOperations=MBean \u00FCzerinde bu kullan\u0131c\u0131 taraf\u0131ndan ger\u00E7ekle\u015Ftirilen en son i\u015Flemler.

DiagnosticsClearExceptionKeywordsOperationDescription=Daha \u00F6nce eklenen anahtar s\u00F6zc\u00FCkleri temizleyin.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=G\u00FCnl\u00FCkler \u00F6nde\u011Fer olarak g\u00FCnl\u00FCk i\u015Fleyicisine yaz\u0131l\u0131r. Bu i\u015Flem, \u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lamas\u0131 i\u00E7in g\u00FCnl\u00FC\u011Fe yazma i\u015Flemini etkinle\u015Ftirir veya devre d\u0131\u015F\u0131 b\u0131rak\u0131r.

DiagnosticsConnectionIdPrefixParameterDescription=Ba\u011Flant\u0131 Kimli\u011Fi \u00D6n Eki.

DiagnosticsTenantNameParameterDescription=Ge\u00E7ici Kullan\u0131c\u0131 Ad\u0131.

DiagnosticsLoggerNameParameterDescription=G\u00FCnl\u00FCk Kay\u0131t\u00E7\u0131s\u0131 Ad\u0131.

DiagnosticsLevelParameterDescription=G\u00FCnl\u00FCk Kay\u0131t\u00E7\u0131s\u0131 D\u00FCzeyi.

DiagnosticsBufferSizeParameterDescription=\u0130lk Hata Tan\u0131s\u0131 Koyma tan\u0131lamas\u0131 Arabellek Boyutu.

DiagnosticsConfigFileParameterDescription=G\u00FCnl\u00FCk Kayd\u0131 Konfig\u00FCrasyon Dosyas\u0131.

DiagnosticsErrorCodesParameterDescription=ORA-XXXXX format\u0131nda bir hata kodu.

DiagnosticsEnabledParameterDescription=Do\u011Fru veya yanl\u0131\u015F de\u011Feri.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Virg\u00FClle ayr\u0131lm\u0131\u015F de\u011Ferler olarak anahtar s\u00F6zc\u00FCkler.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






