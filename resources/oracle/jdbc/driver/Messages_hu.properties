#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Az Oracle JDBC-illeszt\u0151programok diagnosztiz\u00E1lhat\u00F3s\u00E1g\u00E1nak szab\u00E1lyoz\u00E1sa

DiagnosabilityMBeanConstructor()=Nulla \u00E9rt\u00E9k\u0171 argumentumot haszn\u00E1l\u00F3 konstruktor az Oracle JDBC diagnosztiz\u00E1lhat\u00F3s\u00E1g\u00E1t vizsg\u00E1l\u00F3 MBean sz\u00E1m\u00E1ra 

DiagnosabilityMBeanLoggingEnabledDescription=Ez a logikai \u00E9rt\u00E9k szab\u00E1lyozza valamennyi Oracle JDBC napl\u00F3z\u00E1si k\u00F3dj\u00E1t. Ha \u00E9rt\u00E9ke hamis, nem k\u00E9sz\u00FClnek napl\u00F3\u00FCzenetek. Ha \u00E9rt\u00E9ke igaz, a napl\u00F3\u00FCzeneteket a java.util.logging szintjei \u00E9s sz\u0171r\u0151i hat\u00E1rozz\u00E1k meg.

DiagnosabilityMBeanStateManageableDescription=Az Oracle JDBC-illeszt\u0151programok nem ind\u00EDthat\u00F3k el \u00E9s nem \u00E1ll\u00EDthat\u00F3k le. Mindig hamis \u00E9rt\u00E9ket ad vissza.

DiagnosabilityMBeanStatisticsProviderDescription=Az Oracle JDBC-illeszt\u0151programok nem ny\u00FAjtanak statisztikai adatokat a diagnosztiz\u00E1lhat\u00F3s\u00E1got vizsg\u00E1l\u00F3 MBean r\u00E9v\u00E9n.

DiagnosabilityMBeanTraceControllerDescription=Clio nyomk\u00F6vet\u00E9si vez\u00E9rl\u0151.

DiagnosabilityMBeanSuspendDescription=Clio m\u0171k\u00F6d\u00E9s felf\u00FCggeszt\u00E9se.

DiagnosabilityMBeanResumeDescription=Clio m\u0171k\u00F6d\u00E9s folytat\u00E1sa.

DiagnosabilityMBeanTraceDescription=Clio m\u0171k\u00F6d\u00E9s nyomk\u00F6vet\u00E9se.

DiagnosabilityMBeanEnableContinousLoggingDescription=Enged\u00E9lyezi a biztons\u00E1gos folyamatos f\u00E1jlnapl\u00F3z\u00E1st. A ServiceName \u00E9s a UserName sz\u0171r\u0151 alkalmazhat\u00F3 megad\u00E1s eset\u00E9n. Alap\u00E9rtelmez\u00E9s szerint le van tiltva.

DiagnosabilityMBeanDisableContinousLoggingDescription=Letiltja a biztons\u00E1gos folyamatos f\u00E1jlnapl\u00F3z\u00E1st. Alap\u00E9rtelmez\u00E9s szerint le van tiltva.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Enged\u00E9lyezi az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get. A ServiceName \u00E9s a UserName sz\u0171r\u0151 alkalmazhat\u00F3 megad\u00E1s eset\u00E9n. Alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Letiltja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get. Alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van.

DiagnosabilityMBeanServiceNameFilterDescription=Ha a ServiceName sz\u0171r\u0151 be van \u00E1ll\u00EDtva, akkor a mem\u00F3riabeli/folyamatos napl\u00F3z\u00E1s csak a konfigur\u00E1lt szolg\u00E1ltat\u00E1s kapcsolatai sz\u00E1m\u00E1ra van enged\u00E9lyezve.

DiagnosabilityMBeanUserFilterDescription=Ha a User sz\u0171r\u0151 be van \u00E1ll\u00EDtva, akkor a mem\u00F3riabeli/folyamatos napl\u00F3z\u00E1s csak a konfigur\u00E1lt felhaszn\u00E1l\u00F3 kapcsolatai sz\u00E1m\u00E1ra van enged\u00E9lyezve.

ReplayStatisticsMBeanDescription=Az Application Continuity (AC) funkci\u00F3 statisztik\u00E1j\u00E1t jelen\u00EDti meg.

ReplayStatisticsMBeanConstructor=A nulla argumentum\u00FA konstruktor az Oracle JDBC AC statisztikai MBean sz\u00E1m\u00E1ra

ReplayStatisticsMBeanAllStatisticsDescription=Az AC statisztika beolvas\u00E1sa az \u00F6sszes ismert jellemz\u0151 adatforr\u00E1sp\u00E9ld\u00E1nyhoz.

ReplayStatisticsMBeanGetDSStatisticsDescription=Az AC statisztika beolvas\u00E1sa adott jellemz\u0151 adatforr\u00E1sp\u00E9ld\u00E1nyhoz.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Az Oracle JDBC-illeszt\u0151programok diagnosztiz\u00E1lhat\u00F3s\u00E1g\u00E1nak szab\u00E1lyoz\u00E1sa

DiagnosticsMBeanLoggingEnabledAttributeDescription=Az \u00F6sszes Oracle JDBC napl\u00F3z\u00E1si k\u00F3dot ez a logikai attrib\u00FAtum vez\u00E9rli. Az alap\u00E9rtelmezett \u00E9rt\u00E9k a hamis. Ha be van kapcsolva, a napl\u00F3\u00FCzeneteket a java.util.logging szintjei \u00E9s sz\u0171r\u0151k vez\u00E9rlik. Ha ki van kapcsolva, nem k\u00E9sz\u00FCl napl\u00F3\u00FCzenet.

DiagnosticsMBeanMetricsEnabledAttributeDescription=A JDBC illeszt\u0151program \u00E1ltal r\u00F6gz\u00EDtett esem\u00E9nyek minden m\u00E9r\u0151sz\u00E1m\u00E1t ez a logikai attrib\u00FAtum vez\u00E9rli. Alap\u00E9rtelmezett \u00E9rt\u00E9ke: hamis. Ha enged\u00E9lyezve van, az illeszt\u0151program addig r\u00F6gz\u00EDti az esem\u00E9nyek m\u00E9r\u0151sz\u00E1mait, am\u00EDg ezt le nem tiltj\u00E1k.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Igaz \u00E9rt\u00E9ket ad vissza, ha a napl\u00F3k \u00EDr\u00E1sa a mem\u00F3ri\u00E1n bel\u00FCli nyomk\u00F6vet\u00E9si pufferbe enged\u00E9lyezett. Az alap\u00E9rtelmezett \u00E9rt\u00E9k a hamis.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet enged\u00E9lyezi az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott kapcsolatazonos\u00EDt\u00F3 el\u0151tagja alapj\u00E1n, ha le volt tiltva.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet letiltja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott kapcsolatazonos\u00EDt\u00F3 el\u0151tagja alapj\u00E1n.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet enged\u00E9lyezi az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott b\u00E9rl\u0151n\u00E9v alapj\u00E1n, ha le volt tiltva.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet letiltja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott b\u00E9rl\u0151n\u00E9v alapj\u00E1n.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet enged\u00E9lyezi az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n, ha le volt tiltva.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet letiltja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get az adott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet enged\u00E9lyezi az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get, ha le volt tiltva.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa alap\u00E9rtelmez\u00E9s szerint enged\u00E9lyezve van. Ez a m\u0171velet letiltja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa lehet\u0151s\u00E9get.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi a napl\u00F3z\u00E1st az adott kapcsolatazonos\u00EDt\u00F3-el\u0151tag alapj\u00E1n.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja a napl\u00F3z\u00E1st az adott kapcsolatazonos\u00EDt\u00F3-el\u0151tag alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableLoggingByTenantNameOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi a napl\u00F3z\u00E1st a megadott b\u00E9rl\u0151n\u00E9v alapj\u00E1n.

DiagnosticsDisableLoggingByTenantNameOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja a napl\u00F3z\u00E1st a megadott b\u00E9rl\u0151n\u00E9v alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi a napl\u00F3z\u00E1st a megadott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja a napl\u00F3z\u00E1st a megadott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableLoggingOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi a napl\u00F3z\u00E1st.

DiagnosticsDisableLoggingOperationDescription=A napl\u00F3z\u00E1s alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja a napl\u00F3z\u00E1st, ha az enged\u00E9lyezve volt.

DiagnosticsEnableMetricsOperationDescription=Id\u0151z\u00EDt\u00E9si m\u00E9r\u0151sz\u00E1mok gy\u0171jt\u00E9s\u00E9nek elkezd\u00E9se a kapcsolat ideje alatt.

DiagnosticsDisableMetricsOperationDescription=Id\u0151z\u00EDt\u00E9si m\u00E9r\u0151sz\u00E1mok gy\u0171jt\u00E9s\u00E9nek le\u00E1ll\u00EDt\u00E1sa a kapcsolat ideje alatt.

DiagnosticsShowMetricsOperationDescription=A kapcsolat ideje alatt gy\u0171jt\u00F6tt id\u0151z\u00EDt\u00E9si m\u00E9r\u0151sz\u00E1mok megjelen\u00EDt\u00E9se.

DiagnosticsClearMetricsOperationDescription=A kapcsolat ideje alatt gy\u0171jt\u00F6tt id\u0151z\u00EDt\u00E9si m\u00E9r\u0151sz\u00E1mok t\u00F6rl\u00E9se.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi az \u00E9rz\u00E9keny diagnosztik\u00E1t a megadott kapcsolatazonos\u00EDt\u00F3-el\u0151tag alapj\u00E1n.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja az \u00E9rz\u00E9keny diagnosztik\u00E1t az adott kapcsolatazonos\u00EDt\u00F3-el\u0151tag alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi az \u00E9rz\u00E9keny diagnosztik\u00E1t a megadott b\u00E9rl\u0151n\u00E9v alapj\u00E1n.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja az \u00E9rz\u00E9keny diagnosztik\u00E1t az adott b\u00E9rl\u0151n\u00E9v alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet lehet\u0151v\u00E9 teszi az \u00E9rz\u00E9keny diagnosztik\u00E1t a megadott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja az \u00E9rz\u00E9keny diagnosztik\u00E1t a megadott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n, ha az enged\u00E9lyezve volt.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet enged\u00E9lyezi az \u00E9rz\u00E9keny diagnosztik\u00E1t.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Az \u00E9rz\u00E9keny diagnosztika alap\u00E9rtelmez\u00E9s szerint le van tiltva. Ez a m\u0171velet letiltja az \u00E9rz\u00E9keny diagnosztik\u00E1t, ha az enged\u00E9lyezve volt.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=A diagnosztikai szint friss\u00EDt\u00E9se a megadott kapcsolatazonos\u00EDt\u00F3-el\u0151taggal. Az argumentum karakterl\u00E1nca egy szint nev\u00E9t vagy egy eg\u00E9sz sz\u00E1mot tartalmazhat. P\u00E9ld\u00E1ul: "SEVERE" vagy "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=A diagnosztikai szint friss\u00EDt\u00E9se a megadott b\u00E9rletn\u00E9vvel. Az argumentum karakterl\u00E1nca egy szint nev\u00E9t vagy egy eg\u00E9sz sz\u00E1mot tartalmazhat. P\u00E9ld\u00E1ul: "SEVERE" vagy "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=A diagnosztikai szint friss\u00EDt\u00E9se a megadott napl\u00F3z\u00F3n\u00E9vvel. Az argumentum karakterl\u00E1nca egy szint nev\u00E9t vagy egy eg\u00E9sz sz\u00E1mot tartalmazhat. P\u00E9ld\u00E1ul: "SEVERE" vagy "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=A diagnosztikai szint friss\u00EDt\u00E9se. Az argumentum karakterl\u00E1nca egy szint nev\u00E9t vagy egy eg\u00E9sz sz\u00E1mot tartalmazhat. P\u00E9ld\u00E1ul: "SEVERE" vagy "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa puffer m\u00E9ret\u00E9nek m\u00F3dos\u00EDt\u00E1sa a megadott kapcsolatazonos\u00EDt\u00F3-el\u0151tag alapj\u00E1n. Az alap\u00E9rtelmezett pufferm\u00E9ret 4000 napl\u00F3bejegyz\u00E9s.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa puffer m\u00E9ret\u00E9nek m\u00F3dos\u00EDt\u00E1sa a megadott b\u00E9rl\u0151n\u00E9v alapj\u00E1n. Az alap\u00E9rtelmezett pufferm\u00E9ret 4000 napl\u00F3bejegyz\u00E9s.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa puffer m\u00E9ret\u00E9nek m\u00F3dos\u00EDt\u00E1sa a megadott napl\u00F3z\u00F3n\u00E9v alapj\u00E1n. Az alap\u00E9rtelmezett pufferm\u00E9ret 4000 napl\u00F3bejegyz\u00E9s.

DiagnosticsUpdateBufferSizeOperationDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa puffer m\u00E9ret\u00E9nek m\u00F3dos\u00EDt\u00E1sa. Az alap\u00E9rtelmezett pufferm\u00E9ret 4000 napl\u00F3bejegyz\u00E9s.

DiagnosticsReadLoggingConfigFileOperationDescription=Inicializ\u00E1lja \u00FAjra a napl\u00F3z\u00E1si tulajdons\u00E1gokat, \u00E9s olvassa be \u00FAjra a napl\u00F3z\u00E1si konfigur\u00E1ci\u00F3t az adott f\u00E1jlb\u00F3l, amelynek java.util.Properties form\u00E1tumban kell lennie.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Adja hozz\u00E1 a k\u00F6vetkez\u0151 el\u0151fordul\u00E1shoz a figyelni k\u00EDv\u00E1nt hibak\u00F3dokat ORA-XXXXX form\u00E1tumban. A JDBC-illeszt\u0151program be\u00EDrja az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa diagnosztikai adatait a konfigur\u00E1lt napl\u00F3kezel\u0151be, ha hiba t\u00F6rt\u00E9nik a figyel\u00E9si list\u00E1ban.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=T\u00E1vol\u00EDtsa el a megadott hibak\u00F3dot a figyel\u00E9si list\u00E1b\u00F3l. A hibak\u00F3dnak ORA-XXXXX form\u00E1tumban kell lennie.

DiagnosticsShowErrorCodesWatchListOperationDescription=A figyelt hibak\u00F3dok megjelen\u00EDt\u00E9se.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigur\u00E1lja a figyel\u00E9si list\u00E1t az alap\u00E9rtelmezett hibak\u00F3dokkal.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa diagnosztikai adatok ki\u00EDr\u00E1sa a c\u00E9lkezel\u0151be.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=\u00CDrja ki az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa diagnosztikai adatait, ha a j\u00F6v\u0151beni kiv\u00E9tel az egyik megadott kulcssz\u00F3t tartalmazza. A p\u00E9ld\u00E1k kulcsszavai: reset (vissza\u00E1ll\u00EDt\u00E1s), violation (megs\u00E9rt\u00E9s).

DiagnosticsShowExceptionKeywords=A kor\u00E1bban kihagyott kulcsszavak kiv\u00E9telt k\u00E9peznek.

DiagnosticsShowRecentOperations=A felhaszn\u00E1l\u00F3 \u00E1ltal ezen az MBean elemen v\u00E9grehajtott legut\u00F3bbi m\u0171veletek.

DiagnosticsClearExceptionKeywordsOperationDescription=T\u00F6r\u00F6lje a kor\u00E1bban hozz\u00E1adott kulcsszavakat.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Alap\u00E9rtelmez\u00E9s szerint a rendszer a napl\u00F3kezel\u0151be \u00EDrja a napl\u00F3kat. Ez a m\u0171velet enged\u00E9lyezi/letiltja a napl\u00F3k Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa diagnosztikai adataiba val\u00F3 \u00EDr\u00E1s\u00E1t.

DiagnosticsConnectionIdPrefixParameterDescription=A kapcsolatazonos\u00EDt\u00F3 el\u0151tagja.

DiagnosticsTenantNameParameterDescription=A b\u00E9rl\u0151 neve.

DiagnosticsLoggerNameParameterDescription=A napl\u00F3z\u00F3 neve.

DiagnosticsLevelParameterDescription=A napl\u00F3z\u00F3 szintje.

DiagnosticsBufferSizeParameterDescription=Az Els\u0151 hiba diagnosztiz\u00E1l\u00E1sa diagnosztik\u00E1j\u00E1nak pufferm\u00E9rete.

DiagnosticsConfigFileParameterDescription=A napl\u00F3z\u00E1s konfigur\u00E1ci\u00F3s f\u00E1jlja.

DiagnosticsErrorCodesParameterDescription=Hibak\u00F3d ORA-XXXXX form\u00E1tumban.

DiagnosticsEnabledParameterDescription=Az igaz vagy hamis \u00E9rt\u00E9k.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Kulcsszavak vessz\u0151vel tagolt \u00E9rt\u00E9kekk\u00E9nt.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






