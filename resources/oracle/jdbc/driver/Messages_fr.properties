#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Contr\u00F4le les fonctionnalit\u00E9s de diagnostic des pilotes Oracle JDBC.

DiagnosabilityMBeanConstructor()=Constructeur sans argument pour Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=Cet attribut bool\u00E9en contr\u00F4le tout le code de journalisation Oracle JDBC. Si la valeur est False, aucun message de journalisation n'est produit. Si la valeur est True, les messages de journalisation seront contr\u00F4l\u00E9s par les filtres et les niveaux java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Les pilotes Oracle JDBC ne peuvent pas \u00EAtre d\u00E9marr\u00E9s ni arr\u00EAt\u00E9s. La valeur False est toujours renvoy\u00E9e.

DiagnosabilityMBeanStatisticsProviderDescription=Les pilotes Oracle JDBC ne fournissent pas de statistiques via Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Contr\u00F4leur de trace Clio.

DiagnosabilityMBeanSuspendDescription=Op\u00E9ration de suspension Clio.

DiagnosabilityMBeanResumeDescription=Op\u00E9ration de reprise Clio.

DiagnosabilityMBeanTraceDescription=Op\u00E9ration de trace Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Active la journalisation de fichier continue et s\u00E9curis\u00E9e. Les filtres ServiceName et UserName sont applicables s'ils sont d\u00E9finis. L'option est d\u00E9sactiv\u00E9e par d\u00E9faut.

DiagnosabilityMBeanDisableContinousLoggingDescription=D\u00E9sactive la journalisation de fichier continue et s\u00E9curis\u00E9e. L'option est d\u00E9sactiv\u00E9e par d\u00E9faut.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Active le diagnostic de premier \u00E9chec. Les filtres ServiceName et UserName sont applicables s'ils sont d\u00E9finis. L'option est activ\u00E9e par d\u00E9faut.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=D\u00E9sactive le diagnostic de premier \u00E9chec. L'option est activ\u00E9e par d\u00E9faut.

DiagnosabilityMBeanServiceNameFilterDescription=Si le filtre ServiceName est d\u00E9fini, la journalisation continue/en m\u00E9moire est activ\u00E9e uniquement pour les connexions du service configur\u00E9.

DiagnosabilityMBeanUserFilterDescription=Si le filtre UserName est d\u00E9fini, la journalisation continue/en m\u00E9moire est activ\u00E9e uniquement pour les connexions de l'utilisateur configur\u00E9.

ReplayStatisticsMBeanDescription=Affiche les statistiques de la fonctionnalit\u00E9 de continuit\u00E9 de l'application (AC).

ReplayStatisticsMBeanConstructor=Constructeur sans argument pour le MBean de statistiques AC Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Obtient des statistiques AC sur toutes les instances de source de donn\u00E9es de pilote connues.

ReplayStatisticsMBeanGetDSStatisticsDescription=Obtient des statistiques AC pour une instance de source de donn\u00E9es de pilote sp\u00E9cifique.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Contr\u00F4le les fonctionnalit\u00E9s de diagnostic des pilotes Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Cet attribut bool\u00E9en contr\u00F4le tout le code de journalisation Oracle JDBC. La valeur par d\u00E9faut est False. Si l'attribut est activ\u00E9, les messages de journalisation sont contr\u00F4l\u00E9s par les filtres et les niveaux java.util.logging. S'il est d\u00E9sactiv\u00E9, aucun message de journalisation n'est g\u00E9n\u00E9r\u00E9.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Toutes les mesures d'\u00E9v\u00E9nements captur\u00E9es par le pilote JDBC sont contr\u00F4l\u00E9es par cet attribut bool\u00E9en. La valeur par d\u00E9faut est False. Si l'attribut est activ\u00E9, les mesures d'\u00E9v\u00E9nements sont captur\u00E9es par le pilote jusqu'\u00E0 ce qu'il soit d\u00E9sactiv\u00E9.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Renvoie True si l'\u00E9criture des journaux dans le tampon de trace en m\u00E9moire est activ\u00E9e. La valeur par d\u00E9faut est False.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration active le diagnostic de premier \u00E9chec selon le pr\u00E9fixe d'ID de connexion donn\u00E9 s'il \u00E9tait d\u00E9sactiv\u00E9.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive le diagnostic de premier \u00E9chec selon le pr\u00E9fixe d'ID de connexion donn\u00E9.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration active le diagnostic de premier \u00E9chec selon le nom de locataire donn\u00E9 s'il \u00E9tait d\u00E9sactiv\u00E9.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive le diagnostic de premier \u00E9chec selon le nom de locataire donn\u00E9.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration active le diagnostic de premier \u00E9chec selon le nom de journaliseur donn\u00E9 s'il \u00E9tait d\u00E9sactiv\u00E9.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive le diagnostic de premier \u00E9chec selon le nom de journaliseur donn\u00E9.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration active le diagnostic de premier \u00E9chec s'il \u00E9tait d\u00E9sactiv\u00E9.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Le diagnostic de premier \u00E9chec est activ\u00E9 par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive le diagnostic de premier \u00E9chec.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration active la journalisation selon le pr\u00E9fixe d'ID de connexion donn\u00E9.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive la journalisation selon le pr\u00E9fixe d'ID de connexion donn\u00E9 si elle \u00E9tait activ\u00E9e.

DiagnosticsEnableLoggingByTenantNameOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration active la journalisation selon le nom de locataire donn\u00E9.

DiagnosticsDisableLoggingByTenantNameOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive la journalisation selon le nom de locataire donn\u00E9 si elle \u00E9tait activ\u00E9e.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration active la journalisation selon le nom de journaliseur donn\u00E9.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive la journalisation selon le nom de journaliseur donn\u00E9 si elle \u00E9tait activ\u00E9e.

DiagnosticsEnableLoggingOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration active la journalisation.

DiagnosticsDisableLoggingOperationDescription=La journalisation est d\u00E9sactiv\u00E9e par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive la journalisation si elle \u00E9tait activ\u00E9e.

DiagnosticsEnableMetricsOperationDescription=D\u00E9marrez la collecte des mesures temporelles pendant la dur\u00E9e de connexion.

DiagnosticsDisableMetricsOperationDescription=Arr\u00EAtez la collecte des mesures temporelles pendant la dur\u00E9e de connexion.

DiagnosticsShowMetricsOperationDescription=Affichez les mesures temporelles collect\u00E9es pendant la dur\u00E9e de connexion.

DiagnosticsClearMetricsOperationDescription=Effacez les mesures temporelles collect\u00E9es pendant la dur\u00E9e de connexion.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration active les diagnostics avec donn\u00E9es confidentielles selon le pr\u00E9fixe d'ID de connexion donn\u00E9.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive les diagnostics avec donn\u00E9es confidentielles selon le pr\u00E9fixe d'ID de connexion donn\u00E9 s'ils \u00E9taient activ\u00E9s.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration active les diagnostics avec donn\u00E9es confidentielles selon le nom de locataire donn\u00E9.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive les diagnostics avec donn\u00E9es confidentielles selon le nom de locataire donn\u00E9 s'ils \u00E9taient activ\u00E9s.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration active les diagnostics avec donn\u00E9es confidentielles selon le nom de journaliseur donn\u00E9.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive les diagnostics avec donn\u00E9es confidentielles selon le nom de journaliseur donn\u00E9 s'ils \u00E9taient activ\u00E9s.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration active les diagnostics avec donn\u00E9es confidentielles.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Les diagnostics avec donn\u00E9es confidentielles sont d\u00E9sactiv\u00E9s par d\u00E9faut. Cette op\u00E9ration d\u00E9sactive les diagnostics avec donn\u00E9es confidentielles s'ils \u00E9taient activ\u00E9s.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Mettez \u00E0 jour le niveau de diagnostic d'apr\u00E8s le pr\u00E9fixe d'ID de connexion donn\u00E9. La cha\u00EEne d'argument peut \u00EAtre compos\u00E9e d'un nom de niveau ou d'une valeur enti\u00E8re. Par exemple : "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Mettez \u00E0 jour le niveau de diagnostic d'apr\u00E8s le nom de locataire donn\u00E9. La cha\u00EEne d'argument peut \u00EAtre compos\u00E9e d'un nom de niveau ou d'une valeur enti\u00E8re. Par exemple : "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Mettez \u00E0 jour le niveau de diagnostic d'apr\u00E8s le nom de journaliseur donn\u00E9. La cha\u00EEne d'argument peut \u00EAtre compos\u00E9e d'un nom de niveau ou d'une valeur enti\u00E8re. Par exemple : "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Mettez \u00E0 jour le niveau de diagnostic. La cha\u00EEne d'argument peut \u00EAtre compos\u00E9e d'un nom de niveau ou d'une valeur enti\u00E8re. Par exemple : "SEVERE" ou "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Mettez \u00E0 jour la taille du tampon de diagnostic de premier \u00E9chec d'apr\u00E8s le pr\u00E9fixe d'ID de connexion donn\u00E9. La taille par d\u00E9faut du tampon est de 4 000 enregistrements de journal.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Mettez \u00E0 jour la taille du tampon de diagnostic de premier \u00E9chec d'apr\u00E8s le nom de locataire donn\u00E9. La taille par d\u00E9faut du tampon est de 4 000 enregistrements de journal.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Mettez \u00E0 jour la taille du tampon de diagnostic de premier \u00E9chec d'apr\u00E8s le nom de journaliseur donn\u00E9. La taille par d\u00E9faut du tampon est de 4 000 enregistrements de journal.

DiagnosticsUpdateBufferSizeOperationDescription=Mettez \u00E0 jour la taille du tampon de diagnostic de premier \u00E9chec. La taille par d\u00E9faut du tampon est de 4 000 enregistrements de journal.

DiagnosticsReadLoggingConfigFileOperationDescription=R\u00E9initialisez les propri\u00E9t\u00E9s de journalisation et lisez \u00E0 nouveau la configuration de journalisation \u00E0 partir du fichier donn\u00E9, qui doit \u00EAtre au format java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Ajoutez le code d'erreur \u00E0 contr\u00F4ler pour la prochaine occurrence au format ORA-XXXXX. Le pilote JDBC \u00E9crit le diagnostic de premier \u00E9chec dans le gestionnaire de journaux configur\u00E9 lorsqu'une erreur de la liste de contr\u00F4le survient.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Enlevez le code d'erreur donn\u00E9 de la liste de contr\u00F4le. Le code d'erreur doit \u00EAtre au format ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Affichez les codes d'erreur actuellement contr\u00F4l\u00E9s.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configurez la liste de contr\u00F4le avec les codes d'erreur par d\u00E9faut.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Videz le diagnostic de premier \u00E9chec vers le gestionnaire cible.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Videz le diagnostic de premier \u00E9chec lorsque la future exception contient l'un des mots-cl\u00E9s donn\u00E9s. Exemples de mot-cl\u00E9 : r\u00E9initialiser, violation.

DiagnosticsShowExceptionKeywords=Mots-cl\u00E9s d'exception ajout\u00E9s pr\u00E9c\u00E9demment.

DiagnosticsShowRecentOperations=Op\u00E9rations les plus r\u00E9centes effectu\u00E9es par l'utilisateur sur ce MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Effacez les mots-cl\u00E9s ajout\u00E9s pr\u00E9c\u00E9demment.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Par d\u00E9faut, les journaux sont \u00E9crits dans le gestionnaire de journaux. Cette op\u00E9ration active ou d\u00E9sactive l'\u00E9criture de journaux dans le diagnostic de premier \u00E9chec.

DiagnosticsConnectionIdPrefixParameterDescription=Pr\u00E9fixe d'ID de connexion.

DiagnosticsTenantNameParameterDescription=Nom de locataire.

DiagnosticsLoggerNameParameterDescription=Nom de journaliseur.

DiagnosticsLevelParameterDescription=Niveau de journaliseur.

DiagnosticsBufferSizeParameterDescription=Taille de tampon du diagnostic de premier \u00E9chec.

DiagnosticsConfigFileParameterDescription=Fichier de configuration de journalisation.

DiagnosticsErrorCodesParameterDescription=Code d'erreur au format ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Valeur True ou False.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Mots-cl\u00E9s sous forme de valeurs s\u00E9par\u00E9es par des virgules.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






