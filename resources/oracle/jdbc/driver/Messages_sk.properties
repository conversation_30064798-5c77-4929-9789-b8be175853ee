#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Riadi funkcie diagnostiky ovl\u00E1da\u010Dov Oracle JDBC.

DiagnosabilityMBeanConstructor()=Kon\u0161truktor s nulov\u00FDm argumentom pre komponent Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=Tento booleovsk\u00FD atrib\u00FAt riadi cel\u00FD k\u00F3d protokolovania Oracle JDBC. Ak m\u00E1 hodnotu nepravda, protokolov\u00E9 spr\u00E1vy sa nebud\u00FA vytv\u00E1ra\u0165. Ak m\u00E1 hodnotu pravda, protokolov\u00E9 spr\u00E1vy bud\u00FA riaden\u00E9 \u00FArov\u0148ami a filtrami java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Ovl\u00E1da\u010De Oracle JDBC nemo\u017Eno spusti\u0165 a zastavi\u0165. V\u017Edy sa vr\u00E1ti hodnota nepravda.

DiagnosabilityMBeanStatisticsProviderDescription=Ovl\u00E1da\u010De Oracle JDBC neposkytuj\u00FA \u0161tatistick\u00E9 \u00FAdaje prostredn\u00EDctvom komponentu Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Radi\u010D trasovania Clio.

DiagnosabilityMBeanSuspendDescription=Oper\u00E1cia pozastavenia Clio.

DiagnosabilityMBeanResumeDescription=Oper\u00E1cia obnovenia Clio.

DiagnosabilityMBeanTraceDescription=Oper\u00E1cia trasovania Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktivuje nepretr\u017Eit\u00E9 zabezpe\u010Den\u00E9 protokolovanie s\u00FAboru. Ak s\u00FA nastaven\u00E9 filtre ServiceName a UserName, mo\u017Eno ich pou\u017Ei\u0165. Nastavenie je predvolene deaktivovan\u00E9.

DiagnosabilityMBeanDisableContinousLoggingDescription=Deaktivuje nepretr\u017Eit\u00E9 zabezpe\u010Den\u00E9 protokolovanie s\u00FAboru. Nastavenie je predvolene deaktivovan\u00E9.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktivuje diagnostiku prv\u00E9ho zlyhania. Ak s\u00FA nastaven\u00E9 filtre ServiceName a UserName, mo\u017Eno ich pou\u017Ei\u0165. Vo\u013Eba je predvolene aktivovan\u00E1.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Deaktivuje diagnostiku prv\u00E9ho zlyhania. Funkcia je predvolene aktivovan\u00E1.

DiagnosabilityMBeanServiceNameFilterDescription=Ak je nastaven\u00FD filter ServiceName, protokolovanie pam\u00E4te alebo nepretr\u017Eit\u00E9 protokolovanie je aktivovan\u00E9 len pre pripojenia konfigurovanej slu\u017Eby.

DiagnosabilityMBeanUserFilterDescription=Ak je nastaven\u00FD filter User, protokolovanie pam\u00E4te alebo nepretr\u017Eit\u00E9 protokolovanie je aktivovan\u00E9 len pre pripojenia konfigurovan\u00E9ho pou\u017E\u00EDvate\u013Ea.

ReplayStatisticsMBeanDescription=Zobraz\u00ED \u0161tatistiku funkcie Application Continuity (AC).

ReplayStatisticsMBeanConstructor=Kon\u0161truktor s nulov\u00FDm argumentom pre objekt MBean \u0161tatistiky Oracle JDBC AC.

ReplayStatisticsMBeanAllStatisticsDescription=Z\u00EDska \u0161tatistiku AC pre v\u0161etky zn\u00E1me in\u0161tancie d\u00E1tov\u00E9ho zdroja ovl\u00E1da\u010Da.

ReplayStatisticsMBeanGetDSStatisticsDescription=Z\u00EDska \u0161tatistiku AC pre konkr\u00E9tnu in\u0161tanciu d\u00E1tov\u00E9ho zdroja ovl\u00E1da\u010Da.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Riadi funkcie diagnostiky ovl\u00E1da\u010Dov Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Tento boolovsk\u00FD atrib\u00FAt riadi cel\u00FD k\u00F3d protokolovania Oracle JDBC. Predvolen\u00E1 hodnota je nepravda. Ak je zapnut\u00FD, protokolov\u00E9 spr\u00E1vy bud\u00FA riaden\u00E9 \u00FArov\u0148ami a filtrami java.util.logging. Ak je vypnut\u00FD, nebud\u00FA sa vytv\u00E1ra\u0165 \u017Eiadne protokolov\u00E9 spr\u00E1vy.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Tento boolovsk\u00FD atrib\u00FAt riadi v\u0161etky metriky udalost\u00ED zaznamenan\u00FDch ovl\u00E1da\u010Dom JDBC. Predvolen\u00E1 hodnota je false. Ak je nastavenie aktivovan\u00E9, ovl\u00E1da\u010D zaznamen\u00E1va metriky udalost\u00ED, a\u017E k\u00FDm nastavenie nebude deaktivovan\u00E9.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Vr\u00E1ti hodnotu pravda, ak je aktivovan\u00FD z\u00E1pis protokolov do buffra pam\u00E4\u0165ov\u00E9ho trasovania. Predvolen\u00E1 hodnota je nepravda.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uvedenej predpony ID pripojenia, ak bola deaktivovan\u00E1.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uvedenej predpony ID pripojenia.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu, ak bola deaktivovan\u00E1.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera, ak bola deaktivovan\u00E1.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku prv\u00E9ho zlyhania, ak bola deaktivovan\u00E1.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnostika prv\u00E9ho zlyhania je predvolene aktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku prv\u00E9ho zlyhania.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia aktivuje protokolovanie pod\u013Ea uvedenej predpony ID pripojenia.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia deaktivuje protokolovanie pod\u013Ea uvedenej predpony ID pripojenia, ak bolo aktivovan\u00E9.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia aktivuje protokolovanie pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia deaktivuje protokolovanie pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu, ak bolo aktivovan\u00E9.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia aktivuje protokolovanie pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia deaktivuje protokolovanie pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera, ak bolo aktivovan\u00E9.

DiagnosticsEnableLoggingOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia aktivuje protokolovanie.

DiagnosticsDisableLoggingOperationDescription=Protokolovanie je predvolene deaktivovan\u00E9. T\u00E1to oper\u00E1cia deaktivuje protokolovanie, ak bolo aktivovan\u00E9.

DiagnosticsEnableMetricsOperationDescription=Spusti\u0165 zhroma\u017E\u010Fovanie metriky \u010Dasovania po\u010Das prip\u00E1jania.

DiagnosticsDisableMetricsOperationDescription=Zastavi\u0165 zhroma\u017E\u010Fovanie metriky \u010Dasovania po\u010Das prip\u00E1jania.

DiagnosticsShowMetricsOperationDescription=Zobrazi\u0165 metriku \u010Dasovania zhroma\u017Eden\u00FA po\u010Das prip\u00E1jania.

DiagnosticsClearMetricsOperationDescription=Vymaza\u0165 metriku \u010Dasovania zhroma\u017Eden\u00FA po\u010Das prip\u00E1jania.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uvedenej predpony ID pripojenia.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uvedenej predpony ID pripojenia, ak bola aktivovan\u00E1.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu, ak bola aktivovan\u00E1.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku citliv\u00FDch d\u00E1t pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera, ak bola aktivovan\u00E1.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia aktivuje diagnostiku citliv\u00FDch d\u00E1t.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Diagnostika citliv\u00FDch d\u00E1t je predvolene deaktivovan\u00E1. T\u00E1to oper\u00E1cia deaktivuje diagnostiku citliv\u00FDch d\u00E1t, ak bola aktivovan\u00E1.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Aktualizujte \u00FArove\u0148 diagnostiky pod\u013Ea uvedenej predpony ID pripojenia. Re\u0165azec argumentu m\u00F4\u017Ee pozost\u00E1va\u0165 bu\u010F z n\u00E1zvu \u00FArovne, alebo celo\u010D\u00EDselnej hodnoty. Pr\u00EDklad: SEVERE or 1000.

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Aktualizujte \u00FArove\u0148 diagnostiky pod\u013Ea uveden\u00E9ho n\u00E1zvu klienta. Re\u0165azec argumentu m\u00F4\u017Ee pozost\u00E1va\u0165 bu\u010F z n\u00E1zvu \u00FArovne, alebo celo\u010D\u00EDselnej hodnoty. Pr\u00EDklad: SEVERE or 1000.

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Aktualizujte \u00FArove\u0148 diagnostiky pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera. Re\u0165azec argumentu m\u00F4\u017Ee pozost\u00E1va\u0165 bu\u010F z n\u00E1zvu \u00FArovne, alebo celo\u010D\u00EDselnej hodnoty. Pr\u00EDklad: SEVERE or 1000.

DiagnosticsUpdateDiagnosticLevelOperationDescription=Aktualizujte \u00FArove\u0148 diagnostiky. Re\u0165azec argumentu m\u00F4\u017Ee pozost\u00E1va\u0165 bu\u010F z n\u00E1zvu \u00FArovne, alebo celo\u010D\u00EDselnej hodnoty. Pr\u00EDklad: SEVERE or 1000.

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Aktualizuje ve\u013Ekos\u0165 buffra diagnostiky prv\u00E9ho zlyhania pod\u013Ea uvedenej predpony ID pripojenia. Predvolen\u00E1 ve\u013Ekos\u0165 buffra je 4\u00A0000 z\u00E1znamov protokolu.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Aktualizuje ve\u013Ekos\u0165 buffra diagnostiky prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu n\u00E1jomcu. Predvolen\u00E1 ve\u013Ekos\u0165 buffra je 4\u00A0000 z\u00E1znamov protokolu.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Aktualizuje ve\u013Ekos\u0165 buffra diagnostiky prv\u00E9ho zlyhania pod\u013Ea uveden\u00E9ho n\u00E1zvu loggera. Predvolen\u00E1 ve\u013Ekos\u0165 buffra je 4\u00A0000 z\u00E1znamov protokolu.

DiagnosticsUpdateBufferSizeOperationDescription=Aktualizuje ve\u013Ekos\u0165 buffra diagnostiky prv\u00E9ho zlyhania. Predvolen\u00E1 ve\u013Ekos\u0165 buffra je 4\u00A0000 z\u00E1znamov protokolu.

DiagnosticsReadLoggingConfigFileOperationDescription=Znova inicializuje vlastnosti protokolovania a znova pre\u010D\u00EDta konfigur\u00E1ciu protokolovania z uveden\u00E9ho s\u00FAboru, ktor\u00FD by mal by\u0165 vo form\u00E1te java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Pridajte k\u00F3d chyby, ktorej \u010Fal\u0161\u00ED v\u00FDskyt m\u00E1 by\u0165 sledovan\u00FD, vo form\u00E1te ORA-XXXXX. Ovl\u00E1da\u010D JDBC zap\u00ED\u0161e diagnostiku prv\u00E9ho zlyhania do konfigurovan\u00E9ho obslu\u017En\u00E9ho programu protokolu, ke\u010F nastane chyba v zozname sledovan\u00FDch polo\u017Eiek.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Odstr\u00E1\u0148te uveden\u00FD k\u00F3d chyby zo zoznamu sledovan\u00FDch polo\u017Eiek. K\u00F3d chyby by mal by\u0165 vo form\u00E1te ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Zobraz\u00ED k\u00F3dy ch\u00FDb, ktor\u00E9 s\u00FA sledovan\u00E9.

DiagnosticsResetErrorCodesWatchListOperationDescription=Nakonfigurujte zoznam sledovan\u00FDch polo\u017Eiek s predvolen\u00FDmi k\u00F3dmi ch\u00FDb.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Vytvori\u0165 v\u00FDpis diagnostiky prv\u00E9ho zlyhania v cie\u013Eovom obslu\u017Enom programe.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Vytvor\u00ED v\u00FDpis diagnostiky prv\u00E9ho zlyhania, ak bude bud\u00FAca v\u00FDnimka obsahova\u0165 jedno z uveden\u00FDch k\u013E\u00FA\u010Dov\u00FDch slov. Pr\u00EDklady k\u013E\u00FA\u010Dov\u00FDch slov: reset, violation.

DiagnosticsShowExceptionKeywords=Predt\u00FDm pridan\u00E9 k\u013E\u00FA\u010Dov\u00E9 slov\u00E1 v\u00FDnimky.

DiagnosticsShowRecentOperations=Najnov\u0161ie oper\u00E1cie vykonan\u00E9 pou\u017E\u00EDvate\u013Eom v tomto objekte MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Vyma\u017Ee predt\u00FDm pridan\u00E9 k\u013E\u00FA\u010Dov\u00E9 slov\u00E1.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Predvolene sa protokoly zapisuj\u00FA do obslu\u017En\u00E9ho programu protokolu. T\u00E1to oper\u00E1cia aktivuje alebo deaktivuje z\u00E1pis protokolov do diagnostiky funkcie Diagnostika prv\u00E9ho zlyhania.

DiagnosticsConnectionIdPrefixParameterDescription=Predpona ID pripojenia.

DiagnosticsTenantNameParameterDescription=N\u00E1zov n\u00E1jomcu.

DiagnosticsLoggerNameParameterDescription=N\u00E1zov loggera.

DiagnosticsLevelParameterDescription=\u00DArove\u0148 loggera.

DiagnosticsBufferSizeParameterDescription=Ve\u013Ekos\u0165 buffra diagnostiky prv\u00E9ho zlyhania.

DiagnosticsConfigFileParameterDescription=Konfigura\u010Dn\u00FD s\u00FAbor protokolovania.

DiagnosticsErrorCodesParameterDescription=K\u00F3d chyby vo form\u00E1te ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Hodnota pravda alebo nepravda.

DiagnosticsCommaSeparatedKeywordsParameterDescription=K\u013E\u00FA\u010Dov\u00E9 slov\u00E1 ako hodnoty oddelen\u00E9 \u010Diarkou.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






