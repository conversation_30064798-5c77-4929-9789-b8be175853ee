"(x,y,z)": '(' "(x,y,z)___0" ')'
"(x,y,z)": '(' ')'
"(x,y,z)___0": "(x,y,z)___1" "expr_list"
"(x,y,z)___0": "expr_list"
"(x,y,z)___1": 'ALL'
"(x,y,z)___1": 'DISTINCT'
".+-.": '+'
".+-.": '-'
".exp.": 'E' ".+-." digits
".exp.": 'E' digits
".fd.": 'D'
".fd.": 'F'
"ExponentPart___0": 'E'
"ExponentPart___0": 'e'
"ExponentPart___1": '+'
"ExponentPart___1": '-'
"FLOAT___0": 'D'
"FLOAT___0": 'F'
"FLOAT___0": 'd'
"FLOAT___0": 'f'
"FLOAT___1": '.' digits
"FLOAT___1": digits '.'
"FLOAT___1": digits '.' digits
"FLOAT___2": "FLOAT___4"
"FLOAT___2": ExponentPart
"FLOAT___2": ExponentPart "FLOAT___3"
"FLOAT___3": 'D'
"FLOAT___3": 'F'
"FLOAT___3": 'd'
"FLOAT___3": 'f'
"FLOAT___4": 'D'
"FLOAT___4": 'F'
"FLOAT___4": 'd'
"FLOAT___4": 'f'
"JSON_ARRAY_content___0": "JSON_ARRAY_content___0" JSON_on_null_clause
"JSON_ARRAY_content___0": JSON_on_null_clause
"JSON_ARRAY_content___1": JSON_ARRAY_element
"JSON_ARRAY_content___1": JSON_ARRAY_element "JSON_ARRAY_content___2"
"JSON_ARRAY_content___2": "JSON_ARRAY_content___2" "JSON_ARRAY_content___2"
"JSON_ARRAY_content___2": ',' JSON_ARRAY_element
"JSON_OBJECT_content___0": 'EXTENDED'
"JSON_OBJECT_content___0": 'STRICT'
"JSON_OBJECT_content___1": "JSON_OBJECT_content___2" "JSON_OBJECT_content___3"
"JSON_OBJECT_content___2": 'WITH'
"JSON_OBJECT_content___2": 'WITHOUT'
"JSON_OBJECT_content___3": 'TYPENAME'
"JSON_OBJECT_content___3": 'UNIQUE'
"JSON_OBJECT_content___3": 'UNIQUE' 'KEYS'
"JSON_OBJECT_content___4": '*'
"JSON_OBJECT_content___4": entry
"JSON_OBJECT_content___4": entry "JSON_OBJECT_content___5"
"JSON_OBJECT_content___5": "JSON_OBJECT_content___5" "JSON_OBJECT_content___5"
"JSON_OBJECT_content___5": ',' entry
"JSON_TRANSFORM_returning_clause___0": 'ALLOW'
"JSON_TRANSFORM_returning_clause___0": 'DISALLOW'
"JSON_TRANSFORM_returning_clause___0": 'PRETTY'
"JSON_TRANSFORM_returning_clause___1": 'BLOB'
"JSON_TRANSFORM_returning_clause___1": 'CLOB'
"JSON_TRANSFORM_returning_clause___1": 'JSON'
"JSON_TRANSFORM_returning_clause___1": 'VARCHAR2'
"JSON_TRANSFORM_returning_clause___1": 'VARCHAR2' "JSON_TRANSFORM_returning_clause___2"
"JSON_TRANSFORM_returning_clause___2": '(' digits "JSON_TRANSFORM_returning_clause___3" ')'
"JSON_TRANSFORM_returning_clause___2": '(' digits ')'
"JSON_TRANSFORM_returning_clause___3": 'BYTE'
"JSON_TRANSFORM_returning_clause___3": 'CHAR'
"JSON_object_access_expr___0": "JSON_object_access_expr___0" "JSON_object_access_expr___0"
"JSON_object_access_expr___0": '.' identifier
"JSON_object_access_expr___0": array_step
"JSON_on_error_clause___0": 'ERROR'
"JSON_on_error_clause___0": 'NULL'
"JSON_on_null_clause___0": 'EMPTY'
"JSON_on_null_clause___0": 'NULL'
"JSON_on_null_clause___1": 'ABSENT'
"JSON_on_null_clause___1": 'NULL'
"JSON_on_null_clause___1": 'SQL' 'NULL'
"JSON_relative_object_access___0": "JSON_relative_object_access___0" "JSON_relative_object_access___0"
"JSON_relative_object_access___0": '.' identifier
"JSON_relative_object_access___0": '.' identifier array_step
"JSON_returning_clause___0": 'ETAG'
"JSON_returning_clause___0": 'REFERENCE'
"JSON_returning_clause___0": 'VALUE'
"aggr_name": 'APPROX_COUNT_DISTINCT'
"aggr_name": 'APPROX_COUNT_DISTINCT_AGG'
"aggr_name": 'APPROX_PERCENTILE_DETAIL'
"aggr_name": 'AVG'
"aggr_name": 'CORR'
"aggr_name": 'CORR_K'
"aggr_name": 'CORR_S'
"aggr_name": 'COUNT'
"aggr_name": 'COUNT' 'DISTINCT'
"aggr_name": 'COVAR_POP'
"aggr_name": 'COVAR_SAMP'
"aggr_name": 'FIRST'
"aggr_name": 'GROUPING'
"aggr_name": 'GROUPING_ID'
"aggr_name": 'GROUP_ID'
"aggr_name": 'LAST'
"aggr_name": 'MAX'
"aggr_name": 'MEDIAN'
"aggr_name": 'MIN'
"aggr_name": 'REGR_AVGX'
"aggr_name": 'REGR_AVGY'
"aggr_name": 'REGR_COUNT'
"aggr_name": 'REGR_INTERCEPT'
"aggr_name": 'REGR_R2'
"aggr_name": 'REGR_SLOPE'
"aggr_name": 'REGR_SXX'
"aggr_name": 'REGR_SXY'
"aggr_name": 'REGR_SYY'
"aggr_name": 'STATS_BINOMIAL_TEST'
"aggr_name": 'STATS_CROSSTAB'
"aggr_name": 'STATS_F_TEST'
"aggr_name": 'STATS_KS_TEST'
"aggr_name": 'STATS_MODE'
"aggr_name": 'STATS_MW_TEST'
"aggr_name": 'STATS_ONE_WAY_ANOVA'
"aggr_name": 'STATS_T_TEST_' '*'
"aggr_name": 'STATS_T_TEST_INDEP'
"aggr_name": 'STATS_T_TEST_INDEPU'
"aggr_name": 'STATS_T_TEST_ONE'
"aggr_name": 'STATS_T_TEST_PAIRED'
"aggr_name": 'STATS_WSR_TEST'
"aggr_name": 'STDDEV'
"aggr_name": 'STDDEV_POP'
"aggr_name": 'STDDEV_SAMP'
"aggr_name": 'SUM'
"aggr_name": 'SYS_XMLAGG'
"aggr_name": 'VARIANCE'
"aggr_name": 'VAR_POP'
"aggr_name": 'VAR_SAMP'
"aliased_expr": expr as_alias
"appendOp___0": "appendOp___1" 'ON' 'NULL'
"appendOp___1": 'ERROR'
"appendOp___1": 'IGNORE'
"appendOp___1": 'NULL'
"appendOp___2": "appendOp___3" 'ON' 'MISSING'
"appendOp___3": 'CREATE'
"appendOp___3": 'ERROR'
"appendOp___3": 'IGNORE'
"arrayValue___0": "arrayValue___0" value
"arrayValue___0": value
"array_step___0": "array_step___0" "array_step___0"
"array_step___0": ',' integer
"array_step___0": ',' integer "array_step___1"
"array_step___1": 'TO' integer
"array_step___2": '*'
"array_step___2": integer
"array_step___2": integer "array_step___0"
"array_step___2": integer "array_step___3"
"array_step___2": integer "array_step___3" "array_step___0"
"array_step___3": 'TO' integer
"as_alias": 'AS' c_alias
"as_alias": c_alias
"assignOp___0": "assignOp___1" 'ON' 'MISSING'
"assignOp___1": 'CREATE'
"assignOp___1": 'ERROR'
"assignOp___1": 'IGNORE'
"assignOp___2": "assignOp___3" 'ON' 'NULL'
"assignOp___3": 'ERROR'
"assignOp___3": 'IGNORE'
"assignOp___3": 'NULL'
"assignOp___4": "assignOp___5" 'ON' 'EXISTING'
"assignOp___5": 'ERROR'
"assignOp___5": 'IGNORE'
"assignOp___5": 'REPLACE'
"bind_var.INDICATOR_id.": bind_var
"bind_var.INDICATOR_id.": bind_var 'INDICATOR' bind_var
"bind_var.INDICATOR_id.": bind_var 'INDICATOR' identifier
"blockString___0": "blockString___0" REGULAR_STRING_PART
"blockString___0": REGULAR_STRING_PART
"column_tags_clause___0": "column_tags_clause___0" json_col_tags
"column_tags_clause___0": json_col_tags
"column_tags_clause___1": "column_tags_clause___1" "column_tags_clause___1"
"column_tags_clause___1": ',' json_col_tags
"column_tags_clause___2": "column_tags_clause___2" "column_tags_clause___2"
"column_tags_clause___2": ',' json_col_tags
"column_tags_clause___3": "column_tags_clause___3" json_col_tags
"column_tags_clause___3": json_col_tags
"cond_or_expr": comparison_condition
"cond_or_expr": expr
"cond_or_expr": model_condition
"entry___0": regular_entry
"entry___0": regular_entry JSON_on_null_clause
"entry___0": regular_entry column_tags_clause
"entry___0": regular_entry column_tags_clause JSON_on_null_clause
"entry___0": regular_entry column_tags_clause format_clause
"entry___0": regular_entry column_tags_clause format_clause JSON_on_null_clause
"entry___0": regular_entry format_clause
"entry___0": regular_entry format_clause JSON_on_null_clause
"expr[c_alias]": expr
"expr[c_alias]": expr c_alias
"expr_list": "expr_list" ',' 'DISTINCT' expr
"expr_list": "expr_list" ',' expr
"expr_list": "expr_list" ',' expr 'DETERMINISTIC'
"expr_list": 'DISTINCT' expr
"expr_list": expr
"expr_list": expr 'DETERMINISTIC'
"expr_list_def": "expr_list_def___2"
"expr_list_def": "expr_list_def___2" "expr_list_def___0"
"expr_list_def___0": "expr_list_def___0" ',' "expr_list_def___1"
"expr_list_def___0": ',' "expr_list_def___1"
"expr_list_def___1": 'DEFAULT'
"expr_list_def___1": expr
"expr_list_def___2": 'DEFAULT'
"expr_list_def___2": expr
"expression_list": "expr_list"
"expression_list": '(' "expr_list" ')'
"expression_list": '(' ')'
"gby+having": group_by_clause
"gby+having": group_by_clause having_clause
"gby+having": having_clause
"gby+having": having_clause group_by_clause
"inner_cross_join_clause": "inner_cross_join_clause___0" 'JOIN' table_reference_or_join_clause
"inner_cross_join_clause": 'INNER' 'JOIN' table_reference_or_join_clause on_using_condition
"inner_cross_join_clause": 'JOIN' table_reference_or_join_clause on_using_condition
"inner_cross_join_clause___0": 'CROSS'
"inner_cross_join_clause___0": 'NATURAL'
"inner_cross_join_clause___0": 'NATURAL' 'INNER'
"insertOp___0": "insertOp___1" 'ON' "insertOp___2"
"insertOp___1": 'ERROR'
"insertOp___1": 'IGNORE'
"insertOp___1": 'NULL'
"insertOp___1": 'REMOVE'
"insertOp___2": 'EXISTING'
"insertOp___2": 'MISSING'
"insertOp___2": 'NULL'
"json_col_tags___0": 'CHECK'
"json_col_tags___0": 'NOCHECK'
"json_obj_tags___0": 'CHECK'
"json_obj_tags___0": 'NOCHECK'
"json_object___0": 'JSON'
"json_object___0": 'JSON_OBJECT'
"json_object___1": 'JSON'
"json_object___1": 'JSON_OBJECT'
"json_object___2": 'BSON'
"json_object___2": 'JSON'
"json_object___2": 'JSON_OBJECT'
"json_object___2": 'OSON'
"json_object___3": 'JSON'
"json_object___3": 'JSON_OBJECT'
"json_oper___0": appendOp
"json_oper___0": assignOp
"json_oper___0": insertOp
"json_oper___0": keepOp
"json_oper___0": mergeOp
"json_oper___0": removeOp
"json_oper___0": renameOp
"json_oper___0": replaceOp
"json_oper___0": setOp
"json_oper___0": sortOp
"json_oper___0": unionOp
"json_table___0": ',' JSON_basic_path_expression
"json_table___1": 'NULL' 'ON' 'MISMATCH'
"json_table___2": 'JSON_EXPAND_ARRAY'
"json_table___2": 'JSON_TABLE'
"json_transform___0": "json_transform___0" "json_transform___0"
"json_transform___0": ',' "json_oper___0"
"jsonize___0": 'JSONIZE'
"jsonize___0": 'SHELL'
"keepOp___0": "keepOp___0" "keepOp___0"
"keepOp___0": ',' string_literal
"keepOp___0": ',' string_literal "keepOp___1"
"keepOp___1": "keepOp___2" 'ON' 'MISSING'
"keepOp___2": 'ERROR'
"keepOp___2": 'IGNORE'
"keepOp___3": string_literal
"keepOp___3": string_literal "keepOp___4"
"keepOp___4": "keepOp___5" 'ON' 'MISSING'
"keepOp___5": 'ERROR'
"keepOp___5": 'IGNORE'
"key_value_clause___0": '(' duality_subquery ')'
"key_value_clause___0": '[' duality_subquery ']'
"key_value_clause___0": identifier
"key_value_clause___0": identifier column_tags_clause
"key_value_clause___1": ':'
"key_value_clause___1": 'IS'
"label_expression___0": '&'
"label_expression___0": '|'
"legalfilename": "legalfilename" fname_separator filenamefragment
"legalfilename": filenamefragment
"nested_clause_1___0": ',' JSON_basic_path_expression
"nested_clause_1___1": 'NESTED'
"nested_clause_1___1": 'NESTED' 'PATH'
"nested_clause_1___2": JSON_relative_object_access
"nested_clause_1___2": identifier "nested_clause_1___0"
"objectValue___0": "objectValue___0" objectField
"objectValue___0": objectField
"object_gen_clause___0": "object_gen_clause___0" "object_gen_clause___0"
"object_gen_clause___0": ',' key_value_clause
"objectname___0": "objectname___0" "objectname___0"
"objectname___0": filename '/'
"ord_by_1desc": expr
"ord_by_1desc": expr "ord_by_1desc___0"
"ord_by_1desc": expr "ord_by_1desc___1"
"ord_by_1desc": expr "ord_by_1desc___1" "ord_by_1desc___0"
"ord_by_1desc___0": 'NULLS' 'FIRST'
"ord_by_1desc___0": 'NULLS' 'LAST'
"ord_by_1desc___1": 'ASC'
"ord_by_1desc___1": 'DESC'
"outer_join_clause": "outer_join_clause___0" table_reference_or_join_clause
"outer_join_clause": "outer_join_clause___0" table_reference_or_join_clause on_using_condition
"outer_join_clause": "outer_join_clause___0" table_reference_or_join_clause query_partition_clause
"outer_join_clause": "outer_join_clause___0" table_reference_or_join_clause query_partition_clause on_using_condition
"outer_join_clause": query_partition_clause "outer_join_clause___0" table_reference_or_join_clause
"outer_join_clause": query_partition_clause "outer_join_clause___0" table_reference_or_join_clause on_using_condition
"outer_join_clause": query_partition_clause "outer_join_clause___0" table_reference_or_join_clause query_partition_clause
"outer_join_clause": query_partition_clause "outer_join_clause___0" table_reference_or_join_clause query_partition_clause on_using_condition
"outer_join_clause___0": 'INNER' 'JOIN'
"outer_join_clause___0": 'JOIN'
"outer_join_clause___0": 'NATURAL' 'JOIN'
"outer_join_clause___0": 'NATURAL' outer_join_type 'JOIN'
"outer_join_clause___0": outer_join_type 'JOIN'
"path_pattern_list___0": "path_pattern_list___0" "path_pattern_list___0"
"path_pattern_list___0": ',' path_term
"qualifier___0": '/' 'B' '/' filename
"regular_entry___0": ':' expr
"removeOp___0": "removeOp___1" 'ON' 'MISSING'
"removeOp___1": 'ERROR'
"removeOp___1": 'IGNORE'
"renameOp___0": "renameOp___1" 'ON' 'MISSING'
"renameOp___1": 'ERROR'
"renameOp___1": 'IGNORE'
"renameOp___2": '='
"renameOp___2": 'WITH'
"replaceOp___0": "replaceOp___1" 'ON' 'NULL'
"replaceOp___1": 'ERROR'
"replaceOp___1": 'IGNORE'
"replaceOp___1": 'NULL'
"replaceOp___1": 'REMOVE'
"replaceOp___2": "replaceOp___3" 'ON' 'MISSING'
"replaceOp___3": 'CREATE'
"replaceOp___3": 'ERROR'
"replaceOp___3": 'IGNORE'
"rhsExpr___0": 'FORMAT' 'JSON'
"setOp___0": "setOp___1" 'ON' 'MISSING'
"setOp___1": 'CREATE'
"setOp___1": 'ERROR'
"setOp___1": 'IGNORE'
"setOp___2": "setOp___3" 'ON' 'NULL'
"setOp___3": 'ERROR'
"setOp___3": 'IGNORE'
"setOp___3": 'NULL'
"setOp___4": 'LET'
"setOp___4": 'SET'
"table_tags_clause___0": "table_tags_clause___0" json_obj_tags
"table_tags_clause___0": json_obj_tags
"table_tags_clause___1": "table_tags_clause___1" "table_tags_clause___1"
"table_tags_clause___1": ',' json_obj_tags
"table_tags_clause___2": "table_tags_clause___2" "table_tags_clause___2"
"table_tags_clause___2": ',' json_obj_tags
"table_tags_clause___3": "table_tags_clause___3" json_obj_tags
"table_tags_clause___3": json_obj_tags
"templatePlaceholder___0": "templatePlaceholder___0" TEMPLATE_CHAR
"templatePlaceholder___0": TEMPLATE_CHAR
"transform_directive___0": 'EXISTING'
"transform_directive___0": 'MISSING'
"transform_directive___0": 'NULL'
"transform_directive___1": "transform_directive___2" 'ON' "transform_directive___0"
"transform_directive___2": 'CREATE'
"transform_directive___2": 'CREATE' 'OBJECT'
"transform_directive___2": 'ERROR'
"transform_directive___2": 'IGNORE'
"transform_directive___2": 'REPLACE'
"unionOp___0": "unionOp___1" 'ON' 'MISSING'
"unionOp___1": 'CREATE'
"unionOp___1": 'ERROR'
"unionOp___1": 'IGNORE'
"unionOp___2": "unionOp___3" 'ON' 'NULL'
"unionOp___3": 'ERROR'
"unionOp___3": 'IGNORE'
"unionOp___3": 'NULL'
"unionOp___4": "unionOp___5" 'ON' 'EXISTING'
"unionOp___5": 'ERROR'
"unionOp___5": 'IGNORE'
"unionOp___5": 'REPLACE'
"where,gby,hier": "gby+having"
"where,gby,hier": "gby+having" hierarchical_query_clause
"where,gby,hier": hierarchical_query_clause
"where,gby,hier": hierarchical_query_clause "gby+having"
"where,gby,hier": hierarchical_query_clause where_clause
"where,gby,hier": where_clause
"where,gby,hier": where_clause "gby+having"
"where,gby,hier": where_clause "gby+having" hierarchical_query_clause
"where,gby,hier": where_clause hierarchical_query_clause
"where,gby,hier": where_clause hierarchical_query_clause "gby+having"
"wildcard___0": identifier '.'
"{,expr[c_alias]}...": "{,expr[c_alias]}..." "{,expr[c_alias]}...#"
"{,expr[c_alias]}...": "{,expr[c_alias]}...#"
"{,expr[c_alias]}...#": ',' "expr[c_alias]"
AND_OR: 'AND'
AND_OR: 'OR'
ANSI_supported_datatypes: 'CHARACTER' '(' digits ')'
ANSI_supported_datatypes: 'CHARACTER' 'VARYING' '(' digits ')'
ANSI_supported_datatypes: 'DOUBLE' 'PRECISION'
ANSI_supported_datatypes: 'FLOAT'
ANSI_supported_datatypes: 'FLOAT' ANSI_supported_datatypes___2
ANSI_supported_datatypes: 'INT'
ANSI_supported_datatypes: 'INTEGER'
ANSI_supported_datatypes: 'NATIONAL' 'CHAR'
ANSI_supported_datatypes: 'NATIONAL' 'CHARACTER'
ANSI_supported_datatypes: 'NATIONAL' ANSI_supported_datatypes___4 '(' digits ')'
ANSI_supported_datatypes: 'NATIONAL' ANSI_supported_datatypes___4 'VARYING' '(' digits ')'
ANSI_supported_datatypes: 'REAL'
ANSI_supported_datatypes: 'SMALLINT'
ANSI_supported_datatypes: 'VARCHAR' '(' digits ')'
ANSI_supported_datatypes: ANSI_supported_datatypes___0
ANSI_supported_datatypes: ANSI_supported_datatypes___0 ANSI_supported_datatypes___1
ANSI_supported_datatypes: ANSI_supported_datatypes___3 'VARYING' '(' digits ')'
ANSI_supported_datatypes___0: 'DEC'
ANSI_supported_datatypes___0: 'DECIMAL'
ANSI_supported_datatypes___0: 'NUMERIC'
ANSI_supported_datatypes___1: '(' precision ')'
ANSI_supported_datatypes___1: '(' precision ',' scale ')'
ANSI_supported_datatypes___2: '(' digits ')'
ANSI_supported_datatypes___3: 'CHAR'
ANSI_supported_datatypes___3: 'NCHAR'
ANSI_supported_datatypes___4: 'CHAR'
ANSI_supported_datatypes___4: 'CHARACTER'
Any_types: 'SYS' '.' 'ANYDATA'
Any_types: 'SYS' '.' 'ANYDATASET'
Any_types: 'SYS' '.' 'ANYTYPE'
BULK_COLLECT_opt: 'BULK' 'COLLECT'
ELSE_expr_opt: 'ELSE' pls_expr
EXP_pri_opt: '*' '*' pri
ExponentPart: "ExponentPart___0" "ExponentPart___1" digits
ExponentPart: "ExponentPart___0" digits
FLOAT: "FLOAT___1"
FLOAT: "FLOAT___1" "FLOAT___0"
FLOAT: "FLOAT___1" ExponentPart
FLOAT: "FLOAT___1" ExponentPart "FLOAT___0"
FLOAT: digits "FLOAT___2"
JSON_ARRAY_content: "JSON_ARRAY_content___0"
JSON_ARRAY_content: "JSON_ARRAY_content___0" 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___0" 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___0" JSON_returning_clause
JSON_ARRAY_content: "JSON_ARRAY_content___0" JSON_returning_clause 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___0" JSON_returning_clause 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___0" JSON_returning_clause format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___0" format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1"
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0"
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" JSON_returning_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" JSON_returning_clause 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" JSON_returning_clause 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" JSON_returning_clause format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" "JSON_ARRAY_content___0" format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___1" 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" JSON_returning_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" JSON_returning_clause 'STRICT'
JSON_ARRAY_content: "JSON_ARRAY_content___1" JSON_returning_clause 'STRICT' format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" JSON_returning_clause format_clause
JSON_ARRAY_content: "JSON_ARRAY_content___1" format_clause
JSON_ARRAY_content: 'STRICT'
JSON_ARRAY_content: 'STRICT' format_clause
JSON_ARRAY_content: JSON_returning_clause
JSON_ARRAY_content: JSON_returning_clause 'STRICT'
JSON_ARRAY_content: JSON_returning_clause 'STRICT' format_clause
JSON_ARRAY_content: JSON_returning_clause format_clause
JSON_ARRAY_content: format_clause
JSON_ARRAY_element: JSON_ARRAY
JSON_ARRAY_element: expr
JSON_ARRAY_element: expr format_clause
JSON_ARRAY_element: json_object
JSON_OBJECT_content: "JSON_OBJECT_content___0"
JSON_OBJECT_content: "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4"
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___0"
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_on_null_clause JSON_returning_clause JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: "JSON_OBJECT_content___4" JSON_returning_clause JSON_on_error_clause
JSON_OBJECT_content: JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_on_null_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_on_null_clause JSON_returning_clause JSON_on_error_clause
JSON_OBJECT_content: JSON_returning_clause
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___0"
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___0" "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___0" JSON_on_error_clause
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___1"
JSON_OBJECT_content: JSON_returning_clause "JSON_OBJECT_content___1" JSON_on_error_clause
JSON_OBJECT_content: JSON_returning_clause JSON_on_error_clause
JSON_TRANSFORM_returning_clause: 'RETURNING' "JSON_TRANSFORM_returning_clause___1"
JSON_TRANSFORM_returning_clause: 'RETURNING' "JSON_TRANSFORM_returning_clause___1" "JSON_TRANSFORM_returning_clause___0"
JSON_basic_path_expression: 'STREAM' string_literal
JSON_basic_path_expression: string_literal
JSON_basic_path_expression: string_literal JSON_query_returning_clause identifier
JSON_basic_path_expression: string_literal identifier
JSON_condition: JSON_exists_condition
JSON_condition: JSON_textcontains_condition
JSON_condition: is_JSON_condition
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_passing_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_passing_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_passing_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_passing_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_empty_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause ')'
JSON_exists_condition: 'JSON_EXISTS' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression JSON_passing_clause JSON_exists_on_error_clause JSON_exists_on_empty_clause ')'
JSON_exists_on_empty_clause: 'TYPE' '(' 'STRICT' ')'
JSON_exists_on_empty_clause: JSON_exists_on_empty_clause___0 'ON' 'EMPTY'
JSON_exists_on_empty_clause___0: 'ERROR'
JSON_exists_on_empty_clause___0: 'FALSE'
JSON_exists_on_empty_clause___0: 'TRUE'
JSON_exists_on_error_clause: JSON_exists_on_error_clause___0 'ON' 'ERROR'
JSON_exists_on_error_clause___0: 'ERROR'
JSON_exists_on_error_clause___0: 'FALSE'
JSON_exists_on_error_clause___0: 'TRUE'
JSON_function: json_array
JSON_function: json_arrayagg
JSON_function: json_dataguide
JSON_function: json_mergepatch
JSON_function: json_object
JSON_function: json_object format_clause
JSON_function: json_objectagg
JSON_function: json_query
JSON_function: json_scalar
JSON_function: json_serialize
JSON_function: json_table
JSON_function: json_transform
JSON_function: json_value
JSON_object_access_expr: identifier '.' identifier "JSON_object_access_expr___0"
JSON_on_error_clause: "JSON_on_error_clause___0" 'ON' 'ERROR'
JSON_on_no_rows_clause: 'EMPTY' 'ON' 'NO' 'ROWS'
JSON_on_null_clause: "JSON_on_null_clause___1" 'ON' "JSON_on_null_clause___0"
JSON_passing_clause: 'PASSING' expr 'AS' identifier
JSON_passing_clause: 'PASSING' expr 'AS' identifier JSON_passing_clause___0
JSON_passing_clause___0: JSON_passing_clause___0 JSON_passing_clause___0#
JSON_passing_clause___0: JSON_passing_clause___0#
JSON_passing_clause___0#: ',' expr 'AS' identifier
JSON_patch_expr: expr
JSON_patch_expr: expr format_clause
JSON_query_on_empty_clause: JSON_query_on_empty_clause___0 'ON' 'EMPTY'
JSON_query_on_empty_clause___0: 'EMPTY'
JSON_query_on_empty_clause___0: 'EMPTY' 'ARRAY'
JSON_query_on_empty_clause___0: 'EMPTY' 'OBJECT'
JSON_query_on_empty_clause___0: 'ERROR'
JSON_query_on_empty_clause___0: 'NULL'
JSON_query_on_error_clause: JSON_query_on_error_clause___0 'ON' 'ERROR'
JSON_query_on_error_clause___0: 'EMPTY'
JSON_query_on_error_clause___0: 'EMPTY' 'ARRAY'
JSON_query_on_error_clause___0: 'EMPTY' 'OBJECT'
JSON_query_on_error_clause___0: 'ERROR'
JSON_query_on_error_clause___0: 'NULL'
JSON_query_on_mismatch_clause: JSON_query_on_mismatch_clause___0 'ON' 'MISMATCH'
JSON_query_on_mismatch_clause___0: 'ERROR'
JSON_query_on_mismatch_clause___0: 'NULL'
JSON_query_returning_clause: 'RETURNING' JSON_value_return_type
JSON_query_returning_clause: 'RETURNING' JSON_value_return_type JSON_query_returning_clause___0
JSON_query_returning_clause: JSON_query_returning_clause___0
JSON_query_returning_clause___0: 'ASCII'
JSON_query_returning_clause___0: 'PRETTY'
JSON_query_returning_clause___0: 'PRETTY' 'ASCII'
JSON_query_returning_clause___0: JSON_query_returning_clause___1 'SCALARS'
JSON_query_returning_clause___0: JSON_query_returning_clause___1 'SCALARS' 'ASCII'
JSON_query_returning_clause___0: JSON_query_returning_clause___1 'SCALARS' 'PRETTY'
JSON_query_returning_clause___0: JSON_query_returning_clause___1 'SCALARS' 'PRETTY' 'ASCII'
JSON_query_returning_clause___1: 'ALLOW'
JSON_query_returning_clause___1: 'DISALLOW'
JSON_query_wrapper_clause: 'WITH' 'ARRAY' 'WRAPPER'
JSON_query_wrapper_clause: 'WITH' 'WRAPPER'
JSON_query_wrapper_clause: 'WITH' JSON_query_wrapper_clause___0 'ARRAY' 'WRAPPER'
JSON_query_wrapper_clause: 'WITH' JSON_query_wrapper_clause___0 'WRAPPER'
JSON_query_wrapper_clause: 'WITHOUT' 'ARRAY' 'WRAPPER'
JSON_query_wrapper_clause: 'WITHOUT' 'WRAPPER'
JSON_query_wrapper_clause___0: 'CONDITIONAL'
JSON_query_wrapper_clause___0: 'UNCONDITIONAL'
JSON_relative_object_access: identifier
JSON_relative_object_access: identifier "JSON_relative_object_access___0"
JSON_relative_object_access: identifier array_step
JSON_relative_object_access: identifier array_step "JSON_relative_object_access___0"
JSON_returning_clause: 'RETURNING' datatype
JSON_returning_clause: 'RETURNING' datatype "JSON_returning_clause___0"
JSON_returning_clause: 'RETURNING' datatype "JSON_returning_clause___0" 'INCLUDE'
JSON_returning_clause: 'RETURNING' datatype "JSON_returning_clause___0" 'INCLUDE' format_clause
JSON_returning_clause: 'RETURNING' datatype "JSON_returning_clause___0" format_clause
JSON_returning_clause: 'RETURNING' datatype 'INCLUDE'
JSON_returning_clause: 'RETURNING' datatype 'INCLUDE' format_clause
JSON_returning_clause: 'RETURNING' datatype format_clause
JSON_table_on_empty_clause: JSON_table_on_empty_clause___0 'ON' 'EMPTY'
JSON_table_on_empty_clause___0: 'ERROR'
JSON_table_on_empty_clause___0: 'NULL'
JSON_table_on_error_clause: JSON_table_on_error_clause___0 'ON' 'ERROR'
JSON_table_on_error_clause___0: 'DEFAULT' expr
JSON_table_on_error_clause___0: 'ERROR'
JSON_table_on_error_clause___0: 'NULL'
JSON_target_expr: expr
JSON_target_expr: expr format_clause
JSON_textcontains_condition: 'JSON_TEXTCONTAINS' '(' column ',' JSON_basic_path_expression ',' string ')'
JSON_value_mapper_clause: 'USING' 'CASE' '-' 'INSENSITIVE' 'MAPPING'
JSON_value_mapper_clause: 'USING' 'CASE' '-' 'SENSITIVE' 'MAPPING'
JSON_value_mapper_clause: 'USING' 'CASE_SENSITIVE' 'MAPPING'
JSON_value_on_empty_clause: JSON_value_on_empty_clause___1 'ON' JSON_value_on_empty_clause___0
JSON_value_on_empty_clause___0: 'EMPTY'
JSON_value_on_empty_clause___0: 'NO' 'ROWS'
JSON_value_on_empty_clause___1: 'DEFAULT' literal
JSON_value_on_empty_clause___1: 'EMPTY'
JSON_value_on_empty_clause___1: 'EMPTY' JSON_value_on_empty_clause___2
JSON_value_on_empty_clause___1: 'ERROR'
JSON_value_on_empty_clause___1: 'NULL'
JSON_value_on_empty_clause___1: 'SQL' 'NULL'
JSON_value_on_empty_clause___2: 'ARRAY'
JSON_value_on_empty_clause___2: 'OBJECT'
JSON_value_on_error_clause: JSON_value_on_error_clause___0 'ON' 'ERROR'
JSON_value_on_error_clause___0: 'DEFAULT' literal
JSON_value_on_error_clause___0: 'EMPTY'
JSON_value_on_error_clause___0: 'EMPTY' JSON_value_on_error_clause___1
JSON_value_on_error_clause___0: 'ERROR'
JSON_value_on_error_clause___0: 'NULL'
JSON_value_on_error_clause___0: 'SQL' 'NULL'
JSON_value_on_error_clause___1: 'ARRAY'
JSON_value_on_error_clause___1: 'OBJECT'
JSON_value_on_mismatch_clause: JSON_value_on_mismatch_clause JSON_value_on_mismatch_clause#
JSON_value_on_mismatch_clause: JSON_value_on_mismatch_clause#
JSON_value_on_mismatch_clause#: '(' 'IGNORE'
JSON_value_on_mismatch_clause#: 'ERROR'
JSON_value_on_mismatch_clause#: 'NULL' ')' 'ON' 'MISMATCH'
JSON_value_on_mismatch_clause#: 'NULL' ')' 'ON' 'MISMATCH' JSON_value_on_mismatch_clause___4
JSON_value_on_mismatch_clause#: JSON_value_on_mismatch_clause___5 'ON' 'MISMATCH'
JSON_value_on_mismatch_clause#: JSON_value_on_mismatch_clause___5 'ON' 'MISMATCH' JSON_value_on_mismatch_clause___0
JSON_value_on_mismatch_clause___0: '(' JSON_value_on_mismatch_clause___1 ')'
JSON_value_on_mismatch_clause___0: '(' JSON_value_on_mismatch_clause___1 JSON_value_on_mismatch_clause___2 ')'
JSON_value_on_mismatch_clause___1: 'EXTRA' 'DATA'
JSON_value_on_mismatch_clause___1: 'MISSING' 'DATA'
JSON_value_on_mismatch_clause___1: 'TYPE' 'ERROR'
JSON_value_on_mismatch_clause___2: ',' JSON_value_on_mismatch_clause___3
JSON_value_on_mismatch_clause___2: JSON_value_on_mismatch_clause___2 ',' JSON_value_on_mismatch_clause___3
JSON_value_on_mismatch_clause___3: 'EXTRA' 'DATA'
JSON_value_on_mismatch_clause___3: 'MISSING' 'DATA'
JSON_value_on_mismatch_clause___3: 'TYPE' 'ERROR'
JSON_value_on_mismatch_clause___4: '(' '(' 'MISSING' 'DATA' ')'
JSON_value_on_mismatch_clause___4: '(' 'EXTRA' 'DATA' ')'
JSON_value_on_mismatch_clause___4: '(' 'TYPE' 'ERROR' ')' ')'
JSON_value_on_mismatch_clause___5: 'ERROR'
JSON_value_on_mismatch_clause___5: 'IGNORE'
JSON_value_on_mismatch_clause___5: 'NULL'
JSON_value_return_object_instance: object_type_name
JSON_value_return_object_instance: object_type_name JSON_value_mapper_clause
JSON_value_return_type: 'ANY' JSON_value_return_object_instance
JSON_value_return_type: 'ASCII'
JSON_value_return_type: 'BLOB'
JSON_value_return_type: 'BOOLEAN'
JSON_value_return_type: 'BOOLEAN' JSON_value_return_type___9
JSON_value_return_type: 'CLOB'
JSON_value_return_type: 'DATE'
JSON_value_return_type: 'DATE' JSON_value_return_type___5 'TIME'
JSON_value_return_type: 'INTEGER'
JSON_value_return_type: 'INTERVAL' 'DAY' 'TO' 'SECOND'
JSON_value_return_type: 'INTERVAL' 'YEAR' 'TO' 'MONTH'
JSON_value_return_type: 'JSON'
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 'ASCII'
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 'ASCII' JSON_value_return_type___11
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 JSON_value_return_type___7
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 JSON_value_return_type___7 'ASCII'
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 JSON_value_return_type___7 'ASCII' JSON_value_return_type___11
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 JSON_value_return_type___7 JSON_value_return_type___11
JSON_value_return_type: 'NATIONAL' JSON_value_return_type___2 JSON_value_return_type___11
JSON_value_return_type: 'NUMBER'
JSON_value_return_type: 'NUMBER' 'ORDERED'
JSON_value_return_type: 'NUMBER' JSON_value_return_type___0
JSON_value_return_type: 'NUMBER' JSON_value_return_type___0 JSON_value_return_type___1
JSON_value_return_type: 'NUMBER' JSON_value_return_type___1
JSON_value_return_type: 'NUMBER' JSON_value_return_type___3
JSON_value_return_type: 'NUMBER' JSON_value_return_type___3 JSON_value_return_type___4
JSON_value_return_type: 'NUMBER' JSON_value_return_type___4
JSON_value_return_type: 'RAW'
JSON_value_return_type: 'RAW' 'WITH' 'TYPENAME'
JSON_value_return_type: 'RAW' JSON_value_return_type___6
JSON_value_return_type: 'RAW' JSON_value_return_type___6 'WITH' 'TYPENAME'
JSON_value_return_type: 'SDO_GEOMETRY'
JSON_value_return_type: 'SDO_GEOMETRY' 'WITH' 'VALIDATION'
JSON_value_return_type: 'TIMESTAMP'
JSON_value_return_type: 'TIMESTAMP' 'WITH' 'LOCAL' 'TIME' 'ZONE'
JSON_value_return_type: 'TIMESTAMP' 'WITH' 'TIME' 'ZONE'
JSON_value_return_type: JSON_value_return_object_instance
JSON_value_return_type: JSON_value_return_type___2
JSON_value_return_type: JSON_value_return_type___2 'ASCII'
JSON_value_return_type: JSON_value_return_type___2 'ASCII' JSON_value_return_type___11
JSON_value_return_type: JSON_value_return_type___2 JSON_value_return_type___7
JSON_value_return_type: JSON_value_return_type___2 JSON_value_return_type___7 'ASCII'
JSON_value_return_type: JSON_value_return_type___2 JSON_value_return_type___7 'ASCII' JSON_value_return_type___11
JSON_value_return_type: JSON_value_return_type___2 JSON_value_return_type___7 JSON_value_return_type___11
JSON_value_return_type: JSON_value_return_type___2 JSON_value_return_type___11
JSON_value_return_type: allow_boolean_number_conversion
JSON_value_return_type___0: '(' precision ')'
JSON_value_return_type___0: '(' precision ',' scale ')'
JSON_value_return_type___1: 'DEFAULT' expr 'ON' 'ERROR'
JSON_value_return_type___2: 'CHAR'
JSON_value_return_type___2: 'CHARACTER'
JSON_value_return_type___2: 'VARCHAR'
JSON_value_return_type___2: 'VARCHAR2'
JSON_value_return_type___3: '(' precision ')'
JSON_value_return_type___3: '(' precision ',' scale ')'
JSON_value_return_type___4: 'DISALLOW' 'BOOL' 'TO' 'NUMBER'
JSON_value_return_type___5: 'PRESERVE'
JSON_value_return_type___5: 'TRUNCATE'
JSON_value_return_type___6: '(' precision ')'
JSON_value_return_type___6: '(' precision ',' scale ')'
JSON_value_return_type___7: '(' digits ')'
JSON_value_return_type___7: '(' digits JSON_value_return_type___8 ')'
JSON_value_return_type___7: 'VARYING'
JSON_value_return_type___7: 'VARYING' JSON_value_return_type___10
JSON_value_return_type___8: 'BYTE'
JSON_value_return_type___8: 'CHAR'
JSON_value_return_type___9: 'TYPE' '(' 'STRICT' ')'
JSON_value_return_type___10: '(' digits ')'
JSON_value_return_type___11: 'PRETTY'
JSON_value_return_type___11: 'TRUNCATE'
JSON_value_returning_clause: 'ASCII'
JSON_value_returning_clause: 'RETURNING' JSON_value_return_type
JSON_value_returning_clause: 'RETURNING' JSON_value_return_type 'ASCII'
MODEL_kw: 'MODEL'
MODEL_kw: 'SPREADSHEET'
NAME: identifier '-' digits
NAME: identifier digits
NOT_NULL_opt: 'NOT' 'NULL'
NOT_NULL_opt: 'NULL'
Oracle_built_in_datatypes: boolean_datatype
Oracle_built_in_datatypes: character_datatypes
Oracle_built_in_datatypes: datetime_datatypes
Oracle_built_in_datatypes: json_datatype
Oracle_built_in_datatypes: large_object_datatypes
Oracle_built_in_datatypes: long_and_raw_datatypes
Oracle_built_in_datatypes: number_datatypes
Oracle_built_in_datatypes: rowid_datatypes
Oracle_supplied_types: XML_types
Oracle_supplied_types: any_types
Oracle_supplied_types: media_types
Oracle_supplied_types: spatial_types
Rowid_datatypes: 'ROWID'
Rowid_datatypes: 'UROWID'
Rowid_datatypes: 'UROWID' Rowid_datatypes___0
Rowid_datatypes___0: '(' digits ')'
SET_OPER: 'EXCEPT'
SET_OPER: 'EXCEPT' 'ALL'
SET_OPER: 'INTERSECT'
SET_OPER: 'INTERSECT' 'ALL'
SET_OPER: 'MINUS'
SET_OPER: 'MINUS' 'ALL'
SET_OPER: 'UNION'
SET_OPER: 'UNION' 'ALL'
Spatial_types: 'SDO_GEOMETRY'
Spatial_types: 'SDO_GEORASTER'
Spatial_types: 'SDO_TOPO_GEOMETRY'
VARIABLE_NAME: '$' NAME
XMLTABLE_options: XMLTABLE_options___0
XMLTABLE_options: XMLTABLE_options___2
XMLTABLE_options: XMLTABLE_options___2 XMLTABLE_options___0
XMLTABLE_options: XML_passing_clause
XMLTABLE_options: XML_passing_clause XMLTABLE_options___0
XMLTABLE_options: XML_passing_clause XMLTABLE_options___2
XMLTABLE_options: XML_passing_clause XMLTABLE_options___2 XMLTABLE_options___0
XMLTABLE_options___0: 'COLUMNS' XML_table_column
XMLTABLE_options___0: 'COLUMNS' XML_table_column XMLTABLE_options___1
XMLTABLE_options___1: ',' XML_table_column
XMLTABLE_options___1: XMLTABLE_options___1 ',' XML_table_column
XMLTABLE_options___2: 'RETURNING' 'SEQUENCE' 'BY' 'REF'
XML_attributes_clause: 'XMLATTRIBUTES' '(' XML_attributes_clause___0 aux_xml_value_expr ')'
XML_attributes_clause: 'XMLATTRIBUTES' '(' XML_attributes_clause___0 aux_xml_value_expr XML_attributes_clause___3 ')'
XML_attributes_clause: 'XMLATTRIBUTES' '(' aux_xml_value_expr ')'
XML_attributes_clause: 'XMLATTRIBUTES' '(' aux_xml_value_expr XML_attributes_clause___3 ')'
XML_attributes_clause___0: XML_attributes_clause___1
XML_attributes_clause___0: XML_attributes_clause___1 XML_attributes_clause___2
XML_attributes_clause___0: XML_attributes_clause___2
XML_attributes_clause___1: 'ENTITYESCAPING'
XML_attributes_clause___1: 'NOENTITYESCAPING'
XML_attributes_clause___2: 'NOSCHEMACHECK'
XML_attributes_clause___2: 'SCHEMACHECK'
XML_attributes_clause___3: ',' aux_xml_value_expr
XML_attributes_clause___3: XML_attributes_clause___3 ',' aux_xml_value_expr
XML_condition: expr 'NOT' 'XMLTYPE'
XML_condition: xmlexists
XML_function: xmlcast
XML_function: xmlcolattval
XML_function: xmlelement
XML_function: xmlexists
XML_function: xmlforest
XML_function: xmlparse
XML_function: xmlpi
XML_function: xmlquery
XML_function: xmlroot
XML_function: xmlserialize
XML_function: xmltable
XML_passing_clause: 'PASSING' 'BY' 'REF' expr
XML_passing_clause: 'PASSING' 'BY' 'REF' expr 'AS' identifier
XML_passing_clause: 'PASSING' 'BY' 'REF' expr 'AS' identifier XML_passing_clause___0
XML_passing_clause: 'PASSING' 'BY' 'REF' expr XML_passing_clause___0
XML_passing_clause: 'PASSING' 'BY' 'VALUE' expr
XML_passing_clause: 'PASSING' 'BY' 'VALUE' expr 'AS' identifier
XML_passing_clause: 'PASSING' 'BY' 'VALUE' expr 'AS' identifier XML_passing_clause___0
XML_passing_clause: 'PASSING' 'BY' 'VALUE' expr XML_passing_clause___0
XML_passing_clause: 'PASSING' expr
XML_passing_clause: 'PASSING' expr 'AS' identifier
XML_passing_clause: 'PASSING' expr 'AS' identifier XML_passing_clause___0
XML_passing_clause: 'PASSING' expr XML_passing_clause___0
XML_passing_clause___0: XML_passing_clause___0 XML_passing_clause___0#
XML_passing_clause___0: XML_passing_clause___0#
XML_passing_clause___0#: ',' expr
XML_passing_clause___0#: ',' expr 'AS' identifier
XML_table_column: column
XML_table_column: column XML_table_column___3
XML_table_column___0: 'XMLTYPE'
XML_table_column___0: 'XMLTYPE' XML_table_column___1
XML_table_column___0: datatype
XML_table_column___1: '(' 'SEQUENCE' ')' 'BY' 'REF'
XML_table_column___2: 'EXISTS' 'PATH' string
XML_table_column___2: 'PATH' string
XML_table_column___3: 'DEFAULT' expr
XML_table_column___3: 'FOR' 'ORDINALITY'
XML_table_column___3: XML_table_column___0
XML_table_column___3: XML_table_column___0 'DEFAULT' expr
XML_table_column___3: XML_table_column___0 XML_table_column___2
XML_table_column___3: XML_table_column___0 XML_table_column___2 'DEFAULT' expr
XML_table_column___3: XML_table_column___2
XML_table_column___3: XML_table_column___2 'DEFAULT' expr
XML_types: 'URITYPE'
XML_types: 'XMLTYPE'
XMLnamespaces_clause: 'XMLNAMESPACES' '(' XMLnamespaces_clause___0 ')'
XMLnamespaces_clause: 'XMLNAMESPACES' '(' XMLnamespaces_clause___0 XMLnamespaces_clause___1 ')'
XMLnamespaces_clause___0: 'DEFAULT' string
XMLnamespaces_clause___0: expr 'AS' identifier
XMLnamespaces_clause___1: ',' XMLnamespaces_clause___2
XMLnamespaces_clause___1: XMLnamespaces_clause___1 ',' XMLnamespaces_clause___2
XMLnamespaces_clause___2: 'DEFAULT' string
XMLnamespaces_clause___2: expr 'AS' identifier
a_f: identifier '(' "expr_list" ')' 'OVER'
a_f: identifier '(' "expr_list" ')' 'OVER' '(' ')'
a_f: identifier '(' "expr_list" ')' 'OVER' '(' analytic_clause ')'
a_f: identifier '(' "expr_list" ')' 'OVER' analytic_clause
a_f: identifier '(' "expr_list" ')' keep_clause 'OVER'
a_f: identifier '(' "expr_list" ')' keep_clause 'OVER' '(' ')'
a_f: identifier '(' "expr_list" ')' keep_clause 'OVER' '(' analytic_clause ')'
a_f: identifier '(' "expr_list" ')' keep_clause 'OVER' analytic_clause
a_f: identifier '(' ')' 'OVER'
a_f: identifier '(' ')' 'OVER' '(' ')'
a_f: identifier '(' ')' 'OVER' '(' analytic_clause ')'
a_f: identifier '(' ')' 'OVER' analytic_clause
a_f: identifier '(' ')' keep_clause 'OVER'
a_f: identifier '(' ')' keep_clause 'OVER' '(' ')'
a_f: identifier '(' ')' keep_clause 'OVER' '(' analytic_clause ')'
a_f: identifier '(' ')' keep_clause 'OVER' analytic_clause
abbreviated_edge_pattern: '-'
abbreviated_edge_pattern: '-' '>'
abbreviated_edge_pattern: '<' '-'
add_meas_clause: 'ADD' 'MEASURES' '(' cube_meas ')'
add_meas_clause: 'ADD' 'MEASURES' '(' cube_meas add_meas_clause___0 ')'
add_meas_clause___0: ',' cube_meas
add_meas_clause___0: add_meas_clause___0 ',' cube_meas
aggregate_function: aggregate_function___0
aggregate_function: aggregate_function___0 keep_clause
aggregate_function: aggregate_function___1
aggregate_function: aggregate_function___1 keep_clause
aggregate_function: approx_count
aggregate_function: approx_median
aggregate_function: approx_percentile
aggregate_function: approx_rank
aggregate_function: approx_sum
aggregate_function: collect
aggregate_function: count
aggregate_function: cume_dist_aggregate
aggregate_function: dense_rank_aggregate
aggregate_function: listagg
aggregate_function: opt_FINAL_RUNNING aggregate_function___0
aggregate_function: opt_FINAL_RUNNING aggregate_function___0 keep_clause
aggregate_function: percent_rank_aggregate
aggregate_function: percentile_cont
aggregate_function: percentile_disc
aggregate_function: rank_aggregate
aggregate_function: user_defined_function keep_clause
aggregate_function___0: "aggr_name" '(' "expr_list" ')'
aggregate_function___0: xmlagg
aggregate_function___1: "aggr_name" '(' "expr_list" ')'
aggregate_function___1: xmlagg
alias: identifier
alias: identifier ':'
alias: name
aliased_dml_table_expression_clause: aliased_dml_table_expression_clause___0
aliased_dml_table_expression_clause: aliased_dml_table_expression_clause___0 identifier
aliased_dml_table_expression_clause___0: 'ONLY' '(' dml_table_expression_clause ')'
aliased_dml_table_expression_clause___0: dml_table_expression_clause
alldatetime_d: datetime_d
alldatetime_d: interval_d
allow_boolean_number_conversion: allow_boolean_number_conversion___1 'TO' 'NUMBER'
allow_boolean_number_conversion: allow_boolean_number_conversion___1 'TO' 'NUMBER' 'CONVERSION'
allow_boolean_number_conversion: allow_boolean_number_conversion___1 allow_boolean_number_conversion___0 'TO' 'NUMBER'
allow_boolean_number_conversion: allow_boolean_number_conversion___1 allow_boolean_number_conversion___0 'TO' 'NUMBER' 'CONVERSION'
allow_boolean_number_conversion___0: 'BOOL'
allow_boolean_number_conversion___0: 'BOOLEAN'
allow_boolean_number_conversion___1: 'ALLOW'
allow_boolean_number_conversion___1: 'DISALLOW'
allow_disallow_strict_lax_JSON: '(' 'LAX' ')'
allow_disallow_strict_lax_JSON: 'ALLOW' 'SCALARS'
allow_disallow_strict_lax_JSON: 'DISALLOW' 'SCALARS'
allow_disallow_strict_lax_JSON: 'IS' 'JSON' 'VALIDATE' 'USING' string_literal
allow_disallow_strict_lax_JSON: 'IS' 'JSON' 'VALIDATE' string_literal
allow_disallow_strict_lax_JSON: 'JSON' 'VALIDATE' 'USING' string_literal
allow_disallow_strict_lax_JSON: 'JSON' 'VALIDATE' string_literal
allow_disallow_strict_lax_JSON: 'LAX'
allow_disallow_strict_lax_JSON: 'SIZE' 'LIMIT' digits
allow_disallow_strict_lax_JSON: 'SIZE' 'LIMIT' digits allow_disallow_strict_lax_JSON___2
allow_disallow_strict_lax_JSON: 'STRICT'
allow_disallow_strict_lax_JSON: allow_disallow_strict_lax_JSON___0 allow_disallow_strict_lax_JSON___1
allow_disallow_strict_lax_JSON___0: 'WITH'
allow_disallow_strict_lax_JSON___0: 'WITHOUT'
allow_disallow_strict_lax_JSON___1: 'UNIQUE'
allow_disallow_strict_lax_JSON___1: 'UNIQUE' 'KEYS'
allow_disallow_strict_lax_JSON___2: 'K'
allow_disallow_strict_lax_JSON___2: 'M'
allow_scalars_opt: 'ALLOW' 'SCALARS'
analytic_clause: analytic_clause___0
analytic_clause: analytic_clause___0 order_by_clause
analytic_clause: analytic_clause___0 order_by_clause windowing_clause
analytic_clause: analytic_clause___0 windowing_clause
analytic_clause: order_by_clause
analytic_clause: order_by_clause windowing_clause
analytic_clause: rank_clause
analytic_clause: windowing_clause
analytic_clause___0: identifier
analytic_clause___0: query_partition_clause
analytic_function: a_f
analytic_function: approx_median
analytic_function: approx_percentile
analytic_function: cluster_details_analytic
analytic_function: cluster_distance_analytic
analytic_function: cluster_id_analytic
analytic_function: count_analytic
analytic_function: first_last_value
analytic_function: lag
analytic_function: lead
analytic_function: listagg
analytic_function: max
analytic_function: min
analytic_function: nth_value
analytic_function: prediction_analytic
analytic_function: prediction_details_analytic
analytic_function: prediction_prob_analytic
analytic_function: sum
and_expr: and_expr 'AND' rel
and_expr: rel
annotation_element: annotation_element___0 identifier
annotation_element: annotation_element___0 identifier expr
annotation_element: identifier
annotation_element: identifier expr
annotation_element___0: 'ADD'
annotation_element___0: 'ADD' annotation_element___1
annotation_element___0: 'DROP'
annotation_element___0: 'DROP' 'IF' 'EXISTS'
annotation_element___0: 'REPLACE'
annotation_element___1: 'IF' 'NOT' 'EXISTS'
annotations_clause: 'ANNOTATIONS' '(' annotation_element ')'
annotations_clause: 'ANNOTATIONS' '(' annotation_element annotations_clause___0 ')'
annotations_clause___0: ',' annotation_element
annotations_clause___0: annotations_clause___0 ',' annotation_element
anomalouS_eXpreSsion: 'FOR' expr
anomalouS_eXpreSsion: 'OF' 'ANOMALY'
anomalouS_eXpreSsion: 'OF' 'ANOMALY' expr
anomalouS_eXpreSsion: expr
any_types: 'SYS' '.' 'ANYDATA'
any_types: 'SYS' '.' 'ANYDATASET'
any_types: 'SYS' '.' 'ANYTYPE'
appendOp: 'APPEND' string_literal '=' rhsExpr
appendOp: 'APPEND' string_literal '=' rhsExpr "appendOp___0"
appendOp: 'APPEND' string_literal '=' rhsExpr "appendOp___2"
appendOp: 'APPEND' string_literal '=' rhsExpr "appendOp___2" "appendOp___0"
approx_count: 'APPROX_COUNT' '(' count_arg ')'
approx_count: 'APPROX_COUNT_DISTINCT' '(' count_arg ')'
approx_median: 'APPROX_MEDIAN' '(' expr ')'
approx_median: 'APPROX_MEDIAN' '(' expr ',' string_literal ')'
approx_median: 'APPROX_MEDIAN' '(' expr 'DETERMINISTIC' ')'
approx_median: 'APPROX_MEDIAN' '(' expr 'DETERMINISTIC' ',' string_literal ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___0 approx_percentile___1 ')'
approx_percentile: 'APPROX_PERCENTILE' '(' expr 'DETERMINISTIC' ',' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr approx_percentile___1 ')'
approx_percentile___0: 'ASC'
approx_percentile___0: 'DESC'
approx_percentile___1: 'NULLS' 'FIRST'
approx_percentile___1: 'NULLS' 'LAST'
approx_rank: 'APPROX_RANK' '(' ')'
approx_rank: 'APPROX_RANK' '(' approx_rank___0 ')'
approx_rank: 'APPROX_RANK' '(' approx_rank___0 order_by_clause ')'
approx_rank: 'APPROX_RANK' '(' expr ')'
approx_rank: 'APPROX_RANK' '(' expr approx_rank___0 ')'
approx_rank: 'APPROX_RANK' '(' expr approx_rank___0 order_by_clause ')'
approx_rank: 'APPROX_RANK' '(' expr order_by_clause ')'
approx_rank: 'APPROX_RANK' '(' order_by_clause ')'
approx_rank___0: 'PARTITION' 'BY' expr
approx_sum: 'APPROX_SUM' '(' approx_sum___0 ')'
approx_sum: 'APPROX_SUM' '(' approx_sum___0 ',' string_literal ')'
approx_sum___0: '*'
approx_sum___0: expr
arg: assoc_arg
arg: pls_expr
arg_list: ',' arg
arg_list: arg_list ',' arg
argument: 'FROM' ':' value
argument: expr
argument: identifier ':' value
argument: name ':' value
arith_expr: arith_expr binary_add_op term
arith_expr: term
arrayValue: '[' "arrayValue___0" ']'
arrayValue: '[' ']'
array_step: '(' ')'
array_step: '[' "array_step___2" ']'
as_alias: 'AS' c_alias
as_alias: c_alias
assignOp: 'ASSIGN' string_literal '=' rhsExpr
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___0"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___0" "assignOp___2"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___2"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___4"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___4" "assignOp___0"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___4" "assignOp___0" "assignOp___2"
assignOp: 'ASSIGN' string_literal '=' rhsExpr "assignOp___4" "assignOp___2"
assoc_arg: 'FOR' iterator '=' '>' pls_expr
assoc_arg: 'FOR' iterator 'INDEX' pls_expr '=' '>' pls_expr
assoc_arg: 'FOR' iterator 'SEQUENCE' '=' '>' pls_expr
assoc_arg: assoc_name_list '=' '>' pls_expr
assoc_arg: sim_expr '=' '>' expr
assoc_name: sim_expr
assoc_name: sim_expr_DBLDOT__sim_expr
assoc_name_list: assoc_name
assoc_name_list: assoc_name_list '|' assoc_name
attribute: 'SQL' '%' attribute_designator
attribute: name '%' attribute_designator
attribute_designator: 'TYPE'
attribute_designator: identifier
aux_xml_value_expr: expr
aux_xml_value_expr: expr 'AS' aux_xml_value_expr___0
aux_xml_value_expr: expr aux_xml_value_expr___0
aux_xml_value_expr___0: 'EVALNAME' expr
aux_xml_value_expr___0: c_alias
av_hier_expression: hier_function_name '(' member_expression 'WITHIN' 'HIERARCHY' hierarchy_ref ')'
av_meas_expression: av_window_expression
av_meas_expression: lead_lag_expression
av_meas_expression: qdr_expression
av_meas_expression: rank_expression
av_meas_expression: share_of_expression
av_simple_expression: 'NULL'
av_simple_expression: measure_ref
av_simple_expression: numeric_literal
av_simple_expression: string_literal
av_window_clause: 'HIERARCHY' hierarchy_ref 'BETWEEN' av_window_clause___0
av_window_clause: 'HIERARCHY' hierarchy_ref 'BETWEEN' av_window_clause___0 'WITHIN' av_window_clause___1
av_window_clause___0: following_boundary
av_window_clause___0: preceding_boundary
av_window_clause___1: 'ANCESTOR' 'AT' 'LEVEL' identifier
av_window_clause___1: 'LEVEL'
av_window_clause___1: 'PARENT'
av_window_expression: aggregate_function 'OVER' '(' av_window_clause ')'
base_meas_clause: 'FACT' exposed_fact_column_name meas_aggregate_clause
between_condition: expr 'BETWEEN' expr 'AND' expr
between_condition: expr 'NOT' 'BETWEEN' expr 'AND' expr
binary_add_op: '&'
binary_add_op: '+'
binary_add_op: '-'
binary_add_op: '|' '|'
bind_var: ':' digits
bind_var: ':' identifier
bind_var: ':' identifier '.' identifier
bind_var: ':' identifier ':' identifier
bind_var: '?'
bind_var: 'DATE' ':' identifier
bind_var: 'TIME' ':' identifier
blockString: OPEN_TRIPLE_QUOTE "blockString___0" CLOSING_TRIPLE_QUOTE
blockString: OPEN_TRIPLE_QUOTE CLOSING_TRIPLE_QUOTE
booleanValue: 'false'
booleanValue: 'true'
boolean_primary: 'FALSE'
boolean_primary: 'TRUE'
boolean_primary: comparison_predicate
boolean_primary: condition
boolean_primary: function_expression
boolean_primary: is_json
boolean_primary: is_of_predicate
boolean_primary: member_predicate
boolean_primary: sim_expr
boolean_primary: sim_expr relal_op_sim_expr_opt
boolean_primary: submultiset_predicate
boolean_test_condition: condition 'IS' 'NOT' boolean_test_condition___0
boolean_test_condition: condition 'IS' boolean_test_condition___0
boolean_test_condition___0: 'FALSE'
boolean_test_condition___0: 'TRUE'
c_alias: 'AS' identifier
c_alias: identifier
c_external_ops: 'AGENT' 'IN' '(' identifier ')'
c_external_ops: 'CALLING' 'STANDARD' external_calling_standard
c_external_ops: 'LANGUAGE' 'C'
c_external_ops: 'LIBRARY' name
c_external_ops: 'PARAMETER' 'STYLE' external_parameter_style_token
c_external_ops: 'PARAMETERS'
c_external_ops: 'PARAMETERS' external_parameter_list
c_external_ops: 'WITH' 'CONTEXT'
c_external_ops: c_external_ops 'AGENT' 'IN' '(' identifier ')'
c_external_ops: c_external_ops 'CALLING' 'STANDARD' external_calling_standard
c_external_ops: c_external_ops 'LANGUAGE' 'C'
c_external_ops: c_external_ops 'LIBRARY' name
c_external_ops: c_external_ops 'PARAMETER' 'STYLE' external_parameter_style_token
c_external_ops: c_external_ops 'PARAMETERS'
c_external_ops: c_external_ops 'PARAMETERS' external_parameter_list
c_external_ops: c_external_ops 'WITH' 'CONTEXT'
c_external_ops: c_external_ops external_name_ops
c_external_ops: external_name_ops
calc_meas_expression: av_hier_expression
calc_meas_expression: av_meas_expression
calc_meas_expression: av_simple_expression
calc_meas_expression: case_expression
calc_meas_expression: compound_expression
calc_meas_expression: datetime_expression
calc_meas_expression: expr
calc_meas_expression: interval_expression
calc_meas_expression: single_row_function_expression
calc_meas_order_by_clause: calc_meas_expression
calc_meas_order_by_clause: calc_meas_expression 'NULLS' calc_meas_order_by_clause___0
calc_meas_order_by_clause: calc_meas_expression calc_meas_order_by_clause___1
calc_meas_order_by_clause: calc_meas_expression calc_meas_order_by_clause___1 'NULLS' calc_meas_order_by_clause___0
calc_meas_order_by_clause___0: 'FIRST'
calc_meas_order_by_clause___0: 'LAST'
calc_meas_order_by_clause___1: 'ASC'
calc_meas_order_by_clause___1: 'DESC'
calc_measure_clause: 'AS' '(' calc_meas_expression ')'
captureable_datetime_identifiers: 'DATE'
captureable_datetime_identifiers: 'INTERVAL'
captureable_datetime_identifiers: 'TIME'
captureable_datetime_identifiers: 'TIMESTAMP'
cartesian_product: cartesian_product ',' table_reference_or_join_clause
cartesian_product: table_reference_or_join_clause
case_choice: case_dangling_expr
case_choice: pls_expr
case_choice_list: case_choice
case_choice_list: case_choice_list ',' case_choice
case_dangling_expr: dangling_between_predicate
case_dangling_expr: dangling_binary_expr
case_dangling_expr: dangling_dangling_predicate
case_dangling_expr: dangling_empty_predicate
case_dangling_expr: dangling_in_predicate
case_dangling_expr: dangling_infinite_predicate
case_dangling_expr: dangling_like_predicate
case_dangling_expr: dangling_member_of_predicate
case_dangling_expr: dangling_nan_predicate
case_dangling_expr: dangling_null_predicate
case_dangling_expr: dangling_set_predicate
case_dangling_expr: dangling_submultiset_predicate
case_expr: 'CASE' case_expr_alt_seq 'END'
case_expr: 'CASE' case_expr_alt_seq ELSE_expr_opt 'END'
case_expr: 'CASE' pls_expr case_expr_alt_seq 'END'
case_expr: 'CASE' pls_expr case_expr_alt_seq ELSE_expr_opt 'END'
case_expr_alt: 'WHEN' case_choice_list 'THEN' pls_expr
case_expr_alt_seq: case_expr_alt
case_expr_alt_seq: case_expr_alt_seq case_expr_alt
case_expression: 'CASE' case_expression___0 'END'
case_expression: 'CASE' case_expression___0 else_clause 'END'
case_expression___0: searched_case_expression
case_expression___0: simple_case_expression
cast: 'CAST' '(' cast___0 'AS' datatype ')'
cast: 'CAST' '(' cast___0 'AS' datatype cast___1 ')'
cast: 'CAST' '(' cast___0 'AS' datatype cast___1 cast___3 ')'
cast: 'CAST' '(' cast___0 'AS' datatype cast___3 ')'
cast___0: 'MULTISET' scalar_subquery_expression
cast___0: expr
cast___1: 'DEFAULT' expr cast___2
cast___2: 'ON' 'CONVERSION' 'ERROR'
cast___3: ',' string_literal
cast___3: ',' string_literal ',' string_literal
cast_conversion_error: 'DEFAULT' sim_expr 'ON' 'CONVERSION' 'ERROR'
cast_nls_params: ',' sim_expr
cast_nls_params: ',' sim_expr ',' sim_expr
cell_assignment: column '[' cell_assignment___0 ']'
cell_assignment___0: cell_assignment___1
cell_assignment___0: cell_assignment___1 cell_assignment___2
cell_assignment___0: multi_column_for_loop
cell_assignment___1: condition
cell_assignment___1: expr
cell_assignment___1: single_column_for_loop
cell_assignment___2: ',' cell_assignment___3
cell_assignment___2: cell_assignment___2 ',' cell_assignment___3
cell_assignment___3: condition
cell_assignment___3: expr
cell_assignment___3: single_column_for_loop
cell_reference_options: 'UNIQUE' cell_reference_options___1
cell_reference_options: cell_reference_options___0 'NAV'
cell_reference_options: cell_reference_options___0 'NAV' 'UNIQUE' cell_reference_options___1
cell_reference_options___0: 'IGNORE'
cell_reference_options___0: 'KEEP'
cell_reference_options___1: 'DIMENSION'
cell_reference_options___1: 'SINGLE' 'REFERENCE'
character_datatypes: 'CHAR'
character_datatypes: 'CHAR' character_datatypes___0
character_datatypes: 'NCHAR'
character_datatypes: 'NCHAR' character_datatypes___3
character_datatypes: 'NVARCHAR2' '(' digits ')'
character_datatypes: 'VARCHAR2'
character_datatypes: 'VARCHAR2' '(' digits ')'
character_datatypes: 'VARCHAR2' '(' digits character_datatypes___2 ')'
character_datatypes___0: '(' digits ')'
character_datatypes___0: '(' digits character_datatypes___1 ')'
character_datatypes___1: 'BYTE'
character_datatypes___1: 'CHAR'
character_datatypes___2: 'BYTE'
character_datatypes___2: 'CHAR'
character_datatypes___3: '(' digits ')'
character_function: chr
character_function: treat
character_function: trim
charset_csname: bind_var
charset_csname: identifier
charset_csname: link_expanded_n '%' 'CHARSET'
charset_spec_opt: 'CHARACTER' 'SET' charset_csname
chr: 'CHR' '(' numeric_literal ')'
chr: 'CHR' '(' numeric_literal 'USING' 'NCHAR_CS' ')'
cluster_details: 'CLUSTER_DETAILS' '(' identifier '.' model cluster_details___0 cluster_details___1 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' identifier '.' model cluster_details___0 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' identifier '.' model cluster_details___1 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' identifier '.' model mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' model cluster_details___0 cluster_details___1 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' model cluster_details___0 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' model cluster_details___1 mining_attribute_clause ')'
cluster_details: 'CLUSTER_DETAILS' '(' model mining_attribute_clause ')'
cluster_details___0: ',' cluster_id
cluster_details___0: ',' cluster_id opt_topn
cluster_details___1: 'ABS'
cluster_details___1: 'ASC'
cluster_details___1: 'DESC'
cluster_details_analytic: 'CLUSTER_DETAILS' mining_analytic_body
cluster_distance: 'CLUSTER_DISTANCE' '(' identifier '.' model ',' cluster_id mining_attribute_clause ')'
cluster_distance: 'CLUSTER_DISTANCE' '(' identifier '.' model mining_attribute_clause ')'
cluster_distance: 'CLUSTER_DISTANCE' '(' model ',' cluster_id mining_attribute_clause ')'
cluster_distance: 'CLUSTER_DISTANCE' '(' model mining_attribute_clause ')'
cluster_distance_analytic: 'CLUSTER_DISTANCE' mining_analytic_body
cluster_id: 'CLUSTER_ID' '(' identifier '.' model mining_attribute_clause ')'
cluster_id: 'CLUSTER_ID' '(' model mining_attribute_clause ')'
cluster_id: identifier
cluster_id: identifier '.' identifier
cluster_id: literal
cluster_id1: 'CLUSTER_ID' '(' identifier '.' model mining_attribute_clause ')'
cluster_id1: 'CLUSTER_ID' '(' model mining_attribute_clause ')'
cluster_id_analytic: 'CLUSTER_ID' mining_analytic_body
cluster_prob_analytic: 'CLUSTER_PROBABILITY' mining_analytic_body
cluster_probability: 'CLUSTER_PROBABILITY' '(' identifier '.' model ',' literal mining_attribute_clause ')'
cluster_probability: 'CLUSTER_PROBABILITY' '(' identifier '.' model mining_attribute_clause ')'
cluster_probability: 'CLUSTER_PROBABILITY' '(' model ',' literal mining_attribute_clause ')'
cluster_probability: 'CLUSTER_PROBABILITY' '(' model mining_attribute_clause ')'
cluster_set: 'CLUSTER_SET' '(' identifier '.' model cluster_set___0 mining_attribute_clause ')'
cluster_set: 'CLUSTER_SET' '(' identifier '.' model mining_attribute_clause ')'
cluster_set: 'CLUSTER_SET' '(' model cluster_set___0 mining_attribute_clause ')'
cluster_set: 'CLUSTER_SET' '(' model mining_attribute_clause ')'
cluster_set___0: ',' expr
cluster_set___0: ',' expr ',' expr
cluster_set_analytic: 'CLUSTER_SET' mining_analytic_body
cmp_op: '!' '='
cmp_op: '='
cmp_op: '^' '='
cmp_op: '~' '='
cmp_op: not_eq
col_oj: column '(' '+' ')'
collect: 'COLLECT' '(' collect___0 expr ')'
collect: 'COLLECT' '(' collect___0 expr collect___1 ')'
collect: 'COLLECT' '(' expr ')'
collect: 'COLLECT' '(' expr collect___1 ')'
collect___0: 'DISTINCT'
collect___0: 'UNIQUE'
collect___1: 'ORDER' 'BY' expr_desc_asc_nulls
collect___1: 'ORDER' 'BY' expr_desc_asc_nulls collect___2
collect___2: ',' expr_desc_asc_nulls
collect___2: collect___2 ',' expr_desc_asc_nulls
collection_expression: expr
collection_expression: subquery
colmapped_query_name: identifier
colmapped_query_name: identifier colmapped_query_name___0
colmapped_query_name___0: '(' c_alias ')'
colmapped_query_name___0: '(' c_alias colmapped_query_name___1 ')'
colmapped_query_name___1: ',' c_alias
colmapped_query_name___1: colmapped_query_name___1 ',' c_alias
column: column___0 identifier
column: identifier
column: identifier '.' identifier '.' identifier '.' identifier
column: identifier '.' identifier '.' table
column___0: identifier '.' table '.'
column___0: table '.'
column_tags_clause: 'WITH' '(' json_col_tags "column_tags_clause___0" ')'
column_tags_clause: 'WITH' '(' json_col_tags "column_tags_clause___2" ')'
column_tags_clause: 'WITH' '(' json_col_tags ')'
column_tags_clause: 'WITH' json_col_tags
column_tags_clause: 'WITH' json_col_tags "column_tags_clause___1"
column_tags_clause: 'WITH' json_col_tags "column_tags_clause___3"
combinable_multiset_expr: combinable_multiset_expr multiset_op_union_except combinable_multiset_term
combinable_multiset_expr: combinable_multiset_term
combinable_multiset_primary: pri 'MULTISET'
combinable_multiset_term: combinable_multiset_primary
combinable_multiset_term: combinable_multiset_term multiset_op_intersect combinable_multiset_primary
comparison_condition: 'CURRENT' 'OF' identifier
comparison_condition: 'CURRENT' 'OF' identifier '.' identifier
comparison_condition: between_condition
comparison_condition: column comparison_condition___0
comparison_condition: group_comparison_condition
comparison_condition: lnnvl
comparison_condition: simple_comparison_condition
comparison_condition___0: 'IS' 'DANGLING'
comparison_condition___0: 'IS' 'NOT' 'DANGLING'
compound_condition: '(' condition ')'
compound_condition: 'NOT' condition
compound_condition: condition AND_OR condition
compound_condition_w_boolean: '(' compound_condition_w_boolean ')'
compound_condition_w_boolean: compound_condition_w_boolean AND_OR compound_condition_w_boolean
compound_condition_w_boolean: condition
compound_condition_w_boolean: expr
compound_expression: '(' expr ')'
compound_expression: compound_expression___1 expr
compound_expression: expr 'COLLATE' identifier
compound_expression: expr compound_expression___0 expr
compound_expression___0: '*'
compound_expression___0: '+'
compound_expression___0: '-'
compound_expression___0: '/'
compound_expression___0: '|' '|'
compound_expression___1: '+'
compound_expression___1: '-'
compound_expression___1: 'PRIOR'
condition: JSON_condition
condition: XML_condition
condition: boolean_test_condition
condition: comparison_condition
condition: compound_condition
condition: exists_condition
condition: floating_point_condition
condition: function_expression
condition: in_condition
condition: is_of_type_condition
condition: like_condition
condition: logical_condition
condition: model_condition
condition: multiset_condition
condition: null_condition
condition: overlaps_condition
condition: range_condition
condition: simple_expression
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___5
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___3 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___5
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___5 conditional_insert_clause___9
conditional_insert_clause: conditional_insert_clause___2 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___9
conditional_insert_clause___0: conditional_insert_clause___0 conditional_insert_clause___0#
conditional_insert_clause___0: conditional_insert_clause___0#
conditional_insert_clause___0#: insert_into_clause
conditional_insert_clause___0#: insert_into_clause error_logging_clause
conditional_insert_clause___0#: insert_into_clause values_clause
conditional_insert_clause___0#: insert_into_clause values_clause error_logging_clause
conditional_insert_clause___2: 'ALL'
conditional_insert_clause___2: 'FIRST'
conditional_insert_clause___3: conditional_insert_clause___3 conditional_insert_clause___1#
conditional_insert_clause___3: conditional_insert_clause___1#
conditional_insert_clause___1#: insert_into_clause
conditional_insert_clause___1#: insert_into_clause error_logging_clause
conditional_insert_clause___1#: insert_into_clause values_clause
conditional_insert_clause___1#: insert_into_clause values_clause error_logging_clause
conditional_insert_clause___5: conditional_insert_clause___5 conditional_insert_clause___2#
conditional_insert_clause___5: conditional_insert_clause___2#
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause conditional_insert_clause___7
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause error_logging_clause conditional_insert_clause___7
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause values_clause
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause values_clause conditional_insert_clause___7
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause
conditional_insert_clause___2#: 'WHEN' condition 'THEN' insert_into_clause values_clause error_logging_clause conditional_insert_clause___7
conditional_insert_clause___7: conditional_insert_clause___7 conditional_insert_clause___3#
conditional_insert_clause___7: conditional_insert_clause___3#
conditional_insert_clause___3#: insert_into_clause
conditional_insert_clause___3#: insert_into_clause error_logging_clause
conditional_insert_clause___3#: insert_into_clause values_clause
conditional_insert_clause___3#: insert_into_clause values_clause error_logging_clause
conditional_insert_clause___9: 'ELSE' insert_into_clause
conditional_insert_clause___9: 'ELSE' insert_into_clause conditional_insert_clause___0
conditional_insert_clause___9: 'ELSE' insert_into_clause error_logging_clause
conditional_insert_clause___9: 'ELSE' insert_into_clause error_logging_clause conditional_insert_clause___0
conditional_insert_clause___9: 'ELSE' insert_into_clause values_clause
conditional_insert_clause___9: 'ELSE' insert_into_clause values_clause conditional_insert_clause___0
conditional_insert_clause___9: 'ELSE' insert_into_clause values_clause error_logging_clause
conditional_insert_clause___9: 'ELSE' insert_into_clause values_clause error_logging_clause conditional_insert_clause___0
constrained_datetime_interval_type: constrained_datetime_type
constrained_datetime_interval_type: constrained_interval_type
constrained_datetime_type: datetime_link_expanded_n
constrained_datetime_type: datetime_link_expanded_n 'WITH' 'LOCAL' 'TIME' 'ZONE'
constrained_datetime_type: datetime_link_expanded_n 'WITH' 'TIME' 'ZONE'
constrained_datetime_type: datetime_link_expanded_n constraint
constrained_datetime_type: datetime_link_expanded_n constraint 'WITH' 'LOCAL' 'TIME' 'ZONE'
constrained_datetime_type: datetime_link_expanded_n constraint 'WITH' 'TIME' 'ZONE'
constrained_interval_type: 'INTERVAL' constrained_interval_type_2
constrained_interval_type_2: 'DAY' 'TO' 'SECOND'
constrained_interval_type_2: 'DAY' 'TO' 'SECOND' iconstraint
constrained_interval_type_2: 'DAY' iconstraint 'TO' 'SECOND'
constrained_interval_type_2: 'DAY' iconstraint 'TO' 'SECOND' iconstraint
constrained_interval_type_2: 'YEAR' 'TO' 'MONTH'
constrained_interval_type_2: 'YEAR' iconstraint 'TO' 'MONTH'
constrained_type: 'NATIONAL' constraint
constrained_type: 'NATIONAL' constraint NOT_NULL_opt
constrained_type: 'NATIONAL' constraint charset_spec_opt
constrained_type: 'NATIONAL' constraint charset_spec_opt NOT_NULL_opt
constrained_type: constrained_datetime_interval_type
constrained_type: constrained_datetime_interval_type NOT_NULL_opt
constrained_type: constrained_datetime_interval_type charset_spec_opt
constrained_type: constrained_datetime_interval_type charset_spec_opt NOT_NULL_opt
constrained_type: national_w_opt_charset_spec
constrained_type: national_w_opt_charset_spec NOT_NULL_opt
constrained_type: unconstrained_type_wo_datetime_wo_national
constrained_type: unconstrained_type_wo_datetime_wo_national NOT_NULL_opt
constrained_type: unconstrained_type_wo_datetime_wo_national charset_spec_opt
constrained_type: unconstrained_type_wo_datetime_wo_national charset_spec_opt NOT_NULL_opt
constrained_type: unconstrained_type_wo_datetime_wo_national constraint
constrained_type: unconstrained_type_wo_datetime_wo_national constraint NOT_NULL_opt
constrained_type: unconstrained_type_wo_datetime_wo_national constraint charset_spec_opt
constrained_type: unconstrained_type_wo_datetime_wo_national constraint charset_spec_opt NOT_NULL_opt
constraint: '(' sim_expr 'BYTE' ')'
constraint: '(' sim_expr 'CHAR' ')'
constraint: 'RANGE' range
constraint: id_or_qualid
constraint: identifier '.' identifier '@' dblink
constraint: identifier '@' dblink
constraint: inline_constraint
constraint: inline_ref_constraint
constraint: out_of_line_constraint
constraint: out_of_line_ref_constraint
constraint: paren_expr_list
constraint_state: constraint_state___0
constraint_state: constraint_state___0 constraint_state___1
constraint_state: constraint_state___0 constraint_state___1 constraint_state___2
constraint_state: constraint_state___0 constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___0 constraint_state___1 exceptions_clause
constraint_state: constraint_state___0 constraint_state___2
constraint_state: constraint_state___0 constraint_state___2 exceptions_clause
constraint_state: constraint_state___0 exceptions_clause
constraint_state: constraint_state___0 using_index_clause
constraint_state: constraint_state___0 using_index_clause constraint_state___1
constraint_state: constraint_state___0 using_index_clause constraint_state___1 constraint_state___2
constraint_state: constraint_state___0 using_index_clause constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___0 using_index_clause constraint_state___1 exceptions_clause
constraint_state: constraint_state___0 using_index_clause constraint_state___2
constraint_state: constraint_state___0 using_index_clause constraint_state___2 exceptions_clause
constraint_state: constraint_state___0 using_index_clause exceptions_clause
constraint_state: constraint_state___1
constraint_state: constraint_state___1 constraint_state___2
constraint_state: constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___1 exceptions_clause
constraint_state: constraint_state___2
constraint_state: constraint_state___2 exceptions_clause
constraint_state: constraint_state___4
constraint_state: constraint_state___4 constraint_state___0
constraint_state: constraint_state___4 constraint_state___0 constraint_state___1
constraint_state: constraint_state___4 constraint_state___0 constraint_state___1 constraint_state___2
constraint_state: constraint_state___4 constraint_state___0 constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 constraint_state___1 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 constraint_state___2
constraint_state: constraint_state___4 constraint_state___0 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 using_index_clause
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___1
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___1 constraint_state___2
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___1 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___2
constraint_state: constraint_state___4 constraint_state___0 using_index_clause constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 constraint_state___0 using_index_clause exceptions_clause
constraint_state: constraint_state___4 constraint_state___1
constraint_state: constraint_state___4 constraint_state___1 constraint_state___2
constraint_state: constraint_state___4 constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 constraint_state___1 exceptions_clause
constraint_state: constraint_state___4 constraint_state___2
constraint_state: constraint_state___4 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 exceptions_clause
constraint_state: constraint_state___4 using_index_clause
constraint_state: constraint_state___4 using_index_clause constraint_state___1
constraint_state: constraint_state___4 using_index_clause constraint_state___1 constraint_state___2
constraint_state: constraint_state___4 using_index_clause constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 using_index_clause constraint_state___1 exceptions_clause
constraint_state: constraint_state___4 using_index_clause constraint_state___2
constraint_state: constraint_state___4 using_index_clause constraint_state___2 exceptions_clause
constraint_state: constraint_state___4 using_index_clause exceptions_clause
constraint_state: exceptions_clause
constraint_state: using_index_clause
constraint_state: using_index_clause constraint_state___1
constraint_state: using_index_clause constraint_state___1 constraint_state___2
constraint_state: using_index_clause constraint_state___1 constraint_state___2 exceptions_clause
constraint_state: using_index_clause constraint_state___1 exceptions_clause
constraint_state: using_index_clause constraint_state___2
constraint_state: using_index_clause constraint_state___2 exceptions_clause
constraint_state: using_index_clause exceptions_clause
constraint_state___0: 'NORELY'
constraint_state___0: 'RELY'
constraint_state___1: 'DISABLE'
constraint_state___1: 'ENABLE'
constraint_state___2: 'NOVALIDATE'
constraint_state___2: 'VALIDATE'
constraint_state___3: 'DEFERRED'
constraint_state___3: 'IMMEDIATE'
constraint_state___4: 'DEFERRABLE'
constraint_state___4: 'DEFERRABLE' 'INITIALLY' constraint_state___3
constraint_state___4: 'INITIALLY' constraint_state___5
constraint_state___4: 'INITIALLY' constraint_state___5 constraint_state___6
constraint_state___4: 'NOT' 'DEFERRABLE'
constraint_state___4: 'NOT' 'DEFERRABLE' 'INITIALLY' constraint_state___3
constraint_state___5: 'DEFERRED'
constraint_state___5: 'IMMEDIATE'
constraint_state___6: 'DEFERRABLE'
constraint_state___6: 'NOT' 'DEFERRABLE'
containers_clause: 'CONTAINERS' '(' containers_clause___0 ')'
containers_clause: 'CONTAINERS' '(' identifier '.' containers_clause___0 ')'
containers_clause___0: identifier
containers_clause___0: table
conversion_function: cast
conversion_function: decompose
conversion_function: to_binary_double
conversion_function: to_binary_float
conversion_function: to_date
conversion_function: to_dsinterval
conversion_function: to_number
conversion_function: to_timestamp
conversion_function: to_timestamp_tz
conversion_function: to_yminterval
conversion_function: translate_using
conversion_function: validate_conversion
cost_matrix_clause: 'COST' cost_matrix_clause___0
cost_matrix_clause___0: '(' expr ')' 'VALUES' '(' '(' literal ')' ')'
cost_matrix_clause___0: '(' expr ')' 'VALUES' '(' '(' literal ')' cost_matrix_clause___3 ')'
cost_matrix_clause___0: '(' expr ')' 'VALUES' '(' '(' literal cost_matrix_clause___2 ')' ')'
cost_matrix_clause___0: '(' expr ')' 'VALUES' '(' '(' literal cost_matrix_clause___2 ')' cost_matrix_clause___3 ')'
cost_matrix_clause___0: '(' expr cost_matrix_clause___1 ')' 'VALUES' '(' '(' literal ')' ')'
cost_matrix_clause___0: '(' expr cost_matrix_clause___1 ')' 'VALUES' '(' '(' literal ')' cost_matrix_clause___3 ')'
cost_matrix_clause___0: '(' expr cost_matrix_clause___1 ')' 'VALUES' '(' '(' literal cost_matrix_clause___2 ')' ')'
cost_matrix_clause___0: '(' expr cost_matrix_clause___1 ')' 'VALUES' '(' '(' literal cost_matrix_clause___2 ')' cost_matrix_clause___3 ')'
cost_matrix_clause___0: 'MODEL'
cost_matrix_clause___0: 'MODEL' 'AUTO'
cost_matrix_clause___1: ',' expr
cost_matrix_clause___1: cost_matrix_clause___1 ',' expr
cost_matrix_clause___2: ',' literal
cost_matrix_clause___2: cost_matrix_clause___2 ',' literal
cost_matrix_clause___3: cost_matrix_clause___3 cost_matrix_clause___0#
cost_matrix_clause___3: cost_matrix_clause___0#
cost_matrix_clause___0#: ',' '(' literal ')'
cost_matrix_clause___0#: ',' '(' literal cost_matrix_clause___5 ')'
cost_matrix_clause___5: ',' literal
cost_matrix_clause___5: cost_matrix_clause___5 ',' literal
count: 'COUNT' '(' count___0 count___1 ')'
count: 'COUNT' '(' count___1 ')'
count___0: 'ALL'
count___0: 'DISTINCT'
count___0: 'UNIQUE'
count___1: '*'
count___1: expr
count___1: identifier '.' '*'
count_analytic: count keep_clause
count_analytic: count keep_clause over_clause
count_analytic: count over_clause
count_arg: count_arg___0
count_arg: count_arg___1 count_arg___0
count_arg___0: '*'
count_arg___0: expr
count_arg___0: identifier '.' '*'
count_arg___1: 'ALL'
count_arg___1: 'DISTINCT'
count_arg___1: 'UNIQUE'
cross_outer_apply_clause: cross_outer_apply_clause___1 'APPLY' cross_outer_apply_clause___0
cross_outer_apply_clause___0: collection_expression
cross_outer_apply_clause___0: table_reference_or_join_clause
cross_outer_apply_clause___1: 'CROSS'
cross_outer_apply_clause___1: 'OUTER'
cube_meas: identifier cube_meas___0
cube_meas___0: base_meas_clause
cube_meas___0: calc_measure_clause
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' cume_dist_aggregate___3 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' cume_dist_aggregate___3 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 'NULLS' cume_dist_aggregate___3 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 'NULLS' cume_dist_aggregate___3 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' cume_dist_aggregate___3 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' cume_dist_aggregate___3 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 'NULLS' cume_dist_aggregate___3 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 'NULLS' cume_dist_aggregate___3 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___2 cume_dist_aggregate___4 ')'
cume_dist_aggregate: 'CUME_DIST' '(' expr cume_dist_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr cume_dist_aggregate___4 ')'
cume_dist_aggregate___0: 'FIRST'
cume_dist_aggregate___0: 'LAST'
cume_dist_aggregate___1: ',' expr
cume_dist_aggregate___1: cume_dist_aggregate___1 ',' expr
cume_dist_aggregate___2: 'ASC'
cume_dist_aggregate___2: 'DESC'
cume_dist_aggregate___3: 'FIRST'
cume_dist_aggregate___3: 'LAST'
cume_dist_aggregate___4: cume_dist_aggregate___4 cume_dist_aggregate___0#
cume_dist_aggregate___4: cume_dist_aggregate___0#
cume_dist_aggregate___0#: ',' expr
cume_dist_aggregate___0#: ',' expr 'NULLS' cume_dist_aggregate___0
cume_dist_aggregate___0#: ',' expr cume_dist_aggregate___6
cume_dist_aggregate___0#: ',' expr cume_dist_aggregate___6 'NULLS' cume_dist_aggregate___0
cume_dist_aggregate___6: 'ASC'
cume_dist_aggregate___6: 'DESC'
cursor_expression: 'CURSOR' '(' subquery ')'
cycle_clause: 'CYCLE' c_alias 'SET' c_alias 'TO' cycle_clause___1 'DEFAULT' cycle_clause___2
cycle_clause: 'CYCLE' c_alias cycle_clause___0 'SET' c_alias 'TO' cycle_clause___1 'DEFAULT' cycle_clause___2
cycle_clause___0: ',' c_alias
cycle_clause___0: cycle_clause___0 ',' c_alias
cycle_clause___1: bind_var
cycle_clause___1: literal
cycle_clause___2: bind_var
cycle_clause___2: literal
dangling_binary_expr: relal_op sim_expr
dangling_dangling_predicate: 'IS' 'DANGLING'
dangling_dangling_predicate: 'IS' 'NOT' 'DANGLING'
dangling_empty_predicate: 'IS' 'EMPTY'
dangling_empty_predicate: 'IS' 'NOT' 'EMPTY'
dangling_infinite_predicate: 'IS' 'INFINITE'
dangling_infinite_predicate: 'IS' 'NOT' 'INFINITE'
dangling_nan_predicate: 'IS' 'NAN'
dangling_nan_predicate: 'IS' 'NOT' 'NAN'
dangling_null_predicate: 'IS' 'NOT' 'NULL'
dangling_null_predicate: 'IS' 'NULL'
dangling_set_predicate: 'IS' 'A' 'SET'
dangling_set_predicate: 'IS' 'NOT' 'A' 'SET'
data_item: ':' digits
data_item: ':' identifier
data_item: identifier
data_item: identifier '(' "expr_list" ')'
data_item: identifier '(' "expr_list" ')' '.' data_item
data_item: identifier '.' data_item
data_mining_function: cluster_details
data_mining_function: cluster_details_analytic
data_mining_function: cluster_distance
data_mining_function: cluster_distance_analytic
data_mining_function: cluster_id1
data_mining_function: cluster_prob_analytic
data_mining_function: cluster_probability
data_mining_function: cluster_set
data_mining_function: cluster_set_analytic
data_mining_function: feature_compare
data_mining_function: feature_details
data_mining_function: feature_details_analytic
data_mining_function: feature_id
data_mining_function: feature_id_analytic
data_mining_function: feature_set
data_mining_function: feature_set_analytic
data_mining_function: feature_value
data_mining_function: feature_value_analytic
data_mining_function: ora_dm_partition_name
data_mining_function: prediction
data_mining_function: prediction_analytic
data_mining_function: prediction_bounds
data_mining_function: prediction_cost
data_mining_function: prediction_cost_analytic
data_mining_function: prediction_details
data_mining_function: prediction_prob_analytic
data_mining_function: prediction_probability
data_mining_function: prediction_set
data_mining_function: prediction_set_analytic
datatype: 'DOMAIN' identifier 'NOVALIDATE'
datatype: ANSI_supported_datatypes
datatype: Any_types
datatype: Oracle_built_in_datatypes
datatype: Oracle_supplied_types
datatype: Rowid_datatypes
datatype: Spatial_types
datatype: XML_types
datatype: datatype domain_clause
datatype: user_defined_types
datetime_d: 'TIME' 'WITH' 'TIME' 'ZONE'
datetime_d: 'TIMESTAMP' 'WITH' 'LOCAL' 'TIME' 'ZONE'
datetime_d: 'TIMESTAMP' 'WITH' 'TIME' 'ZONE'
datetime_d: datetime_link_expanded_n
datetime_datatypes: 'DATE'
datetime_datatypes: 'INTERVAL' 'DAY' 'TO' 'SECOND'
datetime_datatypes: 'INTERVAL' 'DAY' 'TO' 'SECOND' datetime_datatypes___1
datetime_datatypes: 'INTERVAL' 'DAY' datetime_datatypes___0 'TO' 'SECOND'
datetime_datatypes: 'INTERVAL' 'DAY' datetime_datatypes___0 'TO' 'SECOND' datetime_datatypes___1
datetime_datatypes: 'INTERVAL' 'YEAR' 'TO' 'MONTH'
datetime_datatypes: 'INTERVAL' 'YEAR' datetime_datatypes___4 'TO' 'MONTH'
datetime_datatypes: 'TIME' '(' digits ')'
datetime_datatypes: 'TIMESTAMP'
datetime_datatypes: 'TIMESTAMP' datetime_datatypes___2
datetime_datatypes: 'TIMESTAMP' datetime_datatypes___2 datetime_datatypes___3
datetime_datatypes: 'TIMESTAMP' datetime_datatypes___3
datetime_datatypes___0: '(' digits ')'
datetime_datatypes___1: '(' digits ')'
datetime_datatypes___2: '(' digits ')'
datetime_datatypes___3: 'WITH' 'LOCAL' 'TIME' 'ZONE'
datetime_datatypes___3: 'WITH' 'TIME' 'ZONE'
datetime_datatypes___4: '(' digits ')'
datetime_expanded_n: captureable_datetime_identifiers
datetime_expanded_n: datetime_expanded_n '.' identifier
datetime_expression: expr 'AT' datetime_expression___0
datetime_expression___0: 'LOCAL'
datetime_expression___0: 'TIME' 'ZONE' datetime_expression___1
datetime_expression___1: 'DBTIMEZONE'
datetime_expression___1: 'SESSIONTIMEZONE'
datetime_expression___1: expr
datetime_expression___1: string_literal
datetime_field: 'DAY'
datetime_field: 'HOUR'
datetime_field: 'MINUTE'
datetime_field: 'MONTH'
datetime_field: 'SECOND'
datetime_field: 'TIMEZONE_HOUR'
datetime_field: 'TIMEZONE_MINUTE'
datetime_field: 'YEAR'
datetime_link_expanded_n: datetime_expanded_n
datetime_link_expanded_n: datetime_expanded_n '@' dblink
datetime_literal: 'DATE' string_literal
datetime_literal: 'TIME' string_literal
datetime_literal: 'TIMESTAMP' string_literal
datetime_literal: 'TO_DATE' '(' string_literal ',' string_literal ')'
datetime_literal: datetime_link_expanded_n
datetime_literal: interval_literal
datetime_string_field: 'TIMEZONE_ABBR'
datetime_string_field: 'TIMEZONE_REGION'
dblink: dblink_alts
dblink: identifier
dblink: identifier '@' identifier
dblink: identifier dblink___0
dblink: identifier dblink___0 '@' identifier
dblink___0: '.' identifier
dblink___0: dblink___0 '.' identifier
dblink_alts: '!'
dblink_alts: '@' identifier
dblink_alts: dotted_name
dblink_alts: dotted_name '@' identifier
decl_id: 'CURRENT'
decl_id: 'DELETE'
decl_id: 'EXISTS'
decl_id: 'IF' 'NOT' 'EXISTS' identifier
decl_id: 'IF' 'NOT' 'EXISTS' identifier '.' identifier
decl_id: 'PRIOR'
decl_id: identifier
decl_id: identifier '.' identifier
decompose: 'DECOMPOSE' '(' column ')'
decompose: 'DECOMPOSE' '(' column decompose___0 ')'
decompose___0: 'CANONICAL'
decompose___0: 'COMPATIBILITY'
default_expr_opt: ':' '=' interval_expression
default_expr_opt: ':' '=' pls_expr
default_expr_opt: 'DEFAULT' pls_expr
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause error_logging_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause returning_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause returning_clause error_logging_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause where_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause where_clause error_logging_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause where_clause returning_clause
delete: 'DELETE' 'FROM' aliased_dml_table_expression_clause where_clause returning_clause error_logging_clause
delete: 'DELETE' aliased_dml_table_expression_clause
delete: 'DELETE' aliased_dml_table_expression_clause error_logging_clause
delete: 'DELETE' aliased_dml_table_expression_clause returning_clause
delete: 'DELETE' aliased_dml_table_expression_clause returning_clause error_logging_clause
delete: 'DELETE' aliased_dml_table_expression_clause where_clause
delete: 'DELETE' aliased_dml_table_expression_clause where_clause error_logging_clause
delete: 'DELETE' aliased_dml_table_expression_clause where_clause returning_clause
delete: 'DELETE' aliased_dml_table_expression_clause where_clause returning_clause error_logging_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause error_logging_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause returning_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause returning_clause error_logging_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause where_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause where_clause error_logging_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause where_clause returning_clause
delete: 'DELETE' hint 'FROM' aliased_dml_table_expression_clause where_clause returning_clause error_logging_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause error_logging_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause returning_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause returning_clause error_logging_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause where_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause where_clause error_logging_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause where_clause returning_clause
delete: 'DELETE' hint aliased_dml_table_expression_clause where_clause returning_clause error_logging_clause
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' dense_rank_aggregate___3 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' dense_rank_aggregate___3 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 'NULLS' dense_rank_aggregate___3 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 'NULLS' dense_rank_aggregate___3 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' dense_rank_aggregate___3 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' dense_rank_aggregate___3 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 'NULLS' dense_rank_aggregate___3 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 'NULLS' dense_rank_aggregate___3 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___2 dense_rank_aggregate___4 ')'
dense_rank_aggregate: 'DENSE_RANK' '(' expr dense_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr dense_rank_aggregate___4 ')'
dense_rank_aggregate___0: 'FIRST'
dense_rank_aggregate___0: 'LAST'
dense_rank_aggregate___1: ',' expr
dense_rank_aggregate___1: dense_rank_aggregate___1 ',' expr
dense_rank_aggregate___2: 'ASC'
dense_rank_aggregate___2: 'DESC'
dense_rank_aggregate___3: 'FIRST'
dense_rank_aggregate___3: 'LAST'
dense_rank_aggregate___4: dense_rank_aggregate___4 dense_rank_aggregate___0#
dense_rank_aggregate___4: dense_rank_aggregate___0#
dense_rank_aggregate___0#: ',' expr
dense_rank_aggregate___0#: ',' expr 'NULLS' dense_rank_aggregate___0
dense_rank_aggregate___0#: ',' expr dense_rank_aggregate___6
dense_rank_aggregate___0#: ',' expr dense_rank_aggregate___6 'NULLS' dense_rank_aggregate___0
dense_rank_aggregate___6: 'ASC'
dense_rank_aggregate___6: 'DESC'
designator: decl_id
designator: string_literal
distinct_unique_all: 'ALL'
distinct_unique_all: 'DISTINCT'
distinct_unique_all: 'UNIQUE'
dml_table_expression_clause: '(' subquery ')'
dml_table_expression_clause: '(' subquery subquery_restriction_clause ')'
dml_table_expression_clause: dml_table_expression_clause___1
dml_table_expression_clause: identifier '.' dml_table_expression_clause___1
dml_table_expression_clause: table_collection_expression
dml_table_expression_clause___0: '@' dblink
dml_table_expression_clause___0: partition_extension_clause
dml_table_expression_clause___0: partition_extension_clause 'MOVE' 'TO' partition_extension_clause
dml_table_expression_clause___1: identifier
dml_table_expression_clause___1: identifier '@' dblink
dml_table_expression_clause___1: table
dml_table_expression_clause___1: table dml_table_expression_clause___0
domain_clause: 'DOMAIN' identifier
domain_clause: 'DOMAIN' identifier '.' identifier
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___0
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___0 domain_clause___2
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___0 domain_clause___2 domain_clause___4
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___0 domain_clause___4
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___2
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___2 domain_clause___4
domain_clause: 'DOMAIN' identifier '.' identifier domain_clause___4
domain_clause: 'DOMAIN' identifier domain_clause___0
domain_clause: 'DOMAIN' identifier domain_clause___0 domain_clause___2
domain_clause: 'DOMAIN' identifier domain_clause___0 domain_clause___2 domain_clause___4
domain_clause: 'DOMAIN' identifier domain_clause___0 domain_clause___4
domain_clause: 'DOMAIN' identifier domain_clause___2
domain_clause: 'DOMAIN' identifier domain_clause___2 domain_clause___4
domain_clause: 'DOMAIN' identifier domain_clause___4
domain_clause___0: '(' column ')'
domain_clause___0: '(' column domain_clause___1 ')'
domain_clause___1: ',' column
domain_clause___1: domain_clause___1 ',' column
domain_clause___2: 'USING' '(' column ')'
domain_clause___2: 'USING' '(' column domain_clause___3 ')'
domain_clause___3: ',' column
domain_clause___3: domain_clause___3 ',' column
domain_clause___4: 'NOVALIDATE'
domain_clause___4: 'VALIDATE'
dotted_expr: 'STANDARD' '.' decl_id
dotted_expr: name '.' decl_id
dotted_name: 'STANDARD' '.' identifier
dotted_name: dotted_name '.' identifier
dotted_name: identifier
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier identifier
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier identifier table_tags_clause
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier identifier table_tags_clause where_clause
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier identifier where_clause
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier table_tags_clause
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier table_tags_clause where_clause
duality_subquery: 'SELECT' object_gen_clause 'FROM' identifier where_clause
edge_pattern: abbreviated_edge_pattern
edge_pattern: full_edge_pattern
element_pattern: edge_pattern
element_pattern: vertex_pattern
else_clause: 'ELSE' expr
empty_parens_opt: '(' ')'
entry: "entry___0"
entry: 'UNNEST' '(' duality_subquery ')'
entry: duality_subquery
entry: json_name_value_pair
entry: json_name_value_pair JSON_returning_clause
entry: json_name_value_pair format_clause JSON_returning_clause
entry: wildcard
error_logging_clause: 'LOG' 'ERRORS'
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___0
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___0 error_logging_clause___1
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___0 error_logging_clause___1 reject_limit_unlimited
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___0 reject_limit_unlimited
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___1
error_logging_clause: 'LOG' 'ERRORS' error_logging_clause___1 reject_limit_unlimited
error_logging_clause: 'LOG' 'ERRORS' reject_limit_unlimited
error_logging_clause___0: 'INTO' identifier '.' table
error_logging_clause___0: 'INTO' table
error_logging_clause___1: '(' expr ')'
error_logging_clause___1: '(' subquery ')'
exceptions_clause: 'EXCEPTIONS' 'INTO' identifier '.' table
exceptions_clause: 'EXCEPTIONS' 'INTO' table
exists_condition: 'EXISTS' '(' subquery ')'
exposed_fact_column_name: expr
exposed_fact_column_name: identifier
expr: attribute
expr: expr#
expr: expr# 'COLLATE' identifier
expr#: JSON_object_access_expr
expr#: case_expression
expr#: compound_condition
expr#: compound_expression
expr#: cursor_expression
expr#: datetime_expression
expr#: function_expression
expr#: fuzzy_match
expr#: interval_expression
expr#: json_object
expr#: model_expression
expr#: multiset_expression
expr#: object_access_expression
expr#: scalar_subquery_expression
expr#: simple_expression
expr#: type_constructor_expression
expr_as_alias_: expr_as_alias____0
expr_as_alias_: expr_as_alias____0 as_alias
expr_as_alias____0: '(' "expr_list" ')'
expr_as_alias____0: expr
expr_desc_asc_nulls: expr
expr_desc_asc_nulls: expr 'NULLS' expr_desc_asc_nulls___0
expr_desc_asc_nulls: expr expr_desc_asc_nulls___1
expr_desc_asc_nulls: expr expr_desc_asc_nulls___1 'NULLS' expr_desc_asc_nulls___0
expr_desc_asc_nulls___0: 'FIRST'
expr_desc_asc_nulls___0: 'LAST'
expr_desc_asc_nulls___1: 'ASC'
expr_desc_asc_nulls___1: 'DESC'
external_calling_standard: 'C'
external_calling_standard: 'PASCAL'
external_indicator_mode: 'IN'
external_indicator_mode: 'IN' 'OUT'
external_indicator_mode: 'OUT'
external_indicator_or_len: 'NATIVE'
external_indicator_or_len: external_indicator_token
external_indicator_or_len: external_indicator_token external_indicator_mode
external_indicator_token: 'CHARSETFORM'
external_indicator_token: 'CHARSETID'
external_indicator_token: 'DURATION'
external_indicator_token: 'INDICATOR'
external_indicator_token: 'LENGTH'
external_indicator_token: 'MAXLEN'
external_indicator_token: 'TDO'
external_name_ops: 'NAME' identifier
external_options: 'BY' 'REFERENCE'
external_options: 'BY' 'VALUE'
external_parameter_list: '(' external_parm_list_entry ')'
external_parameter_list: '(' external_parm_list_entry external_parm_list_entry_opt ')'
external_parameter_style_token: 'GENERAL'
external_parameter_style_token: 'NATIVE'
external_parameter_style_token: 'ORACLE'
external_parameter_style_token: 'SQL'
external_parm_list_entry: external_parm_list_name_and_indicator
external_parm_list_entry: external_parm_list_name_and_indicator external_options
external_parm_list_entry: external_parm_list_name_and_indicator external_options external_type
external_parm_list_entry: external_parm_list_name_and_indicator external_type
external_parm_list_entry_opt: ',' external_parm_list_entry
external_parm_list_entry_opt: external_parm_list_entry_opt ',' external_parm_list_entry
external_parm_list_name_and_indicator: 'CONTEXT'
external_parm_list_name_and_indicator: 'RETURN'
external_parm_list_name_and_indicator: 'RETURN' external_indicator_or_len
external_parm_list_name_and_indicator: 'SQLCODE'
external_parm_list_name_and_indicator: 'SQLNAME'
external_parm_list_name_and_indicator: 'SQLSTATE'
external_parm_list_name_and_indicator: identifier
external_parm_list_name_and_indicator: identifier external_indicator_or_len
external_type: 'ARRAY'
external_type: 'CHAR'
external_type: 'DOUBLE'
external_type: 'DOUBLE' 'NATIVE'
external_type: 'FLOAT'
external_type: 'FLOAT' 'NATIVE'
external_type: 'INT'
external_type: 'LONG'
external_type: 'OCICOLL'
external_type: 'OCIDATE'
external_type: 'OCIDATETIME'
external_type: 'OCIDURATION'
external_type: 'OCIINTERVAL'
external_type: 'OCILOBLOCATOR'
external_type: 'OCINUMBER'
external_type: 'OCIRAW'
external_type: 'OCIREF'
external_type: 'OCIREFCURSOR'
external_type: 'OCIROWID'
external_type: 'OCISTRING'
external_type: 'OCITYPE'
external_type: 'ORLANY'
external_type: 'ORLVARY'
external_type: 'RAW'
external_type: 'SB1'
external_type: 'SB2'
external_type: 'SB4'
external_type: 'SHORT'
external_type: 'SIZE_T'
external_type: 'STRING'
external_type: 'STRUCT'
external_type: 'UB1'
external_type: 'UB2'
external_type: 'UB4'
external_type: 'UNSIGNED' 'CHAR'
external_type: 'UNSIGNED' 'INT'
external_type: 'UNSIGNED' 'LONG'
external_type: 'UNSIGNED' 'SHORT'
external_type: 'VALIST'
external_type: 'VOID'
extract_datetime: 'EXTRACT' '(' extract_datetime___0 'FROM' expr ')'
extract_datetime___0: 'DAY'
extract_datetime___0: 'HOUR'
extract_datetime___0: 'MINUTE'
extract_datetime___0: 'MONTH'
extract_datetime___0: 'SECOND'
extract_datetime___0: 'TIMEZONE_ABBR'
extract_datetime___0: 'TIMEZONE_HOUR'
extract_datetime___0: 'TIMEZONE_MINUTE'
extract_datetime___0: 'TIMEZONE_REGION'
extract_datetime___0: 'YEAR'
factor: pri
factor: pri 'AT' time_zone_specifier
factor: pri EXP_pri_opt
factor: unary_add_op factor
feature_compare: 'FEATURE_COMPARE' '(' identifier '.' model mining_attribute_clause 'AND' mining_attribute_clause ')'
feature_compare: 'FEATURE_COMPARE' '(' model mining_attribute_clause 'AND' mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' identifier '.' model feature_details___0 feature_details___1 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' identifier '.' model feature_details___0 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' identifier '.' model feature_details___1 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' identifier '.' model mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' model feature_details___0 feature_details___1 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' model feature_details___0 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' model feature_details___1 mining_attribute_clause ')'
feature_details: 'FEATURE_DETAILS' '(' model mining_attribute_clause ')'
feature_details___0: ',' expr
feature_details___0: ',' expr ',' digits
feature_details___1: 'ABS'
feature_details___1: 'ASC'
feature_details___1: 'DESC'
feature_details_analytic: 'FEATURE_DETAILS' mining_analytic_body
feature_id: 'FEATURE_ID' '(' identifier '.' model mining_attribute_clause ')'
feature_id: 'FEATURE_ID' '(' model mining_attribute_clause ')'
feature_id_analytic: 'FEATURE_ID' mining_analytic_body
feature_set: 'FEATURE_SET' '(' identifier '.' model feature_set___0 mining_attribute_clause ')'
feature_set: 'FEATURE_SET' '(' identifier '.' model mining_attribute_clause ')'
feature_set: 'FEATURE_SET' '(' model feature_set___0 mining_attribute_clause ')'
feature_set: 'FEATURE_SET' '(' model mining_attribute_clause ')'
feature_set___0: ',' expr
feature_set___0: ',' expr ',' expr
feature_set_analytic: 'FEATURE_SET' mining_analytic_body
feature_value: 'FEATURE_VALUE' '(' identifier '.' model ',' expr mining_attribute_clause ')'
feature_value: 'FEATURE_VALUE' '(' identifier '.' model mining_attribute_clause ')'
feature_value: 'FEATURE_VALUE' '(' model ',' expr mining_attribute_clause ')'
feature_value: 'FEATURE_VALUE' '(' model mining_attribute_clause ')'
feature_value_analytic: 'FEATURE_VALUE' mining_analytic_body
ff_w_external: subprg_spec is_or_as 'EXTERNAL'
ff_w_external: subprg_spec is_or_as 'EXTERNAL' c_external_ops
filename: "legalfilename"
filename: identifier
filename: string_literal
filename: string_literal ':' string_literal
filenamefragment: digits
filenamefragment: identifier
filter_clause: hier_id 'TO' condition
filter_clauses: 'FILTER' 'FACT' '(' filter_clause ')'
filter_clauses: 'FILTER' 'FACT' '(' filter_clause filter_clauses___0 ')'
filter_clauses___0: ',' filter_clause
filter_clauses___0: filter_clauses___0 ',' filter_clause
first_last_value: first_last_value___1 first_last_value___0 'OVER' '(' ')'
first_last_value: first_last_value___1 first_last_value___0 'OVER' '(' analytic_clause ')'
first_last_value: first_last_value___2 first_last_value___3 'OVER'
first_last_value: first_last_value___2 first_last_value___3 'OVER' analytic_clause
first_last_value___0: '(' expr ')'
first_last_value___0: '(' expr ')' respect_ignore_nulls
first_last_value___0: '(' expr respect_ignore_nulls ')'
first_last_value___1: 'FIRST_VALUE'
first_last_value___1: 'LAST_VALUE'
first_last_value___2: 'FIRST_VALUE'
first_last_value___2: 'LAST_VALUE'
first_last_value___3: '(' expr ')'
first_last_value___3: '(' expr ')' respect_ignore_nulls
first_last_value___3: '(' expr respect_ignore_nulls ')'
fixed_quantifier: '{' integer '}'
flashback_query_clause: flashback_query_clause flashback_query_clause#
flashback_query_clause: flashback_query_clause#
flashback_query_clause#: 'AS' 'OF' 'PERIOD' 'FOR' column expr
flashback_query_clause#: 'AS' 'OF' flashback_query_clause___1 expr
flashback_query_clause#: 'VERSIONS' 'BETWEEN' flashback_query_clause___2 flashback_query_clause___3 'AND' flashback_query_clause___4
flashback_query_clause#: 'VERSIONS' 'PERIOD' 'FOR' column 'BETWEEN' flashback_query_clause___5 'AND' flashback_query_clause___0
flashback_query_clause___0: 'MAXVALUE'
flashback_query_clause___0: expr
flashback_query_clause___1: 'SCN'
flashback_query_clause___1: 'SNAPSHOT'
flashback_query_clause___1: 'TIMESTAMP'
flashback_query_clause___2: 'SCN'
flashback_query_clause___2: 'TIMESTAMP'
flashback_query_clause___3: 'MINVALUE'
flashback_query_clause___3: expr
flashback_query_clause___4: 'MAXVALUE'
flashback_query_clause___4: expr
flashback_query_clause___5: 'MINVALUE'
flashback_query_clause___5: expr
floating_point_condition: expr 'IS' 'NOT' floating_point_condition___0
floating_point_condition: expr 'IS' floating_point_condition___0
floating_point_condition___0: 'INFINITE'
floating_point_condition___0: 'NAN'
fml_part: '(' prm_spec ')'
fml_part: '(' prm_spec parm_list_opt ')'
fname_separator: '#'
fname_separator: '$'
fname_separator: '%'
fname_separator: '+'
fname_separator: '-'
fname_separator: '.'
fname_separator: '~'
following_boundary: following_boundary___1 'AND' following_boundary___0
following_boundary___0: 'UNBOUNDED' 'FOLLOWING'
following_boundary___0: expr 'FOLLOWING'
following_boundary___1: 'CURRENT' 'MEMBER'
following_boundary___1: expr 'FOLLOWING'
for_compress: 'FOR' 'COMPRESS' digits
for_compress: 'FOR' 'COMPRESS' digits for_compress___0
for_compress___0: ',' digits
for_compress___0: for_compress___0 ',' digits
for_update_clause: 'FOR' 'UPDATE'
for_update_clause: 'FOR' 'UPDATE' for_update_clause___0
for_update_clause: 'FOR' 'UPDATE' for_update_clause___0 for_update_clause___7
for_update_clause: 'FOR' 'UPDATE' for_update_clause___7
for_update_clause___0: 'OF' column
for_update_clause___0: 'OF' column for_update_clause___3
for_update_clause___0: 'OF' for_update_clause___1 column
for_update_clause___0: 'OF' for_update_clause___1 column for_update_clause___3
for_update_clause___1: for_update_clause___2 '.'
for_update_clause___1: identifier '.' for_update_clause___2 '.'
for_update_clause___2: identifier
for_update_clause___2: table
for_update_clause___3: for_update_clause___3 for_update_clause___0#
for_update_clause___3: for_update_clause___0#
for_update_clause___0#: ',' column
for_update_clause___0#: ',' for_update_clause___5 column
for_update_clause___5: for_update_clause___6 '.'
for_update_clause___5: identifier '.' for_update_clause___6 '.'
for_update_clause___6: identifier
for_update_clause___6: table
for_update_clause___7: 'NOWAIT'
for_update_clause___7: 'SKIP' 'LOCKED'
for_update_clause___7: 'WAIT' integer
format_clause: 'FORMAT' 'JSON'
format_clause: 'FORMAT' 'OSON'
from_clause: 'FROM' cartesian_product
from_clause: from_clause table_tags_clause
from_first_last: 'FROM' from_first_last___0
from_first_last___0: 'FIRST'
from_first_last___0: 'LAST'
full_edge_any_direction: '-' '[' ']' '-'
full_edge_any_direction: '-' '[' optional_element_pattern_filter ']' '-'
full_edge_pattern: full_edge_any_direction
full_edge_pattern: full_edge_pointing_left
full_edge_pattern: full_edge_pointing_right
full_edge_pointing_left: '<' '-' '[' ']' '-'
full_edge_pointing_left: '<' '-' '[' optional_element_pattern_filter ']' '-'
full_edge_pointing_right: '-' '[' ']' '-' '>'
full_edge_pointing_right: '-' '[' optional_element_pattern_filter ']' '-' '>'
func_flag_list: 'USING' using_charset_csname
func_flag_list: json_common_flag_list
func_flag_list: json_common_flag_list json_datetime_returning_flag
func_flag_list: json_common_flag_list json_datetime_returning_flag json_common_flag_list
func_flag_list: json_common_flag_list json_non_value_flag
func_flag_list: json_common_flag_list json_non_value_flag json_non_value_flag_list
func_flag_list: json_common_flag_list json_wo_datetime_returning_flag
func_flag_list: json_common_flag_list json_wo_datetime_returning_flag json_non_value_flag_list
func_flag_list: json_datetime_returning_flag
func_flag_list: json_datetime_returning_flag json_common_flag_list
func_flag_list: json_non_value_flag
func_flag_list: json_non_value_flag json_non_value_flag_list
func_flag_list: json_wo_datetime_returning_flag
func_flag_list: json_wo_datetime_returning_flag json_non_value_flag_list
func_return_prm_spec_unconstrained_type: 'SELF' 'AS' 'RESULT'
func_return_prm_spec_unconstrained_type: prm_spec_unconstrained_type
func_return_prm_spec_unconstrained_type: prm_spec_unconstrained_type 'SQL_MACRO'
func_return_prm_spec_unconstrained_type: prm_spec_unconstrained_type 'SQL_MACRO' '(' identifier ')'
function: OLAP_function
function: aggregate_function
function: analytic_function
function: data_cartridge_function
function: model_function
function: object_reference_function
function: single_row_function
function: user_defined_function
function_call: 'MOD' paren_expr_list
function_call: JSON_function
function_call: json_object '(' ')'
function_call: json_object '(' func_flag_list ')'
function_call: json_object '(' json_object_arg ')'
function_call: json_object '(' json_object_arg func_flag_list ')'
function_call: json_object '(' json_object_arg json_object_arg_list ')'
function_call: json_object '(' json_object_arg json_object_arg_list func_flag_list ')'
function_call: name '(' ')'
function_call: name '(' '+' ')'
function_call: name '(' cast_expression_arg 'AS' unconstrained_type ')'
function_call: name '(' cast_expression_arg 'AS' unconstrained_type cast_conversion_error ')'
function_call: name '(' cast_expression_arg 'AS' unconstrained_type cast_conversion_error cast_nls_params ')'
function_call: name '(' cast_expression_arg 'AS' unconstrained_type cast_nls_params ')'
function_call: name '(' datetime_field 'FROM' sim_expr ')'
function_call: name '(' datetime_string_field 'FROM' sim_expr ')'
function_call: name '(' trim_options_sim_expr 'FROM' sim_expr ')'
function_call: name paren_expr_list
function_expression: function
function_expression: function_call
function_expression: function_expression '.' function_call
function_expression: function_expression '.' identifier
fuzzy_match: 'FUZZY_MATCH' '(' fuzzy_match___2 ',' string ',' string ')'
fuzzy_match: 'FUZZY_MATCH' '(' fuzzy_match___2 ',' string ',' string ',' fuzzy_match___0 ')'
fuzzy_match___0: fuzzy_match___0 fuzzy_match___0#
fuzzy_match___0: fuzzy_match___0#
fuzzy_match___0#: 'EDIT_TOLERANCE' literal
fuzzy_match___0#: 'RELATE_TO_SHORTER'
fuzzy_match___0#: 'UNSCALED'
fuzzy_match___2: 'BIGRAM'
fuzzy_match___2: 'DAMERAU_LEVENSHTEIN'
fuzzy_match___2: 'JARO_WINKLER'
fuzzy_match___2: 'LEVENSHTEIN'
fuzzy_match___2: 'LONGEST_COMMON_SUBSTRING'
fuzzy_match___2: 'TRIGRAM'
fuzzy_match___2: 'WHOLE_WORD_MATCH'
gen_call: '(' pls_expr 'AS' unconstrained_type ')' '.' procedure_call
general_quantifier: '{' ',' integer '}'
general_quantifier: '{' integer ',' integer '}'
graph_element_object_name: identifier
graph_element_object_name: identifier '.' identifier
graph_pattern: 'MATCH' path_pattern_list
graph_pattern: 'MATCH' path_pattern_list where_clause_w_boolean
graph_pattern_quantifier: '*'
graph_pattern_quantifier: '+'
graph_pattern_quantifier: '?'
graph_pattern_quantifier: fixed_quantifier
graph_pattern_quantifier: general_quantifier
graph_table: 'GRAPH_TABLE' '(' graph_element_object_name graph_pattern graph_table_columns_clause ')'
group_by_clause: 'GROUP' 'BY' parenthesized_group_by_list
group_by_col: '(' ')'
group_by_col: '(' group_by_list ')'
group_by_col: expr
group_by_col: grouping_sets_clause
group_by_col: rollup_cube_clause
group_by_list: group_by_col
group_by_list: group_by_col group_by_list___0
group_by_list___0: ',' group_by_col
group_by_list___0: group_by_list___0 ',' group_by_col
group_comparison_condition: '(' "expr_list" ')' cmp_op group_comparison_condition___5 '(' group_comparison_condition___0 ')'
group_comparison_condition: expr group_comparison_condition___3 group_comparison_condition___2 '(' group_comparison_condition___4 ')'
group_comparison_condition___0: "expression_list"
group_comparison_condition___0: "expression_list" group_comparison_condition___1
group_comparison_condition___0: subquery
group_comparison_condition___1: ',' "expression_list"
group_comparison_condition___1: group_comparison_condition___1 ',' "expression_list"
group_comparison_condition___2: 'ALL'
group_comparison_condition___2: 'ANY'
group_comparison_condition___2: 'SOME'
group_comparison_condition___3: '<'
group_comparison_condition___3: '<' '='
group_comparison_condition___3: '>'
group_comparison_condition___3: '>' '='
group_comparison_condition___3: cmp_op
group_comparison_condition___4: "expression_list"
group_comparison_condition___4: subquery
group_comparison_condition___5: 'ALL'
group_comparison_condition___5: 'ANY'
group_comparison_condition___5: 'SOME'
grouping_expression_list: "expression_list"
grouping_expression_list: "expression_list" grouping_expression_list___0
grouping_expression_list___0: ',' "expression_list"
grouping_expression_list___0: grouping_expression_list___0 ',' "expression_list"
grouping_sets_clause: 'GROUPING' 'SETS' '(' grouping_sets_clause___0 ')'
grouping_sets_clause___0: grouping_expression_list
grouping_sets_clause___0: rollup_cube_clause
having_clause: 'HAVING' condition
hier_ancestor_expression: 'HIER_ANCESTOR' '(' member_expression '@' hier_ancestor_expression___0 ')'
hier_ancestor_expression___0: 'DEPTH' depth_expression
hier_ancestor_expression___0: 'LEVEL' identifier
hier_function_name: 'HIER_CAPTION'
hier_function_name: 'HIER_CHILD_COUNT'
hier_function_name: 'HIER_DEPTH'
hier_function_name: 'HIER_DESCRIPTION'
hier_function_name: 'HIER_LEVEL'
hier_function_name: 'HIER_MEMBER_NAME'
hier_function_name: 'HIER_MEMBER_UNIQUE_NAME'
hier_function_name: 'HIER_PARENT_LEVEL'
hier_function_name: 'HIER_PARENT_UNIQUE_NAME'
hier_id: '(' '(' dim_alias '.' ')' hier_alias ')'
hier_id: 'MEASURES'
hier_id: column
hier_lead_lag_clause: member_expression 'OFFSET' expr
hier_lead_lag_clause: member_expression 'OFFSET' expr 'WITHIN' hier_lead_lag_clause___0
hier_lead_lag_clause___0: 'ACROSS' 'ANCESTOR' 'AT' 'LEVEL' identifier
hier_lead_lag_clause___0: 'ACROSS' 'ANCESTOR' 'AT' 'LEVEL' identifier hier_lead_lag_clause___1
hier_lead_lag_clause___0: 'LEVEL'
hier_lead_lag_clause___0: 'PARENT'
hier_lead_lag_clause___1: 'POSITION' 'FROM' hier_lead_lag_clause___2
hier_lead_lag_clause___2: 'BEGINNING'
hier_lead_lag_clause___2: 'END'
hier_lead_lag_expression: hier_lead_lag_expression___0 '(' hier_lead_lag_clause ')'
hier_lead_lag_expression___0: 'HIER_LAG'
hier_lead_lag_expression___0: 'HIER_LEAD'
hier_navigation_expression: hier_ancestor_expression
hier_navigation_expression: hier_lead_lag_expression
hier_navigation_expression: hier_parent_expression
hier_parent_expression: 'HIER_PARENT' '(' member_expression ')'
hier_ref: '*'
hier_ref: identifier
hier_ref: identifier '.' identifier
hier_ref: identifier '.' identifier 'DEFAULT'
hier_ref: identifier '.' identifier hier_ref___0
hier_ref: identifier '.' identifier hier_ref___0 'DEFAULT'
hier_ref: identifier 'DEFAULT'
hier_ref: identifier hier_ref___0
hier_ref: identifier hier_ref___0 'DEFAULT'
hier_ref___0: 'AS' alias
hier_ref___0: alias
hierarchical_query_clause: 'CONNECT' 'BY' 'NOCYCLE' condition
hierarchical_query_clause: 'CONNECT' 'BY' 'NOCYCLE' condition hierarchical_query_clause___0
hierarchical_query_clause: 'CONNECT' 'BY' condition
hierarchical_query_clause: 'CONNECT' 'BY' condition hierarchical_query_clause___0
hierarchical_query_clause: 'START' 'WITH' condition 'CONNECT' 'BY' 'NOCYCLE' condition
hierarchical_query_clause: 'START' 'WITH' condition 'CONNECT' 'BY' condition
hierarchical_query_clause___0: 'START' 'WITH' condition
hierarchies_clause: 'HIERARCHIES' '(' ')'
hierarchies_clause: 'HIERARCHIES' '(' hierarchies_clause___0 ')'
hierarchies_clause___0: hier_ref
hierarchies_clause___0: hier_ref hierarchies_clause___1
hierarchies_clause___1: ',' hier_ref
hierarchies_clause___1: hierarchies_clause___1 ',' hier_ref
hierarchy_ref: alias
hierarchy_ref: alias '.' alias
iconstraint: '(' sim_expr ')'
id_or_qualid: identifier
id_or_qualid: identifier '.' identifier
identifier: id
identifier: idq
in_condition: '(' "expr_list" ')' 'IN' '(' in_condition___2 ')'
in_condition: '(' "expr_list" ')' 'IN' values_clause
in_condition: '(' "expr_list" ')' 'NOT' 'IN' '(' in_condition___2 ')'
in_condition: '(' "expr_list" ')' 'NOT' 'IN' values_clause
in_condition: expr 'IN' in_condition___0
in_condition: expr 'NOT' 'IN' in_condition___0
in_condition___0: '(' in_condition___1 ')'
in_condition___0: expr
in_condition___1: "expression_list"
in_condition___1: subquery
in_condition___2: "expression_list"
in_condition___2: "expression_list" in_condition___3
in_condition___2: subquery
in_condition___3: ',' "expression_list"
in_condition___3: in_condition___3 ',' "expression_list"
inline_constraint: 'CONSTRAINT' identifier inline_constraint___0
inline_constraint: 'VALIDATE' string_literal
inline_constraint: inline_constraint___0
inline_constraint___0: 'CHECK' '(' condition ')'
inline_constraint___0: 'CHECK' '(' condition ')' constraint_state
inline_constraint___0: 'CHECK' '(' condition ')' constraint_state precheck_state
inline_constraint___0: 'CHECK' '(' condition ')' precheck_state
inline_constraint___0: inline_constraint___1
inline_constraint___0: inline_constraint___1 constraint_state
inline_constraint___1: 'NOT' 'NULL'
inline_constraint___1: 'NULL'
inline_constraint___1: 'PRIMARY' 'KEY'
inline_constraint___1: 'UNIQUE'
inline_constraint___1: references_clause
inline_ref_constraint: 'CONSTRAINT' identifier references_clause
inline_ref_constraint: 'CONSTRAINT' identifier references_clause constraint_state
inline_ref_constraint: 'SCOPE' 'IS' identifier
inline_ref_constraint: 'SCOPE' 'IS' identifier '.' identifier
inline_ref_constraint: 'WITH' 'ROWID'
inline_ref_constraint: references_clause
inline_ref_constraint: references_clause constraint_state
insert: 'INSERT' insert___0
insertOp: 'INSERT' string_literal '=' rhsExpr
insertOp: 'INSERT' string_literal '=' rhsExpr "insertOp___0"
insertOp: 'INSERT' string_literal '=' rhsExpr "transform_directive___1"
insertOp: 'INSERT' string_literal '=' rhsExpr "transform_directive___1" "insertOp___0"
insert___0: multi_table_insert
insert___0: single_table_insert
insert_into_clause: 'INTO' aliased_dml_table_expression_clause
insert_into_clause: 'INTO' aliased_dml_table_expression_clause insert_into_clause___0
insert_into_clause___0: '(' column ')'
insert_into_clause___0: '(' column insert_into_clause___1 ')'
insert_into_clause___1: ',' column
insert_into_clause___1: insert_into_clause___1 ',' column
integer: '+' digits
integer: '-' digits
integer: digits
interface_constrained_type: unconstrained_type
interface_constrained_type: unconstrained_type NOT_NULL_opt
interface_constrained_type: unconstrained_type paren_expr_list
interface_constrained_type: unconstrained_type paren_expr_list NOT_NULL_opt
interface_fml_part: '(' interface_prm_spec ')'
interface_fml_part: '(' interface_prm_spec interface_prm_spec_list_opt ')'
interface_indicator_opt: 'INDICATOR' identifier
interface_prm_spec: decl_id interface_constrained_type
interface_prm_spec: decl_id interface_constrained_type interface_indicator_opt
interface_prm_spec_list_opt: ',' interface_prm_spec
interface_prm_spec_list_opt: interface_prm_spec_list_opt ',' interface_prm_spec
interface_proc_spec: 'PROCEDURE' identifier '.' identifier interface_fml_part
interface_proc_spec: 'PROCEDURE' identifier interface_fml_part
interval_d: 'INTERVAL' interval_qualifier
interval_day_to_second: 'INTERVAL' interval_day_to_second___1 interval_day_to_second___2
interval_day_to_second: 'INTERVAL' interval_day_to_second___1 interval_day_to_second___2 'TO' interval_day_to_second___6
interval_day_to_second___0: '(' digits ')'
interval_day_to_second___1: bind_var
interval_day_to_second___1: string_literal
interval_day_to_second___2: 'SECOND'
interval_day_to_second___2: 'SECOND' interval_day_to_second___5
interval_day_to_second___2: interval_day_to_second___3
interval_day_to_second___2: interval_day_to_second___3 interval_day_to_second___4
interval_day_to_second___3: 'DAY'
interval_day_to_second___3: 'HOUR'
interval_day_to_second___3: 'MINUTE'
interval_day_to_second___4: '(' digits ')'
interval_day_to_second___5: '(' digits ')'
interval_day_to_second___5: '(' digits ',' digits ')'
interval_day_to_second___6: 'DAY'
interval_day_to_second___6: 'HOUR'
interval_day_to_second___6: 'MINUTE'
interval_day_to_second___6: 'SECOND'
interval_day_to_second___6: 'SECOND' interval_day_to_second___0
interval_expression: '(' expr '-' expr ')' interval_expression___3
interval_expression: interval_day_to_second
interval_expression: interval_year_to_month
interval_expression___0: '(' digits ')'
interval_expression___1: '(' digits ')'
interval_expression___2: '(' digits ')'
interval_expression___3: 'DAY' 'TO' 'SECOND'
interval_expression___3: 'DAY' 'TO' 'SECOND' interval_expression___1
interval_expression___3: 'DAY' interval_expression___0 'TO' 'SECOND'
interval_expression___3: 'DAY' interval_expression___0 'TO' 'SECOND' interval_expression___1
interval_expression___3: 'YEAR' 'TO' 'MONTH'
interval_expression___3: 'YEAR' interval_expression___2 'TO' 'MONTH'
interval_literal: 'INTERVAL' string_literal interval_literal_qualifier
interval_literal_qualifier: 'DAY'
interval_literal_qualifier: 'DAY' 'TO' 'SECOND'
interval_literal_qualifier: 'HOUR'
interval_literal_qualifier: 'MINUTE'
interval_literal_qualifier: 'MONTH'
interval_literal_qualifier: 'SECOND'
interval_literal_qualifier: 'YEAR'
interval_literal_qualifier: 'YEAR' 'TO' 'MONTH'
interval_qualifier: 'DAY' 'TO' 'SECOND'
interval_qualifier: 'YEAR' 'TO' 'MONTH'
interval_year_to_month: 'INTERVAL' interval_year_to_month___0 interval_year_to_month___1
interval_year_to_month: 'INTERVAL' interval_year_to_month___0 interval_year_to_month___1 'TO' interval_year_to_month___3
interval_year_to_month: 'INTERVAL' interval_year_to_month___0 interval_year_to_month___1 interval_year_to_month___2
interval_year_to_month: 'INTERVAL' interval_year_to_month___0 interval_year_to_month___1 interval_year_to_month___2 'TO' interval_year_to_month___3
interval_year_to_month___0: bind_var
interval_year_to_month___0: string_literal
interval_year_to_month___1: 'MONTH'
interval_year_to_month___1: 'YEAR'
interval_year_to_month___2: '(' digits ')'
interval_year_to_month___3: 'MONTH'
interval_year_to_month___3: 'YEAR'
into_list: 'INTO' name_list
is_JSON_condition: expr is_JSON_condition___3
is_JSON_condition: expr is_JSON_condition___3 format_clause
is_JSON_condition: expr is_JSON_condition___3 format_clause is_JSON_condition___2
is_JSON_condition: expr is_JSON_condition___3 is_JSON_condition___2
is_JSON_condition___0: '(' is_JSON_condition___1 ')'
is_JSON_condition___1: allow_disallow_strict_lax_JSON
is_JSON_condition___1: is_JSON_condition___1 allow_disallow_strict_lax_JSON
is_JSON_condition___2: allow_disallow_strict_lax_JSON
is_JSON_condition___2: is_JSON_condition___2 allow_disallow_strict_lax_JSON
is_JSON_condition___3: 'IS' 'JSON'
is_JSON_condition___3: 'IS' 'JSON' is_JSON_condition___0
is_JSON_condition___3: 'IS' 'NOT' 'JSON'
is_JSON_condition___3: 'IS' 'NOT' 'JSON' is_JSON_condition___0
is_JSON_condition___3: allow_disallow_strict_lax_JSON
is_a_set_condition: expr 'IS' 'A' 'SET'
is_a_set_condition: expr 'IS' 'NOT' 'A' 'SET'
is_any_condition: 'ANY'
is_any_condition: identifier 'IS' 'ANY'
is_empty_condition: expr 'IS' 'EMPTY'
is_empty_condition: expr 'IS' 'NOT' 'EMPTY'
is_json: is_prefix 'JSON'
is_json: is_prefix 'JSON' '(' ')'
is_json: is_prefix 'JSON' '(' is_json_options ')'
is_json: is_prefix 'NOT' 'JSON'
is_json: is_prefix 'NOT' 'JSON' '(' ')'
is_json: is_prefix 'NOT' 'JSON' '(' is_json_options ')'
is_json_options: allow_scalars_opt
is_json_options: lax_strict_opt
is_json_options: lax_strict_opt allow_scalars_opt
is_json_options: lax_strict_opt unique_keys_opt
is_json_options: lax_strict_opt unique_keys_opt allow_scalars_opt
is_json_options: unique_keys_opt
is_json_options: unique_keys_opt allow_scalars_opt
is_label_expression: 'IS' label_expression
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' 'ONLY' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' 'ONLY' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' 'ONLY' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' 'ONLY' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' '(' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' 'ONLY' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' 'ONLY' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' 'ONLY' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' 'ONLY' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' type ')'
is_of_type_condition: expr 'IS' 'NOT' 'OF' 'TYPE' '(' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' '(' 'ONLY' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'OF' '(' 'ONLY' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' '(' 'ONLY' type ')'
is_of_type_condition: expr 'IS' 'OF' '(' 'ONLY' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' '(' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'OF' '(' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' '(' type ')'
is_of_type_condition: expr 'IS' 'OF' '(' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' 'ONLY' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' 'ONLY' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' 'ONLY' type ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' 'ONLY' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' identifier '.' type ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' identifier '.' type is_of_type_condition___0 ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' type ')'
is_of_type_condition: expr 'IS' 'OF' 'TYPE' '(' type is_of_type_condition___0 ')'
is_of_type_condition___0: is_of_type_condition___0 is_of_type_condition___0#
is_of_type_condition___0: is_of_type_condition___0#
is_of_type_condition___0#: ',' 'ONLY' identifier '.' type
is_of_type_condition___0#: ',' 'ONLY' type
is_of_type_condition___0#: ',' identifier '.' type
is_of_type_condition___0#: ',' type
is_or_as: 'AS'
is_or_as: 'IS'
is_or_as: annotations_clause is_or_as
is_prefix: 'KEY' sim_expr 'IS'
is_prefix: sim_expr 'IS'
is_present_condition: cell_assignment 'IS' 'PRESENT'
iterand_decl: identifier
iterand_decl: identifier constrained_type
iterand_decl: identifier mutable_opt
iterand_decl: identifier mutable_opt constrained_type
iterator: iterand_decl ',' iterand_decl 'IN' iterator_control_list
iterator: iterand_decl 'IN' iterator_control_list
iterator_control: 'REVERSE' iterator_guts
iterator_control: 'REVERSE' iterator_guts when_clause
iterator_control: 'REVERSE' iterator_guts while_clause
iterator_control: 'REVERSE' iterator_guts while_clause when_clause
iterator_control: iterator_guts
iterator_control: iterator_guts when_clause
iterator_control: iterator_guts while_clause
iterator_control: iterator_guts while_clause when_clause
iterator_control_list: iterator_control
iterator_control_list: iterator_control_list ',' iterator_control
iterator_guts: '(' 'EXECUTE' 'IMMEDIATE' pls_expr ')'
iterator_guts: '(' 'EXECUTE' 'IMMEDIATE' pls_expr using_clause_opt ')'
iterator_guts: '(' select ')'
iterator_guts: 'INDICES' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr ')'
iterator_guts: 'INDICES' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr using_clause_opt ')'
iterator_guts: 'INDICES' 'OF' '(' select ')'
iterator_guts: 'INDICES' 'OF' pls_expr
iterator_guts: 'PAIRS' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr ')'
iterator_guts: 'PAIRS' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr using_clause_opt ')'
iterator_guts: 'PAIRS' 'OF' '(' select ')'
iterator_guts: 'PAIRS' 'OF' pls_expr
iterator_guts: 'REPEAT' pls_expr
iterator_guts: 'VALUES' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr ')'
iterator_guts: 'VALUES' 'OF' '(' 'EXECUTE' 'IMMEDIATE' pls_expr using_clause_opt ')'
iterator_guts: 'VALUES' 'OF' '(' select ')'
iterator_guts: 'VALUES' 'OF' pls_expr
iterator_guts: pls_expr
iterator_guts: sim_expr_DBLDOT__sim_expr
iterator_guts: sim_expr_DBLDOT__sim_expr 'BY' pls_expr
javascript: Statement_List1
join_clause: table_reference_or_join_clause "inner_cross_join_clause"
join_clause: table_reference_or_join_clause "outer_join_clause"
join_clause: table_reference_or_join_clause cross_outer_apply_clause
json_array: 'JSON_ARRAY' '(' ')'
json_array: 'JSON_ARRAY' '(' JSON_ARRAY_content ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'BSON' order_by_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'JSON' order_by_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'FORMAT' 'OSON' order_by_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_on_no_rows_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_on_null_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause 'STRICT' ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause 'STRICT' format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause 'STRICT' format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause 'STRICT' json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause JSON_returning_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause format_clause ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause format_clause json_non_value_flag ')'
json_arrayagg: 'JSON_ARRAYAGG' '(' expr order_by_clause json_non_value_flag ')'
json_col_tags: "json_col_tags___0"
json_col_tags: "json_col_tags___0" 'ETAG'
json_col_tags: 'NOUPDATE'
json_col_tags: 'UPDATE'
json_common_expr_flag: 'DEFAULT' pls_expr 'ON' 'CONVERSION' 'ERROR'
json_common_expr_flag: 'DEFAULT' pls_expr 'ON' 'CONVERSION' 'ERROR' ',' sim_expr
json_common_expr_flag: 'DEFAULT' pls_expr 'ON' 'CONVERSION' 'ERROR' ',' sim_expr ',' sim_expr
json_common_expr_flag: 'DEFAULT' pls_expr 'ON' 'EMPTY'
json_common_expr_flag: 'DEFAULT' pls_expr 'ON' 'ERROR'
json_common_flag: 'ABSENT' 'ON' 'NULL'
json_common_flag: 'ALLOW' 'SCALARS'
json_common_flag: 'ASCII'
json_common_flag: 'EMPTY' 'ARRAY' 'ON' 'EMPTY'
json_common_flag: 'EMPTY' 'OBJECT' 'ON' 'EMPTY'
json_common_flag: 'EMPTY' 'ON' 'EMPTY'
json_common_flag: 'EMPTY' 'ON' 'ERROR'
json_common_flag: 'ERROR' 'ON' 'EMPTY'
json_common_flag: 'ERROR' 'ON' 'ERROR'
json_common_flag: 'FALSE' 'ON' 'ERROR'
json_common_flag: 'LAX'
json_common_flag: 'NULL' 'ON' 'EMPTY'
json_common_flag: 'NULL' 'ON' 'ERROR'
json_common_flag: 'NULL' 'ON' 'NULL'
json_common_flag: 'PRETTY'
json_common_flag: 'STRICT'
json_common_flag: 'TRUE' 'ON' 'ERROR'
json_common_flag: 'TRUNCATE'
json_common_flag: JSON_value_on_empty_clause
json_common_flag: JSON_value_on_error_clause
json_common_flag2: 'ERROR' 'ON' 'MISMATCH'
json_common_flag2: 'ERROR' 'ON' 'MISMATCH' json_mismatch_type
json_common_flag2: 'IGNORE' 'ON' 'MISMATCH'
json_common_flag2: 'IGNORE' 'ON' 'MISMATCH' json_mismatch_type
json_common_flag2: 'NULL' 'ON' 'MISMATCH'
json_common_flag2: 'NULL' 'ON' 'MISMATCH' json_mismatch_type
json_common_flag2: 'USING' 'CASE_INSENSITIVE' 'MAPPING'
json_common_flag2: 'USING' 'CASE_SENSITIVE' 'MAPPING'
json_common_flag_list: json_common_expr_flag
json_common_flag_list: json_common_expr_flag json_common_flag_list
json_common_flag_list: json_common_flag
json_common_flag_list: json_common_flag json_common_flag_list
json_common_flag_list: json_common_flag2
json_common_flag_list: json_common_flag2 json_common_flag_list
json_dataguide: 'JSON_DATAGUIDE' '(' expr ')'
json_dataguide: 'JSON_DATAGUIDE' '(' expr json_dataguide___0 ')'
json_dataguide___0: ',' format
json_dataguide___0: ',' format ',' flag
json_datetime_returning_flag: 'RETURNING' 'DATE'
json_datetime_returning_flag: 'RETURNING' 'TIMESTAMP'
json_datetime_returning_flag: 'RETURNING' 'TIMESTAMP' 'WITH' 'TIME' 'ZONE'
json_format: 'FORMAT' 'BSON'
json_format: 'FORMAT' 'JSON'
json_format: 'FORMAT' 'OSON'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'TRUNCATE' ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_MERGEPATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'PRETTY' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'ASCII' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'PRETTY' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'TRUNCATE' ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause 'TRUNCATE' JSON_on_error_clause ')'
json_mergepatch: 'JSON_PATCH' '(' JSON_target_expr ',' JSON_patch_expr JSON_returning_clause JSON_on_error_clause ')'
json_mismatch_flag: 'EXTRA' 'DATA'
json_mismatch_flag: 'MISSING' 'DATA'
json_mismatch_flag: 'TYPE' 'ERROR'
json_mismatch_list: json_mismatch_flag
json_mismatch_list: json_mismatch_flag ',' json_mismatch_list
json_mismatch_type: '(' json_mismatch_list ')'
json_name_value_pair: is_prefix pls_expr
json_name_value_pair: is_prefix pls_expr 'FORMAT' 'JSON'
json_name_value_pair: pls_expr 'IS' pls_expr
json_name_value_pair: string_literal 'IS' expr
json_non_value_flag: 'WITH' 'ARRAY' 'WRAPPER'
json_non_value_flag: 'WITH' 'CONDITIONAL' 'ARRAY' 'WRAPPER'
json_non_value_flag: 'WITH' 'CONDITIONAL' 'WRAPPER'
json_non_value_flag: 'WITH' 'UNCONDITIONAL' 'ARRAY' 'WRAPPER'
json_non_value_flag: 'WITH' 'UNCONDITIONAL' 'WRAPPER'
json_non_value_flag: 'WITH' 'UNIQUE' 'KEYS'
json_non_value_flag: 'WITH' 'WRAPPER'
json_non_value_flag: 'WITHOUT' 'ARRAY' 'WRAPPER'
json_non_value_flag: 'WITHOUT' 'UNIQUE' 'KEYS'
json_non_value_flag: 'WITHOUT' 'WRAPPER'
json_non_value_flag: json_non_value_flag 'UNWRAP'
json_non_value_flag_list: json_common_expr_flag
json_non_value_flag_list: json_common_expr_flag json_non_value_flag_list
json_non_value_flag_list: json_common_flag
json_non_value_flag_list: json_common_flag json_non_value_flag_list
json_non_value_flag_list: json_common_flag2
json_non_value_flag_list: json_common_flag2 json_non_value_flag_list
json_non_value_flag_list: json_non_value_flag
json_non_value_flag_list: json_non_value_flag json_non_value_flag_list
json_non_value_flag_list: json_wo_datetime_returning_flag
json_non_value_flag_list: json_wo_datetime_returning_flag json_non_value_flag_list
json_obj_tags: "json_obj_tags___0"
json_obj_tags: "json_obj_tags___0" 'ETAG'
json_obj_tags: 'DELETE'
json_obj_tags: 'INSERT'
json_obj_tags: 'NODELETE'
json_obj_tags: 'NOINSERT'
json_obj_tags: 'NOUPDATE'
json_obj_tags: 'UPDATE'
json_object: "json_object___0" '[' ']'
json_object: "json_object___0" '[' JSON_OBJECT_content ']'
json_object: "json_object___1" '[' '{' '}' ']'
json_object: "json_object___1" '[' '{' JSON_OBJECT_content '}' ']'
json_object: "json_object___2" '(' ')'
json_object: "json_object___2" '(' JSON_OBJECT_content ')'
json_object: "json_object___3" '{' '}'
json_object: "json_object___3" '{' JSON_OBJECT_content '}'
json_object: 'JSON_OBJECT'
json_object: 'STANDARD' '.' 'JSON_OBJECT'
json_object: '[' ']'
json_object: '[' '{' '}' ']'
json_object: '[' '{' JSON_OBJECT_content '}' ']'
json_object: '[' JSON_OBJECT_content ']'
json_object: '{' '}'
json_object: '{' JSON_OBJECT_content '}'
json_object_arg: 'KEY' 'VALUE' pls_expr
json_object_arg: 'KEY' 'VALUE' pls_expr json_format
json_object_arg: 'KEY' sim_expr 'VALUE' pls_expr
json_object_arg: 'KEY' sim_expr 'VALUE' pls_expr json_format
json_object_arg: sim_expr 'VALUE' pls_expr
json_object_arg: sim_expr 'VALUE' pls_expr json_format
json_object_arg_list: ',' json_object_arg
json_object_arg_list: json_object_arg_list ',' json_object_arg
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr format_clause json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' 'KEY' expr json_objectagg___0 expr json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___1 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr format_clause json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___1 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___2 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___3 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___3 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___4 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___4 json_objectagg___5 ')'
json_objectagg: 'JSON_OBJECTAGG' '(' expr json_objectagg___0 expr json_objectagg___5 ')'
json_objectagg___0: ','
json_objectagg___0: 'IS'
json_objectagg___0: 'VALUE'
json_objectagg___1: JSON_on_null_clause
json_objectagg___1: JSON_on_null_clause format_clause
json_objectagg___2: JSON_value_on_empty_clause
json_objectagg___2: JSON_value_on_empty_clause format_clause
json_objectagg___3: JSON_returning_clause
json_objectagg___3: JSON_returning_clause format_clause
json_objectagg___4: 'STRICT'
json_objectagg___4: 'STRICT' format_clause
json_objectagg___5: allow_disallow_strict_lax_JSON
json_objectagg___5: allow_disallow_strict_lax_JSON format_clause
json_query: 'JSON_QUERY' '(' expr ',' JSON_basic_path_expression ')'
json_query: 'JSON_QUERY' '(' expr ',' JSON_basic_path_expression json_query___0 ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'BSON' ',' JSON_basic_path_expression json_query___0 ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'JSON' ',' JSON_basic_path_expression json_query___0 ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression ')'
json_query: 'JSON_QUERY' '(' expr 'FORMAT' 'OSON' ',' JSON_basic_path_expression json_query___0 ')'
json_query___0: json_query___0 json_query___0#
json_query___0: json_query___0#
json_query___0#: JSON_query_on_empty_clause
json_query___0#: JSON_query_on_error_clause
json_query___0#: JSON_query_on_mismatch_clause
json_query___0#: JSON_query_returning_clause
json_query___0#: JSON_query_wrapper_clause
json_query___0#: json_query___2 'QUOTES'
json_query___0#: json_query___2 'QUOTES' json_query___3
json_query___2: 'KEEP'
json_query___2: 'OMIT'
json_query___3: 'ON' 'SCALAR' 'STRING'
json_scalar: 'JSON_SCALAR' '(' expr ')'
json_scalar: 'JSON_SCALAR' '(' expr json_scalar___0 ')'
json_scalar: 'JSON_SCALAR' '(' expr json_scalar___0 json_scalar___1 ')'
json_scalar: 'JSON_SCALAR' '(' expr json_scalar___1 ')'
json_scalar___0: 'JSON'
json_scalar___0: 'SQL'
json_scalar___1: 'NULL' 'ON' 'NULL'
json_serialize: 'JSON_SERIALIZE' '(' expr ')'
json_serialize: 'JSON_SERIALIZE' '(' expr JSON_returning_clause ')'
json_serialize: 'JSON_SERIALIZE' '(' expr JSON_returning_clause json_serialize___0 ')'
json_serialize: 'JSON_SERIALIZE' '(' expr JSON_returning_clause json_serialize___0 json_serialize___2 ')'
json_serialize: 'JSON_SERIALIZE' '(' expr JSON_returning_clause json_serialize___2 ')'
json_serialize: 'JSON_SERIALIZE' '(' expr json_serialize___0 ')'
json_serialize: 'JSON_SERIALIZE' '(' expr json_serialize___0 json_serialize___2 ')'
json_serialize: 'JSON_SERIALIZE' '(' expr json_serialize___2 ')'
json_serialize___0: json_serialize___0 json_serialize___0#
json_serialize___0: json_serialize___0#
json_serialize___0#: 'ASCII'
json_serialize___0#: 'EXTENDED'
json_serialize___0#: 'ORDERED'
json_serialize___0#: 'PRETTY'
json_serialize___0#: 'TRUNCATE'
json_serialize___2: json_serialize___3 'ON' 'ERROR'
json_serialize___3: 'EMPTY'
json_serialize___3: 'EMPTY' json_serialize___4
json_serialize___3: 'ERROR'
json_serialize___3: 'NULL'
json_serialize___4: 'ARRAY'
json_serialize___4: 'OBJECT'
json_table: "json_table___2" '(' expr "json_table___0" "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_error_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_error_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_error_clause JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___0" JSON_table_on_error_clause JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_error_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_error_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_error_clause JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr JSON_table_on_error_clause JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_error_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_error_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_error_clause JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___0" JSON_table_on_error_clause JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_empty_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_error_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_error_clause JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_error_clause JSON_table_on_empty_clause "json_table___1" JSON_columns_clause ')'
json_table: "json_table___2" '(' expr format_clause JSON_table_on_error_clause JSON_table_on_empty_clause JSON_columns_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_TRANSFORM_returning_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_passing_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_passing_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_passing_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" JSON_passing_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr "json_transform___0" format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause JSON_passing_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_TRANSFORM_returning_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_passing_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_passing_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_passing_clause format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" JSON_passing_clause format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" format_clause ')'
json_transform: 'JSON_TRANSFORM' '(' expr format_clause "json_transform___0" format_clause 'NO_PARTIAL_OSON_UPDATE' ')'
json_unconstrained_type_wo_datetime: link_expanded_n
json_unconstrained_type_wo_datetime: link_expanded_n '%' attribute_designator
json_value: 'JSON_VALUE' '(' expr ',' 'STREAM' string_literal ')'
json_value: 'JSON_VALUE' '(' expr ',' 'STREAM' string_literal json_value___0 ')'
json_value: 'JSON_VALUE' '(' expr ',' string_literal ')'
json_value: 'JSON_VALUE' '(' expr ',' string_literal json_value___0 ')'
json_value: 'JSON_VALUE' '(' expr format_clause ',' 'STREAM' string_literal ')'
json_value: 'JSON_VALUE' '(' expr format_clause ',' 'STREAM' string_literal json_value___0 ')'
json_value: 'JSON_VALUE' '(' expr format_clause ',' string_literal ')'
json_value: 'JSON_VALUE' '(' expr format_clause ',' string_literal json_value___0 ')'
json_value___0: json_value___0 json_value_on__
json_value___0: json_value_on__
json_value_on__: JSON_passing_clause
json_value_on__: JSON_value_on_empty_clause
json_value_on__: JSON_value_on_error_clause
json_value_on__: JSON_value_on_mismatch_clause
json_value_on__: JSON_value_returning_clause
json_value_on__: allow_boolean_number_conversion
json_wo_datetime_returning_flag: 'RETURNING' json_unconstrained_type_wo_datetime
json_wo_datetime_returning_flag: 'RETURNING' json_unconstrained_type_wo_datetime constraint
jsonize: "jsonize___0" '(' table_hier_cons ')'
keepOp: 'KEEP' "keepOp___3"
keepOp: 'KEEP' "keepOp___3" "keepOp___0"
keep_clause: 'KEEP' '(' 'DENSE_RANK' keep_clause___0 'ORDER' 'BY' expr_desc_asc_nulls ')'
keep_clause: 'KEEP' '(' 'DENSE_RANK' keep_clause___0 'ORDER' 'BY' expr_desc_asc_nulls ')' keep_clause___2
keep_clause: 'KEEP' '(' 'DENSE_RANK' keep_clause___0 'ORDER' 'BY' expr_desc_asc_nulls keep_clause___1 ')'
keep_clause: 'KEEP' '(' 'DENSE_RANK' keep_clause___0 'ORDER' 'BY' expr_desc_asc_nulls keep_clause___1 ')' keep_clause___2
keep_clause___0: 'FIRST'
keep_clause___0: 'LAST'
keep_clause___1: ',' expr_desc_asc_nulls
keep_clause___1: keep_clause___1 ',' expr_desc_asc_nulls
keep_clause___2: 'OVER' '(' ')'
keep_clause___2: 'OVER' '(' query_partition_clause ')'
key_value_clause: 'KEY' string "key_value_clause___1" "key_value_clause___0"
key_value_clause: 'UNNEST' '(' duality_subquery ')'
key_value_clause: string "key_value_clause___1" "key_value_clause___0"
label_expression: '!' label_expression
label_expression: '(' label_expression ')'
label_expression: identifier
label_expression: label_expression "label_expression___0" label_expression
lag: 'LAG' lag___0 'OVER' '(' lead_lag_order ')'
lag: 'LAG' lag___0 'OVER' '(' query_partition_clause lead_lag_order ')'
lag___0: '(' expr ')'
lag___0: '(' expr ')' lag___2 'NULLS'
lag___0: '(' expr lag___1 ')'
lag___0: '(' expr lag___1 ')' lag___2 'NULLS'
lag___0: '(' expr lag___3 'NULLS' ')'
lag___0: '(' expr lag___3 'NULLS' lag___4 ')'
lag___0: '(' expr lag___4 ')'
lag___1: ',' expr
lag___1: ',' expr ',' expr
lag___2: 'IGNORE'
lag___2: 'RESPECT'
lag___3: 'IGNORE'
lag___3: 'RESPECT'
lag___4: ',' expr
lag___4: ',' expr ',' expr
large_object_datatypes: 'BFILE'
large_object_datatypes: 'BLOB'
large_object_datatypes: 'CLOB'
large_object_datatypes: 'NCLOB'
lax_strict_opt: 'LAX'
lax_strict_opt: 'STRICT'
lead: 'LEAD' lead___0 'OVER' '(' lead_lag_order ')'
lead: 'LEAD' lead___0 'OVER' '(' query_partition_clause lead_lag_order ')'
lead___0: '(' expr ')'
lead___0: '(' expr ')' lead___2 'NULLS'
lead___0: '(' expr lead___1 ')'
lead___0: '(' expr lead___1 ')' lead___2 'NULLS'
lead___0: '(' expr lead___3 'NULLS' ')'
lead___0: '(' expr lead___3 'NULLS' lead___4 ')'
lead___0: '(' expr lead___4 ')'
lead___1: ',' expr
lead___1: ',' expr ',' expr
lead___2: 'IGNORE'
lead___2: 'RESPECT'
lead___3: 'IGNORE'
lead___3: 'RESPECT'
lead___4: ',' expr
lead___4: ',' expr ',' expr
lead_lag_clause: 'HIERARCHY' hierarchy_ref 'OFFSET' expr
lead_lag_clause: 'HIERARCHY' hierarchy_ref 'OFFSET' expr lead_lag_clause___0
lead_lag_clause___0: 'ACROSS' 'ANCESTOR' 'AT' 'LEVEL' identifier
lead_lag_clause___0: 'ACROSS' 'ANCESTOR' 'AT' 'LEVEL' identifier lead_lag_clause___2
lead_lag_clause___0: 'WITHIN' lead_lag_clause___1
lead_lag_clause___1: 'LEVEL'
lead_lag_clause___1: 'PARENT'
lead_lag_clause___2: 'POSITION' 'FROM' lead_lag_clause___3
lead_lag_clause___3: 'BEGINNING'
lead_lag_clause___3: 'END'
lead_lag_expression: lead_lag_function_name '(' calc_meas_expression ')' 'OVER' '(' lead_lag_clause ')'
lead_lag_function_name: 'LAG'
lead_lag_function_name: 'LAG_DIFF'
lead_lag_function_name: 'LAG_DIFF_PERCENT'
lead_lag_function_name: 'LEAD'
lead_lag_function_name: 'LEAD_DIFF'
lead_lag_function_name: 'LEAD_DIFF_PERCENT'
lead_lag_order: expr
lead_lag_order: order_by_clause
letter: lower_letter
letter: upper_letter
level_member_literal: identifier level_member_literal___0
level_member_literal___0: named_member_keys
level_member_literal___0: pos_member_keys
like_condition: expr 'NOT' like_condition___0 expr
like_condition: expr 'NOT' like_condition___0 expr 'ESCAPE' expr
like_condition: expr like_condition___0 expr
like_condition: expr like_condition___0 expr 'ESCAPE' expr
like_condition: regexp_like_condition
like_condition___0: 'LIKE'
like_condition___0: 'LIKE2'
like_condition___0: 'LIKE4'
like_condition___0: 'LIKEC'
link_expanded_n: dotted_name
link_expanded_n: dotted_name '@' dblink
listType: '[' type ']'
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ')'
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr ')'
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' 'DISTINCT' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ')'
listagg: 'LISTAGG' '(' 'ALL' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr ')'
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr ',' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'ALL' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'ALL' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'ALL' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ')'
listagg: 'LISTAGG' '(' 'DISTINCT' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'DISTINCT' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr ')'
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr ')' listagg___0
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr ')' listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr ',' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' 'DISTINCT' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' 'DISTINCT' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' 'DISTINCT' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' expr ')'
listagg: 'LISTAGG' '(' expr ')' listagg___0
listagg: 'LISTAGG' '(' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' expr ')' listagg___1
listagg: 'LISTAGG' '(' expr ',' expr ')'
listagg: 'LISTAGG' '(' expr ',' expr ')' listagg___0
listagg: 'LISTAGG' '(' expr ',' expr ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' expr ',' expr ')' listagg___1
listagg: 'LISTAGG' '(' expr ',' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' expr ',' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' expr ',' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' expr ',' expr listagg_overflow_clause ')' listagg___1
listagg: 'LISTAGG' '(' expr listagg_overflow_clause ')'
listagg: 'LISTAGG' '(' expr listagg_overflow_clause ')' listagg___0
listagg: 'LISTAGG' '(' expr listagg_overflow_clause ')' listagg___0 listagg___1
listagg: 'LISTAGG' '(' expr listagg_overflow_clause ')' listagg___1
listagg___0: 'WITHIN' 'GROUP' '(' order_by_clause ')'
listagg___1: 'OVER' '(' ')'
listagg___1: 'OVER' '(' listagg___2 ')'
listagg___2: identifier
listagg___2: query_partition_clause
listagg_overflow_clause: 'ON' 'OVERFLOW' 'ERROR'
listagg_overflow_clause: 'ON' 'OVERFLOW' 'TRUNCATE'
listagg_overflow_clause: 'ON' 'OVERFLOW' 'TRUNCATE' listagg_overflow_clause___0
listagg_overflow_clause: 'ON' 'OVERFLOW' 'TRUNCATE' listagg_overflow_clause___0 listagg_overflow_clause___1 'COUNT'
listagg_overflow_clause: 'ON' 'OVERFLOW' 'TRUNCATE' listagg_overflow_clause___1 'COUNT'
listagg_overflow_clause___0: 'NULL'
listagg_overflow_clause___0: string_literal
listagg_overflow_clause___1: 'WITH'
listagg_overflow_clause___1: 'WITHOUT'
literal: 'FALSE'
literal: 'TRUE'
literal: datetime_literal
literal: numeric_literal
literal: string_literal
lnnvl: 'LNNVL' '(' condition ')'
long_and_raw_datatypes: 'LONG'
long_and_raw_datatypes: 'LONG' 'RAW'
long_and_raw_datatypes: 'RAW' '(' digits ')'
main_model: 'MAIN' identifier model_column_clauses cell_reference_options model_rules_clause
main_model: 'MAIN' identifier model_column_clauses model_rules_clause
main_model: model_column_clauses cell_reference_options model_rules_clause
main_model: model_column_clauses model_rules_clause
max: 'MAX' '(' expr ')'
max: 'MAX' '(' expr ')' max___1
max: 'MAX' '(' max___0 expr ')'
max: 'MAX' '(' max___0 expr ')' max___1
max___0: 'ALL'
max___0: 'DISTINCT'
max___1: 'OVER' '(' ')'
max___1: 'OVER' '(' analytic_clause ')'
meas_aggregate_clause: 'AGGREGATE' 'BY' "aggr_name"
measure_expr: expr
measure_expr: expr from_first_last
measure_expr: expr from_first_last respect_ignore_nulls
measure_expr: expr respect_ignore_nulls
measure_ref: 'MEASURES' '.' identifier
measure_ref: identifier
member_condition: expr 'MEMBER' 'OF' expr
member_condition: expr 'MEMBER' expr
member_condition: expr 'NOT' 'MEMBER' 'OF' expr
member_condition: expr 'NOT' 'MEMBER' expr
member_expression: 'ALL'
member_expression: 'CURRENT' 'MEMBER'
member_expression: 'NULL'
member_expression: hier_navigation_expression
member_expression: level_member_literal
merge: 'MERGE' 'INTO' merge___0 'ON' '(' condition ')'
merge: 'MERGE' 'INTO' merge___0 'ON' '(' condition ')' error_logging_clause
merge: 'MERGE' 'INTO' merge___0 'ON' '(' condition ')' permutted_merge_insert_update
merge: 'MERGE' 'INTO' merge___0 'ON' '(' condition ')' permutted_merge_insert_update error_logging_clause
merge: 'MERGE' 'INTO' merge___0 identifier 'ON' '(' condition ')'
merge: 'MERGE' 'INTO' merge___0 identifier 'ON' '(' condition ')' error_logging_clause
merge: 'MERGE' 'INTO' merge___0 identifier 'ON' '(' condition ')' permutted_merge_insert_update
merge: 'MERGE' 'INTO' merge___0 identifier 'ON' '(' condition ')' permutted_merge_insert_update error_logging_clause
merge: 'MERGE' 'INTO' merge___0 identifier merge___2 'ON' '(' condition ')'
merge: 'MERGE' 'INTO' merge___0 identifier merge___2 'ON' '(' condition ')' error_logging_clause
merge: 'MERGE' 'INTO' merge___0 identifier merge___2 'ON' '(' condition ')' permutted_merge_insert_update
merge: 'MERGE' 'INTO' merge___0 identifier merge___2 'ON' '(' condition ')' permutted_merge_insert_update error_logging_clause
merge: 'MERGE' 'INTO' merge___0 merge___2 'ON' '(' condition ')'
merge: 'MERGE' 'INTO' merge___0 merge___2 'ON' '(' condition ')' error_logging_clause
merge: 'MERGE' 'INTO' merge___0 merge___2 'ON' '(' condition ')' permutted_merge_insert_update
merge: 'MERGE' 'INTO' merge___0 merge___2 'ON' '(' condition ')' permutted_merge_insert_update error_logging_clause
mergeOp: 'MERGE' string_literal '=' rhsExpr
merge___0: '(' subquery ')'
merge___0: identifier '.' merge___1
merge___0: merge___1
merge___1: table
merge___1: table '@' dblink
merge___2: 'USING' merge___3
merge___2: 'USING' merge___3 identifier
merge___2: 'USING' merge___3 modified_external_table
merge___2: 'USING' merge___3 modified_external_table identifier
merge___3: '(' subquery ')'
merge___3: identifier '.' merge___4
merge___3: merge___4
merge___4: table
merge___4: table '@' dblink
merge_insert_clause: 'WHEN' 'NOT' 'MATCHED' 'THEN' 'INSERT' 'VALUES' merge_insert_clause___2
merge_insert_clause: 'WHEN' 'NOT' 'MATCHED' 'THEN' 'INSERT' 'VALUES' merge_insert_clause___2 where_clause
merge_insert_clause: 'WHEN' 'NOT' 'MATCHED' 'THEN' 'INSERT' merge_insert_clause___0 'VALUES' merge_insert_clause___2
merge_insert_clause: 'WHEN' 'NOT' 'MATCHED' 'THEN' 'INSERT' merge_insert_clause___0 'VALUES' merge_insert_clause___2 where_clause
merge_insert_clause___0: '(' column ')'
merge_insert_clause___0: '(' column merge_insert_clause___1 ')'
merge_insert_clause___1: ',' column
merge_insert_clause___1: merge_insert_clause___1 ',' column
merge_insert_clause___2: '(' "expr_list_def" ')'
merge_insert_clause___2: expr
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 merge_update_clause___2
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 merge_update_clause___2 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 merge_update_clause___2 where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 merge_update_clause___2 where_clause 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column '=' merge_update_clause___1 where_clause 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 merge_update_clause___2
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 merge_update_clause___2 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 merge_update_clause___2 where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 merge_update_clause___2 where_clause 'DELETE' where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 where_clause
merge_update_clause: 'WHEN' 'MATCHED' 'THEN' 'UPDATE' 'SET' column merge_update_clause___0 '=' merge_update_clause___1 where_clause 'DELETE' where_clause
merge_update_clause___0: '(' identifier ')'
merge_update_clause___1: 'DEFAULT'
merge_update_clause___1: expr
merge_update_clause___2: merge_update_clause___2 merge_update_clause___0#
merge_update_clause___2: merge_update_clause___0#
merge_update_clause___0#: ',' column '=' merge_update_clause___5
merge_update_clause___0#: ',' column merge_update_clause___4 '=' merge_update_clause___5
merge_update_clause___4: '(' identifier ')'
merge_update_clause___5: 'DEFAULT'
merge_update_clause___5: expr
method_call: identifier '(' ')'
method_call: identifier '(' method_call___0 ')'
method_call___0: argument
method_call___0: argument method_call___1
method_call___1: ',' argument
method_call___1: method_call___1 ',' argument
min: 'MIN' '(' expr ')'
min: 'MIN' '(' expr ')' min___1
min: 'MIN' '(' min___0 expr ')'
min: 'MIN' '(' min___0 expr ')' min___1
min___0: 'ALL'
min___0: 'DISTINCT'
min___1: 'OVER' '(' ')'
min___1: 'OVER' '(' analytic_clause ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___0 mining_analytic_body___1 mining_attribute_clause ')' 'OVER' '(' ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___0 mining_analytic_body___1 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___0 mining_attribute_clause ')' 'OVER' '(' ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___0 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___1 mining_attribute_clause ')' 'OVER' '(' ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___1 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___2 mining_analytic_body___3 mining_attribute_clause ')' 'OVER'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___2 mining_analytic_body___3 mining_attribute_clause ')' 'OVER' mining_analytic_clause
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___2 mining_attribute_clause ')' 'OVER'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___2 mining_attribute_clause ')' 'OVER' mining_analytic_clause
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___3 mining_attribute_clause ')' 'OVER'
mining_analytic_body: '(' 'INTO' digits mining_analytic_body___3 mining_attribute_clause ')' 'OVER' mining_analytic_clause
mining_analytic_body: '(' 'INTO' digits mining_attribute_clause ')' 'OVER'
mining_analytic_body: '(' 'INTO' digits mining_attribute_clause ')' 'OVER' '(' ')'
mining_analytic_body: '(' 'INTO' digits mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
mining_analytic_body: '(' 'INTO' digits mining_attribute_clause ')' 'OVER' mining_analytic_clause
mining_analytic_body___0: ',' cluster_id
mining_analytic_body___0: ',' cluster_id opt_topn
mining_analytic_body___1: 'ABS'
mining_analytic_body___1: 'ASC'
mining_analytic_body___1: 'DESC'
mining_analytic_body___2: ',' cluster_id
mining_analytic_body___2: ',' cluster_id opt_topn
mining_analytic_body___3: 'ABS'
mining_analytic_body___3: 'ASC'
mining_analytic_body___3: 'DESC'
mining_analytic_clause: identifier
mining_analytic_clause: order_by_clause
mining_analytic_clause: query_partition_clause
mining_analytic_clause: query_partition_clause order_by_clause
mining_attribute_clause: 'USING'
mining_attribute_clause: 'USING' mining_attribute_clause___0
mining_attribute_clause___0: '*'
mining_attribute_clause___0: mining_attribute_clause___1
mining_attribute_clause___0: mining_attribute_clause___1 mining_attribute_clause___3
mining_attribute_clause___1: expr
mining_attribute_clause___1: expr mining_attribute_clause___2
mining_attribute_clause___1: identifier '.' table '.' '*'
mining_attribute_clause___1: table '.' '*'
mining_attribute_clause___2: 'AS' alias
mining_attribute_clause___2: alias
mining_attribute_clause___3: ',' mining_attribute_clause___4
mining_attribute_clause___3: mining_attribute_clause___3 ',' mining_attribute_clause___4
mining_attribute_clause___4: expr
mining_attribute_clause___4: expr mining_attribute_clause___5
mining_attribute_clause___4: identifier '.' table '.' '*'
mining_attribute_clause___4: table '.' '*'
mining_attribute_clause___5: 'AS' alias
mining_attribute_clause___5: alias
mode: 'IN'
mode: 'IN' 'OUT'
mode: 'OUT'
model: 'FOR' identifier 'USING' '*'
model: identifier
model: identifier 'USING' '*'
model_clause: MODEL_kw cell_reference_options main_model
model_clause: MODEL_kw cell_reference_options model_clause___0 main_model
model_clause: MODEL_kw cell_reference_options return_rows_clause main_model
model_clause: MODEL_kw cell_reference_options return_rows_clause model_clause___0 main_model
model_clause: MODEL_kw main_model
model_clause: MODEL_kw model_clause___0 main_model
model_clause: MODEL_kw return_rows_clause main_model
model_clause: MODEL_kw return_rows_clause model_clause___0 main_model
model_clause___0: model_clause___0 reference_model
model_clause___0: reference_model
model_column_clauses: 'DIMENSION' 'BY' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')' 'MEASURES' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')'
model_column_clauses: 'DIMENSION' 'BY' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')' 'MEASURES' '(' "expr[c_alias]" ')'
model_column_clauses: 'DIMENSION' 'BY' '(' "expr[c_alias]" ')' 'MEASURES' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')'
model_column_clauses: 'DIMENSION' 'BY' '(' "expr[c_alias]" ')' 'MEASURES' '(' "expr[c_alias]" ')'
model_column_clauses: model_column_clauses___0 'DIMENSION' 'BY' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')' 'MEASURES' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')'
model_column_clauses: model_column_clauses___0 'DIMENSION' 'BY' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')' 'MEASURES' '(' "expr[c_alias]" ')'
model_column_clauses: model_column_clauses___0 'DIMENSION' 'BY' '(' "expr[c_alias]" ')' 'MEASURES' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')'
model_column_clauses: model_column_clauses___0 'DIMENSION' 'BY' '(' "expr[c_alias]" ')' 'MEASURES' '(' "expr[c_alias]" ')'
model_column_clauses___0: 'PARTITION' 'BY' '(' "expr[c_alias]" "{,expr[c_alias]}..." ')'
model_column_clauses___0: 'PARTITION' 'BY' '(' "expr[c_alias]" ')'
model_condition: is_any_condition
model_condition: is_present_condition
model_expression: analytic_function
model_expression: column '[' "cond_or_expr" ']'
model_expression: column '[' "cond_or_expr" model_expression___0 ']'
model_expression: model_expression___1 model_expression___2
model_expression___0: ',' "cond_or_expr"
model_expression___0: model_expression___0 ',' "cond_or_expr"
model_expression___1: aggregate_function
model_expression___1: user_defined_function
model_expression___2: '[' "cond_or_expr" ']'
model_expression___2: '[' "cond_or_expr" model_expression___3 ']'
model_expression___2: '[' multi_column_for_loop ']'
model_expression___2: '[' single_column_for_loop ']'
model_expression___2: '[' single_column_for_loop model_expression___4 ']'
model_expression___3: ',' "cond_or_expr"
model_expression___3: model_expression___3 ',' "cond_or_expr"
model_expression___4: ',' single_column_for_loop
model_expression___4: model_expression___4 ',' single_column_for_loop
model_iterate_clause: 'ITERATE' '(' bind_var ')'
model_iterate_clause: 'ITERATE' '(' bind_var ')' 'UNTIL' '(' condition ')'
model_iterate_clause: 'ITERATE' '(' numeric_literal ')'
model_iterate_clause: 'ITERATE' '(' numeric_literal ')' 'UNTIL' '(' condition ')'
model_iterate_clause: 'ITERATE' '(' numeric_literal ')' 'UNTIL' condition
model_rules_clause: '(' ')'
model_rules_clause: '(' model_rules_clause___1 ')'
model_rules_clause: model_rules_clause___2 '(' ')'
model_rules_clause: model_rules_clause___2 '(' model_rules_clause___1 ')'
model_rules_clause___0: 'AUTOMATIC'
model_rules_clause___0: 'SEQUENTIAL'
model_rules_clause___1: cell_assignment '=' expr
model_rules_clause___1: cell_assignment '=' expr model_rules_clause___3
model_rules_clause___1: cell_assignment order_by_clause '=' expr
model_rules_clause___1: cell_assignment order_by_clause '=' expr model_rules_clause___3
model_rules_clause___1: update_upsert_all cell_assignment '=' expr
model_rules_clause___1: update_upsert_all cell_assignment '=' expr model_rules_clause___3
model_rules_clause___1: update_upsert_all cell_assignment order_by_clause '=' expr
model_rules_clause___1: update_upsert_all cell_assignment order_by_clause '=' expr model_rules_clause___3
model_rules_clause___2: 'RULES'
model_rules_clause___2: 'RULES' model_iterate_clause
model_rules_clause___2: 'RULES' model_rules_clause___0 'ORDER'
model_rules_clause___2: 'RULES' model_rules_clause___0 'ORDER' model_iterate_clause
model_rules_clause___2: 'RULES' update_upsert_all
model_rules_clause___2: 'RULES' update_upsert_all model_iterate_clause
model_rules_clause___2: 'RULES' update_upsert_all model_rules_clause___0 'ORDER'
model_rules_clause___2: 'RULES' update_upsert_all model_rules_clause___0 'ORDER' model_iterate_clause
model_rules_clause___3: model_rules_clause___3 model_rules_clause___0#
model_rules_clause___3: model_rules_clause___0#
model_rules_clause___0#: ',' cell_assignment '=' expr
model_rules_clause___0#: ',' cell_assignment order_by_clause '=' expr
model_rules_clause___0#: ',' update_upsert_all cell_assignment '=' expr
model_rules_clause___0#: ',' update_upsert_all cell_assignment order_by_clause '=' expr
modified_external_table: 'EXTERNAL' 'MODIFY'
mult_op: '*'
mult_op: '/'
mult_op: 'MOD'
mult_op: 'REM'
mult_op: 'REMAINDER'
multi_column_for_loop: 'FOR' '(' identifier ')' 'IN' '(' multi_column_for_loop___1 ')'
multi_column_for_loop: 'FOR' '(' identifier multi_column_for_loop___0 ')' 'IN' '(' multi_column_for_loop___1 ')'
multi_column_for_loop___0: ',' identifier
multi_column_for_loop___0: multi_column_for_loop___0 ',' identifier
multi_column_for_loop___1: '(' expr ')'
multi_column_for_loop___1: '(' expr ')' multi_column_for_loop___3
multi_column_for_loop___1: '(' expr multi_column_for_loop___2 ')'
multi_column_for_loop___1: '(' expr multi_column_for_loop___2 ')' multi_column_for_loop___3
multi_column_for_loop___1: subquery
multi_column_for_loop___2: ',' expr
multi_column_for_loop___2: multi_column_for_loop___2 ',' expr
multi_column_for_loop___3: multi_column_for_loop___3 multi_column_for_loop___0#
multi_column_for_loop___3: multi_column_for_loop___0#
multi_column_for_loop___0#: ',' '(' expr ')'
multi_column_for_loop___0#: ',' '(' expr multi_column_for_loop___5 ')'
multi_column_for_loop___5: ',' expr
multi_column_for_loop___5: multi_column_for_loop___5 ',' expr
multi_table_insert: multi_table_insert___2 subquery
multi_table_insert___0: multi_table_insert___0 multi_table_insert___0#
multi_table_insert___0: multi_table_insert___0#
multi_table_insert___0#: insert_into_clause
multi_table_insert___0#: insert_into_clause error_logging_clause
multi_table_insert___0#: insert_into_clause values_clause
multi_table_insert___0#: insert_into_clause values_clause error_logging_clause
multi_table_insert___2: 'ALL' multi_table_insert___0
multi_table_insert___2: conditional_insert_clause
multiset_condition: is_a_set_condition
multiset_condition: is_empty_condition
multiset_condition: member_condition
multiset_condition: submultiset_condition
multiset_except: expr 'MULTISET' 'EXCEPT' expr
multiset_except: expr 'MULTISET' 'EXCEPT' multiset_except___0 expr
multiset_except___0: 'ALL'
multiset_except___0: 'DISTINCT'
multiset_expression: multiset_except
multiset_expression: multiset_intersect
multiset_expression: multiset_union
multiset_intersect: expr 'MULTISET' 'INTERSECT' expr
multiset_intersect: expr 'MULTISET' 'INTERSECT' multiset_intersect___0 expr
multiset_intersect___0: 'ALL'
multiset_intersect___0: 'DISTINCT'
multiset_op_intersect: 'INTERSECT' 'DISTINCT'
multiset_op_union_except: 'EXCEPT' 'DISTINCT'
multiset_op_union_except: 'UNION' 'DISTINCT'
multiset_term: combinable_multiset_term multiset_op_intersect pri
multiset_term_or_primary: multiset_term
multiset_term_or_primary: pri
multiset_union: expr 'MULTISET' 'UNION' expr
multiset_union: expr 'MULTISET' 'UNION' multiset_union___0 expr
multiset_union___0: 'ALL'
multiset_union___0: 'DISTINCT'
multiset_value_expression: combinable_multiset_expr multiset_op_union_except multiset_term_or_primary
multiset_value_expression: multiset_term
mutable_opt: 'IMMUTABLE'
mutable_opt: 'MUTABLE'
name: function_call
name: name_start
name: name_start name___0
name: name_wo_function_call
name___0: name___0 name_continue
name___0: name_continue
name_continue: '_'
name_continue: digit
name_continue: letter
name_list: name
name_list: name_list ',' name
name_start: '_'
name_start: letter
name_wo_function_call: "bind_var.INDICATOR_id."
name_wo_function_call: attribute
name_wo_function_call: dotted_expr
name_wo_function_call: identifier
named_member_keys: '[' identifier '=' expr ']'
named_member_keys: '[' identifier '=' expr named_member_keys___0 ']'
named_member_keys___0: named_member_keys___0 named_member_keys___0#
named_member_keys___0: named_member_keys___0#
named_member_keys___0#: ',' identifier '=' expr
national_w_opt_charset_spec: 'NATIONAL'
national_w_opt_charset_spec: 'NATIONAL' charset_spec_opt
nested_clause: nested_clause nested_clause
nested_clause: nested_clause_1
nested_clause: nested_clause_1 identifier
nested_clause_1: "nested_clause_1___1" "nested_clause_1___2" JSON_columns_clause
nested_clause_1: "nested_clause_1___1" "nested_clause_1___2" JSON_table_on_error_clause JSON_columns_clause
nested_clause_1: "nested_clause_1___1" JSON_columns_clause
nested_clause_1: "nested_clause_1___1" JSON_table_on_error_clause JSON_columns_clause
nonNullType: identifier '!'
nonNullType: listType '!'
not_eq: '<' '>'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' 'OVER'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' 'OVER' '(' ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' 'OVER' '(' analytic_clause ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' 'OVER' analytic_clause
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last 'OVER'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last 'OVER' '(' ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last 'OVER' '(' analytic_clause ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last 'OVER' analytic_clause
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last respect_ignore_nulls 'OVER'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last respect_ignore_nulls 'OVER' '(' ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last respect_ignore_nulls 'OVER' '(' analytic_clause ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' from_first_last respect_ignore_nulls 'OVER' analytic_clause
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' respect_ignore_nulls 'OVER'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' respect_ignore_nulls 'OVER' '(' ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' respect_ignore_nulls 'OVER' '(' analytic_clause ')'
nth_value: 'NTH_VALUE' '(' measure_expr ',' measure_expr ')' respect_ignore_nulls 'OVER' analytic_clause
null_condition: expr 'IS' 'NOT' 'NULL'
null_condition: expr 'IS' 'NULL'
number_datatypes: 'BINARY_DOUBLE'
number_datatypes: 'BINARY_FLOAT'
number_datatypes: 'FLOAT'
number_datatypes: 'FLOAT' number_datatypes___1
number_datatypes: 'NUMBER'
number_datatypes: 'NUMBER' number_datatypes___0
number_datatypes___0: '(' precision ')'
number_datatypes___0: '(' precision ',' scale ')'
number_datatypes___1: '(' precision ')'
numeric_literal: '.' digits
numeric_literal: '.' digits ".exp."
numeric_literal: '.' digits ".exp." ".fd."
numeric_literal: '.' digits ".fd."
numeric_literal: digits
numeric_literal: digits ".exp."
numeric_literal: digits ".exp." ".fd."
numeric_literal: digits ".fd."
numeric_literal: digits '.'
numeric_literal: digits '.' ".exp."
numeric_literal: digits '.' ".exp." ".fd."
numeric_literal: digits '.' ".fd."
numeric_literal: digits '.' digits
numeric_literal: digits '.' digits ".exp."
numeric_literal: digits '.' digits ".exp." ".fd."
numeric_literal: digits '.' digits ".fd."
objectField: identifier ':' value
objectValue: '{' "objectValue___0" '}'
objectValue: '{' '}'
object_access_expression: object_access_expression___4 object_access_expression___0
object_access_expression___0: identifier
object_access_expression___0: identifier object_access_expression___1
object_access_expression___0: identifier object_access_expression___1 object_access_expression___2
object_access_expression___0: identifier object_access_expression___2
object_access_expression___0: method_call
object_access_expression___0: method_call object_access_expression___3
object_access_expression___1: '.' identifier
object_access_expression___1: object_access_expression___1 '.' identifier
object_access_expression___2: '.' method_call
object_access_expression___2: object_access_expression___2 '.' method_call
object_access_expression___3: '.' method_call
object_access_expression___3: object_access_expression___3 '.' method_call
object_access_expression___4: '(' expr ')' '.'
object_access_expression___4: identifier '.'
object_access_expression___4: identifier '.' column '.'
object_gen_clause: '*'
object_gen_clause: 'JSON' '{' key_value_clause "object_gen_clause___0" '}'
object_gen_clause: 'JSON' '{' key_value_clause '}'
object_gen_clause: json_object
object_type_name: identifier
object_type_name: identifier '.' identifier
objectname: "objectname___0" filename
objectname: filename
on_using_condition: 'ON' condition
on_using_condition: 'USING' '(' column ')'
on_using_condition: 'USING' '(' column on_using_condition___0 ')'
on_using_condition___0: ',' column
on_using_condition___0: on_using_condition___0 ',' column
opt_FINAL_RUNNING: 'FINAL'
opt_FINAL_RUNNING: 'RUNNING'
opt_topn: ',' digits
optional_element_pattern_filter: identifier
optional_element_pattern_filter: identifier is_label_expression
optional_element_pattern_filter: identifier is_label_expression where_clause_w_boolean
optional_element_pattern_filter: identifier where_clause_w_boolean
optional_element_pattern_filter: is_label_expression
optional_element_pattern_filter: is_label_expression where_clause_w_boolean
optional_element_pattern_filter: where_clause_w_boolean
ora_dm_partition_name: 'ORA_DM_PARTITION_NAME' '(' identifier '.' model mining_attribute_clause ')'
ora_dm_partition_name: 'ORA_DM_PARTITION_NAME' '(' model mining_attribute_clause ')'
order_by_clause: 'ORDER' 'BY' "ord_by_1desc"
order_by_clause: 'ORDER' 'BY' "ord_by_1desc" order_by_clause___0
order_by_clause: 'ORDER' 'SIBLINGS' 'BY' "ord_by_1desc"
order_by_clause: 'ORDER' 'SIBLINGS' 'BY' "ord_by_1desc" order_by_clause___0
order_by_clause___0: ',' "ord_by_1desc"
order_by_clause___0: order_by_clause___0 ',' "ord_by_1desc"
ordered_c_alias: c_alias
ordered_c_alias: c_alias ordered_c_alias___0
ordered_c_alias___0: 'ASC'
ordered_c_alias___0: 'DESC'
out_of_line_constraint: 'CONSTRAINT' identifier out_of_line_constraint___0
out_of_line_constraint: out_of_line_constraint___0
out_of_line_constraint___0: 'CHECK' '(' condition ')'
out_of_line_constraint___0: 'CHECK' '(' condition ')' constraint_state
out_of_line_constraint___0: 'CHECK' '(' condition ')' constraint_state precheck_state
out_of_line_constraint___0: 'CHECK' '(' condition ')' precheck_state
out_of_line_constraint___0: out_of_line_constraint___1
out_of_line_constraint___0: out_of_line_constraint___1 constraint_state
out_of_line_constraint___1: 'FOREIGN' 'KEY' '(' column ')' references_clause
out_of_line_constraint___1: 'FOREIGN' 'KEY' '(' column out_of_line_constraint___4 ')' references_clause
out_of_line_constraint___1: 'PRIMARY' 'KEY' '(' column ')'
out_of_line_constraint___1: 'PRIMARY' 'KEY' '(' column out_of_line_constraint___3 ')'
out_of_line_constraint___1: 'UNIQUE' '(' column ')'
out_of_line_constraint___1: 'UNIQUE' '(' column out_of_line_constraint___2 ')'
out_of_line_constraint___2: ',' column
out_of_line_constraint___2: out_of_line_constraint___2 ',' column
out_of_line_constraint___3: ',' column
out_of_line_constraint___3: out_of_line_constraint___3 ',' column
out_of_line_constraint___4: ',' column
out_of_line_constraint___4: out_of_line_constraint___4 ',' column
out_of_line_ref_constraint: 'CONSTRAINT' identifier 'FOREIGN' 'KEY' '(' identifier ')' references_clause
out_of_line_ref_constraint: 'CONSTRAINT' identifier 'FOREIGN' 'KEY' '(' identifier ')' references_clause constraint_state
out_of_line_ref_constraint: 'FOREIGN' 'KEY' '(' identifier ')' references_clause
out_of_line_ref_constraint: 'FOREIGN' 'KEY' '(' identifier ')' references_clause constraint_state
out_of_line_ref_constraint: 'REF' '(' identifier ')' 'WITH' 'ROWID'
out_of_line_ref_constraint: 'SCOPE' 'FOR' '(' identifier ')' 'IS' identifier
out_of_line_ref_constraint: 'SCOPE' 'FOR' '(' identifier ')' 'IS' identifier '.' identifier
outer_join_type: outer_join_type___0
outer_join_type: outer_join_type___0 'OUTER'
outer_join_type___0: 'FULL'
outer_join_type___0: 'LEFT'
outer_join_type___0: 'RIGHT'
over_clause: 'OVER' '(' ')'
over_clause: 'OVER' '(' analytic_clause ')'
overlaps_condition: '(' "expr_list" ')' 'NOT' 'OVERLAPS' '(' overlaps_condition___0 ')'
overlaps_condition: '(' "expr_list" ')' 'OVERLAPS' '(' overlaps_condition___0 ')'
overlaps_condition___0: "expression_list"
overlaps_condition___0: "expression_list" overlaps_condition___1
overlaps_condition___1: ',' "expression_list"
overlaps_condition___1: overlaps_condition___1 ',' "expression_list"
par_expr_list: '(' par_expr_list___2 ')'
par_expr_list: '(' par_expr_list___2 par_expr_list___0 ')'
par_expr_list___0: ',' par_expr_list___1
par_expr_list___0: par_expr_list___0 ',' par_expr_list___1
par_expr_list___1: 'DEFAULT'
par_expr_list___1: expr
par_expr_list___2: 'DEFAULT'
par_expr_list___2: expr
par_subquery: '(' 'VALUES' '(' expr ')' ')'
par_subquery: '(' 'VALUES' '(' expr ')' par_subquery___1 ')'
par_subquery: '(' 'VALUES' '(' expr par_subquery___0 ')' ')'
par_subquery: '(' 'VALUES' '(' expr par_subquery___0 ')' par_subquery___1 ')'
par_subquery: '(' subquery ')'
par_subquery: values_clause
par_subquery___0: ',' expr
par_subquery___0: par_subquery___0 ',' expr
par_subquery___1: expr
par_subquery___1: expr par_subquery___2
par_subquery___2: ',' expr
par_subquery___2: par_subquery___2 ',' expr
paren_aggr: '(' assoc_arg ')'
paren_aggr: '(' assoc_arg arg_list ')'
paren_aggr: '(' pls_expr ')'
paren_aggr: '(' pls_expr arg_list ')'
paren_expr_list: '(' arg ')'
paren_expr_list: '(' arg arg_list ')'
parenthesized_group_by_list: '(' parenthesized_group_by_list ')'
parenthesized_group_by_list: group_by_list
parenthesized_path_pattern_expression: '(' path_term ')'
parenthesized_path_pattern_expression: '(' path_term where_clause_w_boolean ')'
parenthesized_path_pattern_expression: '[' path_term ']'
parenthesized_path_pattern_expression: '[' path_term where_clause_w_boolean ']'
parm_list_opt: ',' prm_spec
parm_list_opt: parm_list_opt ',' prm_spec
partition_extension_clause: 'PARTITION' '(' expr ')'
partition_extension_clause: 'PARTITION' 'FOR' '(' expr ')'
partition_extension_clause: 'PARTITION' 'FOR' '(' expr partition_extension_clause___0 ')'
partition_extension_clause: 'SUBPARTITION' '(' expr ')'
partition_extension_clause: 'SUBPARTITION' 'FOR' '(' expr ')'
partition_extension_clause: 'SUBPARTITION' 'FOR' '(' expr partition_extension_clause___1 ')'
partition_extension_clause___0: ',' expr
partition_extension_clause___0: partition_extension_clause___0 ',' expr
partition_extension_clause___1: ',' expr
partition_extension_clause___1: partition_extension_clause___1 ',' expr
path_concatenation: path_term path_factor
path_factor: path_primary
path_factor: quantified_path_primary
path_pattern_list: path_term
path_pattern_list: path_term "path_pattern_list___0"
path_primary: element_pattern
path_primary: parenthesized_path_pattern_expression
path_term: path_concatenation
path_term: path_factor
pattern: bind_var
pattern: string_literal
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' percent_rank_aggregate___3 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' percent_rank_aggregate___3 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 'NULLS' percent_rank_aggregate___3 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 'NULLS' percent_rank_aggregate___3 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' percent_rank_aggregate___3 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' percent_rank_aggregate___3 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 'NULLS' percent_rank_aggregate___3 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 'NULLS' percent_rank_aggregate___3 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___2 percent_rank_aggregate___4 ')'
percent_rank_aggregate: 'PERCENT_RANK' '(' expr percent_rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr percent_rank_aggregate___4 ')'
percent_rank_aggregate___0: 'FIRST'
percent_rank_aggregate___0: 'LAST'
percent_rank_aggregate___1: ',' expr
percent_rank_aggregate___1: percent_rank_aggregate___1 ',' expr
percent_rank_aggregate___2: 'ASC'
percent_rank_aggregate___2: 'DESC'
percent_rank_aggregate___3: 'FIRST'
percent_rank_aggregate___3: 'LAST'
percent_rank_aggregate___4: percent_rank_aggregate___4 percent_rank_aggregate___0#
percent_rank_aggregate___4: percent_rank_aggregate___0#
percent_rank_aggregate___0#: ',' expr
percent_rank_aggregate___0#: ',' expr 'NULLS' percent_rank_aggregate___0
percent_rank_aggregate___0#: ',' expr percent_rank_aggregate___6
percent_rank_aggregate___0#: ',' expr percent_rank_aggregate___6 'NULLS' percent_rank_aggregate___0
percent_rank_aggregate___6: 'ASC'
percent_rank_aggregate___6: 'DESC'
percentile_cont: 'PERCENTILE_CONT' '(' expr ')' 'WITHIN' 'GROUP' '(' order_by_clause ')'
percentile_cont: 'PERCENTILE_CONT' '(' expr ')' 'WITHIN' 'GROUP' '(' order_by_clause ')' percentile_cont___0
percentile_cont___0: 'OVER' '(' ')'
percentile_cont___0: 'OVER' '(' query_partition_clause ')'
percentile_disc: 'PERCENTILE_DISC' '(' expr ')' 'WITHIN' 'GROUP' '(' order_by_clause ')'
percentile_disc: 'PERCENTILE_DISC' '(' expr ')' 'WITHIN' 'GROUP' '(' order_by_clause ')' percentile_disc___0
percentile_disc___0: 'OVER' '(' ')'
percentile_disc___0: 'OVER' '(' query_partition_clause ')'
permutted_merge_insert_update: merge_insert_clause
permutted_merge_insert_update: merge_insert_clause merge_update_clause
permutted_merge_insert_update: merge_update_clause
permutted_merge_insert_update: merge_update_clause merge_insert_clause
pivot_clause: 'PIVOT' '(' expr as_alias pivot_clause___0 pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' '(' expr as_alias pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' '(' expr pivot_clause___0 pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' '(' expr pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' 'XML' '(' expr as_alias pivot_clause___0 pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' 'XML' '(' expr as_alias pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' 'XML' '(' expr pivot_clause___0 pivot_for_clause pivot_in_clause ')'
pivot_clause: 'PIVOT' 'XML' '(' expr pivot_for_clause pivot_in_clause ')'
pivot_clause___0: pivot_clause___0 pivot_clause___0#
pivot_clause___0: pivot_clause___0#
pivot_clause___0#: ',' expr
pivot_clause___0#: ',' expr as_alias
pivot_for_clause: 'FOR' pivot_for_clause___0
pivot_for_clause___0: '(' column ')'
pivot_for_clause___0: '(' column pivot_for_clause___1 ')'
pivot_for_clause___0: column
pivot_for_clause___1: ',' column
pivot_for_clause___1: pivot_for_clause___1 ',' column
pivot_in_clause: 'IN' '(' ')'
pivot_in_clause: 'IN' '(' pivot_in_clause___0 ')'
pivot_in_clause___0: 'ANY'
pivot_in_clause___0: 'ANY' pivot_in_clause___2
pivot_in_clause___0: expr_as_alias_
pivot_in_clause___0: expr_as_alias_ pivot_in_clause___1
pivot_in_clause___0: subquery
pivot_in_clause___1: ',' expr_as_alias_
pivot_in_clause___1: pivot_in_clause___1 ',' expr_as_alias_
pivot_in_clause___2: ',' 'ANY'
pivot_in_clause___2: pivot_in_clause___2 ',' 'ANY'
pls_expr: and_expr
pls_expr: multiset_expression
pls_expr: pls_expr 'OR' and_expr
pls_number_datatypes: 'BINARY_DOUBLE'
pls_number_datatypes: 'BINARY_FLOAT'
pls_number_datatypes: 'BINARY_INTEGER'
pls_number_datatypes: 'BOOLEAN'
pls_number_datatypes: 'DEC'
pls_number_datatypes: 'DECIMAL'
pls_number_datatypes: 'DOUBLE' 'PRECISION'
pls_number_datatypes: 'FLOAT'
pls_number_datatypes: 'INT'
pls_number_datatypes: 'INTEGER'
pls_number_datatypes: 'NATURAL'
pls_number_datatypes: 'NATURALN'
pls_number_datatypes: 'NUMBER'
pls_number_datatypes: 'NUMERIC'
pls_number_datatypes: 'PLS_INTEGER'
pls_number_datatypes: 'POSITIVE'
pls_number_datatypes: 'POSITIVEN'
pls_number_datatypes: 'REAL'
pls_number_datatypes: 'SIGNTYPE'
pls_number_datatypes: 'SIMPLE_DOUBLE'
pls_number_datatypes: 'SIMPLE_FLOAT'
pls_number_datatypes: 'SIMPLE_INTEGER'
pls_number_datatypes: 'SMALLINT'
plsql_declarations: plsql_declarations subprg_body
plsql_declarations: subprg_body
pos_member_keys: '[' expr ']'
pos_member_keys: '[' expr pos_member_keys___0 ']'
pos_member_keys___0: ',' expr
pos_member_keys___0: pos_member_keys___0 ',' expr
preceding_boundary: preceding_boundary___0 'AND' preceding_boundary___1
preceding_boundary___0: 'UNBOUNDED' 'PRECEDING'
preceding_boundary___0: expr 'PRECEDING'
preceding_boundary___1: 'CURRENT' 'MEMBER'
preceding_boundary___1: 'UNBOUNDED' 'FOLLOWING'
preceding_boundary___1: expr preceding_boundary___2
preceding_boundary___2: 'FOLLOWING'
preceding_boundary___2: 'PRECEDING'
precheck_state: 'NOPRECHECK'
precheck_state: 'PRECHECK'
precision: '*'
precision: digits
prediction: 'PREDICTION' '(' grouping_hint identifier '.' model cost_matrix_clause mining_attribute_clause ')'
prediction: 'PREDICTION' '(' grouping_hint identifier '.' model mining_attribute_clause ')'
prediction: 'PREDICTION' '(' grouping_hint model cost_matrix_clause mining_attribute_clause ')'
prediction: 'PREDICTION' '(' grouping_hint model mining_attribute_clause ')'
prediction: 'PREDICTION' '(' identifier '.' model cost_matrix_clause mining_attribute_clause ')'
prediction: 'PREDICTION' '(' identifier '.' model mining_attribute_clause ')'
prediction: 'PREDICTION' '(' model cost_matrix_clause mining_attribute_clause ')'
prediction: 'PREDICTION' '(' model mining_attribute_clause ')'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' ')'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' ')'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_analytic: 'PREDICTION' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_bounds: 'PREDICTION_BOUNDS' '(' identifier '.' model mining_attribute_clause ')'
prediction_bounds: 'PREDICTION_BOUNDS' '(' identifier '.' model prediction_bounds___0 mining_attribute_clause ')'
prediction_bounds: 'PREDICTION_BOUNDS' '(' model mining_attribute_clause ')'
prediction_bounds: 'PREDICTION_BOUNDS' '(' model prediction_bounds___0 mining_attribute_clause ')'
prediction_bounds___0: ',' literal
prediction_bounds___0: ',' literal ',' literal
prediction_cost: 'PREDICTION_COST' '(' identifier '.' model ',' literal cost_matrix_clause mining_attribute_clause ')'
prediction_cost: 'PREDICTION_COST' '(' identifier '.' model cost_matrix_clause mining_attribute_clause ')'
prediction_cost: 'PREDICTION_COST' '(' model ',' literal cost_matrix_clause mining_attribute_clause ')'
prediction_cost: 'PREDICTION_COST' '(' model cost_matrix_clause mining_attribute_clause ')'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion ',' string_literal cost_matrix_clause mining_attribute_clause ')' 'OVER'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion ',' string_literal cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' ')'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion ',' string_literal cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion ',' string_literal cost_matrix_clause mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' ')'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_cost_analytic: 'PREDICTION_COST' '(' anomalouS_eXpreSsion cost_matrix_clause mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_details: 'PREDICTION_DETAILS' '(' identifier '.' model mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' identifier '.' model prediction_details___0 mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' identifier '.' model prediction_details___0 prediction_details___1 mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' identifier '.' model prediction_details___1 mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' model mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' model prediction_details___0 mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' model prediction_details___0 prediction_details___1 mining_attribute_clause ')'
prediction_details: 'PREDICTION_DETAILS' '(' model prediction_details___1 mining_attribute_clause ')'
prediction_details___0: ',' expr
prediction_details___0: ',' expr ',' digits
prediction_details___1: 'ABS'
prediction_details___1: 'ASC'
prediction_details___1: 'DESC'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___0 mining_attribute_clause ')' 'OVER' '(' ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___0 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___0 prediction_details_analytic___1 mining_attribute_clause ')' 'OVER' '(' ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___0 prediction_details_analytic___1 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___1 mining_attribute_clause ')' 'OVER' '(' ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___1 mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___2 mining_attribute_clause ')' 'OVER'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___2 mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___2 prediction_details_analytic___3 mining_attribute_clause ')' 'OVER'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___2 prediction_details_analytic___3 mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___3 mining_attribute_clause ')' 'OVER'
prediction_details_analytic: 'PREDICTION_DETAILS' '(' anomalouS_eXpreSsion prediction_details_analytic___3 mining_attribute_clause ')' 'OVER' mining_analytic_clause
prediction_details_analytic___0: ',' expr
prediction_details_analytic___0: ',' expr ',' digits
prediction_details_analytic___1: 'ABS'
prediction_details_analytic___1: 'ASC'
prediction_details_analytic___1: 'DESC'
prediction_details_analytic___2: ',' expr
prediction_details_analytic___2: ',' expr ',' digits
prediction_details_analytic___3: 'ABS'
prediction_details_analytic___3: 'ASC'
prediction_details_analytic___3: 'DESC'
prediction_prob_analytic: 'PREDICTION_PROBABILITY' '(' anomalouS_eXpreSsion ',' expr mining_attribute_clause ')' 'OVER' '(' ')'
prediction_prob_analytic: 'PREDICTION_PROBABILITY' '(' anomalouS_eXpreSsion ',' expr mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_prob_analytic: 'PREDICTION_PROBABILITY' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' ')'
prediction_prob_analytic: 'PREDICTION_PROBABILITY' '(' anomalouS_eXpreSsion mining_attribute_clause ')' 'OVER' '(' mining_analytic_clause ')'
prediction_probability: 'PREDICTION_PROBABILITY' '(' identifier '.' model ',' literal mining_attribute_clause ')'
prediction_probability: 'PREDICTION_PROBABILITY' '(' identifier '.' model mining_attribute_clause ')'
prediction_probability: 'PREDICTION_PROBABILITY' '(' model ',' literal mining_attribute_clause ')'
prediction_probability: 'PREDICTION_PROBABILITY' '(' model mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model cost_matrix_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model cost_matrix_clause mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model prediction_set___0 ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model prediction_set___0 cost_matrix_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model prediction_set___0 cost_matrix_clause mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' identifier '.' model prediction_set___0 mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' model ')'
prediction_set: 'PREDICTION_SET' '(' model cost_matrix_clause ')'
prediction_set: 'PREDICTION_SET' '(' model cost_matrix_clause mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' model mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' model prediction_set___0 ')'
prediction_set: 'PREDICTION_SET' '(' model prediction_set___0 cost_matrix_clause ')'
prediction_set: 'PREDICTION_SET' '(' model prediction_set___0 cost_matrix_clause mining_attribute_clause ')'
prediction_set: 'PREDICTION_SET' '(' model prediction_set___0 mining_attribute_clause ')'
prediction_set___0: ',' literal
prediction_set___0: ',' literal ',' literal
prediction_set_analytic: prediction_set 'OVER'
prediction_set_analytic: prediction_set 'OVER' '(' ')'
prediction_set_analytic: prediction_set 'OVER' '(' mining_analytic_clause ')'
prediction_set_analytic: prediction_set 'OVER' mining_analytic_clause
pri: '(' pls_expr 'AS' unconstrained_type ')'
pri: 'NEW' procedure_call
pri: 'NULL'
pri: alternatively_quoted_nonnative_string_literal
pri: alternatively_quoted_string_literal
pri: case_expr
pri: conversion_function
pri: datetime_literal
pri: gen_call
pri: nonnative_string_literal
pri: numeric_literal
pri: paren_aggr
pri: procedure_call
pri: string_literal
prm_spec: decl_id 'ELLIPSIS'
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY'
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY' charset_spec_opt
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY' charset_spec_opt default_expr_opt
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY' default_expr_opt
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY' prm_spec_unconstrained_type
prm_spec: decl_id 'IN' 'OUT' 'NOCOPY' prm_spec_unconstrained_type default_expr_opt
prm_spec: decl_id 'OUT' 'NOCOPY'
prm_spec: decl_id 'OUT' 'NOCOPY' charset_spec_opt
prm_spec: decl_id 'OUT' 'NOCOPY' charset_spec_opt default_expr_opt
prm_spec: decl_id 'OUT' 'NOCOPY' default_expr_opt
prm_spec: decl_id 'OUT' 'NOCOPY' prm_spec_unconstrained_type
prm_spec: decl_id 'OUT' 'NOCOPY' prm_spec_unconstrained_type default_expr_opt
prm_spec: decl_id mode prm_spec_unconstrained_type
prm_spec: decl_id mode prm_spec_unconstrained_type default_expr_opt
prm_spec: decl_id prm_spec_unconstrained_type
prm_spec: decl_id prm_spec_unconstrained_type default_expr_opt
prm_spec_unconstrained_type: national_w_opt_charset_spec
prm_spec_unconstrained_type: unconstrained_type_wo_national
prm_spec_unconstrained_type: unconstrained_type_wo_national charset_spec_opt
proc_interface_opt: 'WITH' 'INTERFACE' interface_proc_spec
procedure_call: name
procedure_call: name_wo_function_call '@' dblink
procedure_call: name_wo_function_call '@' dblink empty_parens_opt
procedure_call: name_wo_function_call '@' dblink paren_expr_list
qdr_expression: 'QUALIFY' '(' calc_meas_expression ',' qualifier ')'
qualifier: "qualifier___0" '/' 'O' '/' objectname
qualifier: '/' 'O' '/' objectname
qualifier: hierarchy_ref '=' member_expression
quantified_path_primary: path_primary graph_pattern_quantifier
query_block: dummyRule
query_block: notToReduce
query_partition_clause: 'PARTITION' 'BY' query_partition_clause___0
query_partition_clause___0: '(' expr ')'
query_partition_clause___0: '(' expr query_partition_clause___2 ')'
query_partition_clause___0: expr
query_partition_clause___0: expr query_partition_clause___1
query_partition_clause___1: ',' expr
query_partition_clause___1: query_partition_clause___1 ',' expr
query_partition_clause___2: ',' expr
query_partition_clause___2: query_partition_clause___2 ',' expr
query_table_expression: '(' query_table_expression ')'
query_table_expression: '(' subquery ')'
query_table_expression: '(' subquery subquery_restriction_clause ')'
query_table_expression: 'ANALYTIC' 'VIEW' '(' sub_av_clause ')'
query_table_expression: 'LATERAL' '(' subquery ')'
query_table_expression: 'LATERAL' '(' subquery subquery_restriction_clause ')'
query_table_expression: 'LATERAL' '(' subquery subquery_restriction_clause ')'
query_table_expression: function_expression
query_table_expression: graph_table
query_table_expression: identifier
query_table_expression: identifier '.' query_table_expression___0
query_table_expression: identifier '.' query_table_expression___0 sample_clause
query_table_expression: jsonize
query_table_expression: query_table_expression___0
query_table_expression: query_table_expression___0 sample_clause
query_table_expression: table_collection_expression
query_table_expression: xmltable
query_table_expression___0: identifier
query_table_expression___0: identifier query_table_expression___2
query_table_expression___0: inline_external_table
query_table_expression___0: table
query_table_expression___0: table query_table_expression___1
query_table_expression___1: '@' dblink
query_table_expression___1: modified_external_table
query_table_expression___1: partition_extension_clause
query_table_expression___2: 'HIERARCHIES' '(' ')'
query_table_expression___2: 'HIERARCHIES' '(' query_table_expression___3 ')'
query_table_expression___3: hier_ref
query_table_expression___3: hier_ref query_table_expression___4
query_table_expression___4: ',' hier_ref
query_table_expression___4: query_table_expression___4 ',' hier_ref
quotedString: OPEN_QUOTE CLOSING_QUOTE
quotedString: OPEN_QUOTE REGULAR_STRING_PART CLOSING_QUOTE
range: name
range: sim_expr_DBLDOT__sim_expr
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' rank_aggregate___3 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' rank_aggregate___3 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 'NULLS' rank_aggregate___3 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 'NULLS' rank_aggregate___3 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' rank_aggregate___3 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr 'NULLS' rank_aggregate___3 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 'NULLS' rank_aggregate___3 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 'NULLS' rank_aggregate___3 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___2 rank_aggregate___4 ')'
rank_aggregate: 'RANK' '(' expr rank_aggregate___1 ')' 'WITHIN' 'GROUP' '(' 'ORDER' 'BY' expr rank_aggregate___4 ')'
rank_aggregate___0: 'FIRST'
rank_aggregate___0: 'LAST'
rank_aggregate___1: ',' expr
rank_aggregate___1: rank_aggregate___1 ',' expr
rank_aggregate___2: 'ASC'
rank_aggregate___2: 'DESC'
rank_aggregate___3: 'FIRST'
rank_aggregate___3: 'LAST'
rank_aggregate___4: rank_aggregate___4 rank_aggregate___0#
rank_aggregate___4: rank_aggregate___0#
rank_aggregate___0#: ',' expr
rank_aggregate___0#: ',' expr 'NULLS' rank_aggregate___0
rank_aggregate___0#: ',' expr rank_aggregate___6
rank_aggregate___0#: ',' expr rank_aggregate___6 'NULLS' rank_aggregate___0
rank_aggregate___6: 'ASC'
rank_aggregate___6: 'DESC'
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause 'WITHIN' rank_clause___1
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause 'WITHIN' rank_clause___1 rank_clause___2
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause rank_clause___0
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause rank_clause___0 'WITHIN' rank_clause___1
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause rank_clause___0 'WITHIN' rank_clause___1 rank_clause___2
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause rank_clause___0 rank_clause___2
rank_clause: 'HIERARCHY' hierarchy_ref 'ORDER' 'BY' calc_meas_order_by_clause rank_clause___2
rank_clause___0: ',' calc_meas_order_by_clause
rank_clause___0: rank_clause___0 ',' calc_meas_order_by_clause
rank_clause___1: 'ANCESTOR' 'AT' 'LEVEL' identifier
rank_clause___1: 'LEVEL'
rank_clause___1: 'PARENT'
rank_clause___2: rank_clause___3 'WHEN' 'NULL'
rank_clause___3: 'INCLUDE'
rank_clause___3: 'SKIP'
rank_expression: rank_function_name '(' ')' 'OVER' '(' rank_clause ')'
rank_function_name: 'AVERAGE_RANK'
rank_function_name: 'DENSE_RANK'
rank_function_name: 'RANK'
rank_function_name: 'ROW_NUMBER'
reference_model: 'REFERENCE' identifier 'ON' '(' subquery ')' model_column_clauses
reference_model: 'REFERENCE' identifier 'ON' '(' subquery ')' model_column_clauses cell_reference_options
references_clause: 'REFERENCES' identifier
references_clause: 'REFERENCES' identifier '.' identifier
references_clause: 'REFERENCES' identifier '.' identifier references_clause___0
references_clause: 'REFERENCES' identifier '.' identifier references_clause___0 references_clause___2
references_clause: 'REFERENCES' identifier '.' identifier references_clause___2
references_clause: 'REFERENCES' identifier references_clause___0
references_clause: 'REFERENCES' identifier references_clause___0 references_clause___2
references_clause: 'REFERENCES' identifier references_clause___2
references_clause___0: '(' column ')'
references_clause___0: '(' column references_clause___1 ')'
references_clause___1: ',' column
references_clause___1: references_clause___1 ',' column
references_clause___2: 'ON' 'DELETE' references_clause___3
references_clause___3: 'CASCADE'
references_clause___3: 'SET' 'NULL'
regexp_like_condition: 'REGEXP_LIKE' '(' expr ',' expr ')'
regexp_like_condition: 'REGEXP_LIKE' '(' expr ',' expr ',' expr ')'
regular_entry: 'KEY' expr 'VALUE' expr
regular_entry: column
regular_entry: expr
regular_entry: expr "regular_entry___0"
regular_entry: expr 'VALUE' expr
reject_limit_unlimited: 'REJECT' 'LIMIT' reject_limit_unlimited___0
reject_limit_unlimited___0: 'UNLIMITED'
reject_limit_unlimited___0: integer
rel: 'NOT' boolean_primary
rel: boolean_primary
relal_op: '!' '='
relal_op: '!' '=' 'ALL'
relal_op: '!' '=' 'ANY'
relal_op: '!' '=' 'SOME'
relal_op: '<'
relal_op: '<' '='
relal_op: '<' '=' 'ALL'
relal_op: '<' '=' 'ANY'
relal_op: '<' '=' 'SOME'
relal_op: '<' '>'
relal_op: '<' '>' 'ALL'
relal_op: '<' '>' 'ANY'
relal_op: '<' '>' 'SOME'
relal_op: '<' 'ALL'
relal_op: '<' 'ANY'
relal_op: '<' 'SOME'
relal_op: '='
relal_op: '=' 'ALL'
relal_op: '=' 'ANY'
relal_op: '=' 'SOME'
relal_op: '>'
relal_op: '>' '='
relal_op: '>' '=' 'ALL'
relal_op: '>' '=' 'ANY'
relal_op: '>' '=' 'SOME'
relal_op: '>' 'ALL'
relal_op: '>' 'ANY'
relal_op: '>' 'SOME'
relal_op_sim_expr_opt: relal_op sim_expr
removeOp: 'REMOVE' string_literal
removeOp: 'REMOVE' string_literal "removeOp___0"
renameOp: 'RENAME' string_literal "renameOp___2" string_literal
renameOp: 'RENAME' string_literal "renameOp___2" string_literal "renameOp___0"
replaceOp: 'REPLACE' string_literal '=' rhsExpr
replaceOp: 'REPLACE' string_literal '=' rhsExpr "replaceOp___0"
replaceOp: 'REPLACE' string_literal '=' rhsExpr "replaceOp___2"
replaceOp: 'REPLACE' string_literal '=' rhsExpr "replaceOp___2" "replaceOp___0"
respect_ignore_nulls: respect_ignore_nulls___0 'NULLS'
respect_ignore_nulls___0: 'IGNORE'
respect_ignore_nulls___0: 'RESPECT'
return_rows_clause: 'RETURN' return_rows_clause___0 'ROWS'
return_rows_clause___0: 'ALL'
return_rows_clause___0: 'UPDATED'
returning_clause: returning_clause___1 expr 'INTO' data_item
returning_clause: returning_clause___1 expr 'INTO' data_item returning_clause___2
returning_clause: returning_clause___1 expr BULK_COLLECT_opt 'INTO' data_item
returning_clause: returning_clause___1 expr BULK_COLLECT_opt 'INTO' data_item returning_clause___2
returning_clause: returning_clause___1 expr returning_clause___0 'INTO' data_item
returning_clause: returning_clause___1 expr returning_clause___0 'INTO' data_item returning_clause___2
returning_clause: returning_clause___1 expr returning_clause___0 BULK_COLLECT_opt 'INTO' data_item
returning_clause: returning_clause___1 expr returning_clause___0 BULK_COLLECT_opt 'INTO' data_item returning_clause___2
returning_clause___0: ',' expr
returning_clause___0: returning_clause___0 ',' expr
returning_clause___1: 'RETURN'
returning_clause___1: 'RETURNING'
returning_clause___2: ',' data_item
returning_clause___2: returning_clause___2 ',' data_item
rhsExpr: 'PATH' expr
rhsExpr: 'PATH' expr "rhsExpr___0"
rhsExpr: expr
rhsExpr: expr "rhsExpr___0"
rollup_cube_clause: rollup_cube_clause___0 '(' grouping_expression_list ')'
rollup_cube_clause___0: 'CUBE'
rollup_cube_clause___0: 'ROLLUP'
row_limiting_clause: row_limiting_clause___1
row_limiting_clause: row_limiting_clause___2
row_limiting_clause: row_limiting_clause___2 row_limiting_clause___1
row_limiting_clause___0: 'ROW'
row_limiting_clause___0: 'ROWS'
row_limiting_clause___1: 'FETCH' row_limiting_clause___3 row_limiting_clause___4 row_limiting_clause___5
row_limiting_clause___1: 'FETCH' row_limiting_clause___3 row_limiting_clause___4 row_limiting_clause___5 row_limiting_clause___6
row_limiting_clause___1: 'FETCH' row_limiting_clause___3 row_limiting_clause___5
row_limiting_clause___1: 'FETCH' row_limiting_clause___3 row_limiting_clause___5 row_limiting_clause___6
row_limiting_clause___2: 'OFFSET' expr row_limiting_clause___0
row_limiting_clause___3: 'FIRST'
row_limiting_clause___3: 'NEXT'
row_limiting_clause___4: expr
row_limiting_clause___4: expr 'PERCENT'
row_limiting_clause___5: 'ROW'
row_limiting_clause___5: 'ROWS'
row_limiting_clause___6: 'ONLY'
row_limiting_clause___6: 'WITH' 'TIES'
rowid_datatypes: 'ROWID'
rowid_datatypes: 'UROWID'
rowid_datatypes: 'UROWID' rowid_datatypes___0
rowid_datatypes___0: '(' digits ')'
sample_clause: 'SAMPLE' '(' sample_percent ')'
sample_clause: 'SAMPLE' '(' sample_percent ')' sample_clause___0
sample_clause: 'SAMPLE' 'BLOCK' '(' sample_percent ')'
sample_clause: 'SAMPLE' 'BLOCK' '(' sample_percent ')' sample_clause___0
sample_clause___0: 'SEED' '(' seed_value ')'
sample_percent: bind_var
sample_percent: numeric_literal
sample_percent: numeric_literal ',' sample_percent
scalar_subquery_expression: '(' subquery ')'
scale: '-' digits
scale: digits
search_clause: 'SEARCH' search_clause___0 'SET' identifier
search_clause___0: 'BREADTH' 'FIRST' 'BY' ordered_c_alias
search_clause___0: 'BREADTH' 'FIRST' 'BY' ordered_c_alias search_clause___3
search_clause___0: 'BREADTH' 'FIRST' 'BY' ordered_c_alias search_clause___3 search_clause___4
search_clause___0: 'BREADTH' 'FIRST' 'BY' ordered_c_alias search_clause___4
search_clause___0: 'DEPTH' 'FIRST' 'BY' ordered_c_alias
search_clause___0: 'DEPTH' 'FIRST' 'BY' ordered_c_alias search_clause___1
search_clause___0: 'DEPTH' 'FIRST' 'BY' ordered_c_alias search_clause___1 search_clause___2
search_clause___0: 'DEPTH' 'FIRST' 'BY' ordered_c_alias search_clause___2
search_clause___1: ',' ordered_c_alias
search_clause___1: search_clause___1 ',' ordered_c_alias
search_clause___2: 'NULLS' 'FIRST'
search_clause___2: 'NULLS' 'LAST'
search_clause___3: ',' ordered_c_alias
search_clause___3: search_clause___3 ',' ordered_c_alias
search_clause___4: 'NULLS' 'FIRST'
search_clause___4: 'NULLS' 'LAST'
searched_case_expression: searched_case_expression searched_case_expression#
searched_case_expression: searched_case_expression#
searched_case_expression#: 'WHEN' condition 'THEN' expr
seed_value: bind_var
seed_value: numeric_literal
select: subquery
select: subquery select___0
select___0: for_update_clause
select___0: for_update_clause order_by_clause
select_clause: 'SELECT' distinct_unique_all select_list
select_clause: 'SELECT' distinct_unique_all select_list BULK_COLLECT_opt
select_clause: 'SELECT' distinct_unique_all select_list BULK_COLLECT_opt into_list
select_clause: 'SELECT' distinct_unique_all select_list into_list
select_clause: 'SELECT' select_list
select_clause: 'SELECT' select_list BULK_COLLECT_opt
select_clause: 'SELECT' select_list BULK_COLLECT_opt into_list
select_clause: 'SELECT' select_list into_list
select_list: '*'
select_list: select_list ',' '*'
select_list: select_list ',' select_term
select_list: select_term
select_term: "aliased_expr"
select_term: condition
select_term: expr
select_term: identifier '.' table '.' '*'
select_term: table '.' '*'
setOp: "setOp___4" string_literal '=' rhsExpr
setOp: "setOp___4" string_literal '=' rhsExpr "setOp___0"
setOp: "setOp___4" string_literal '=' rhsExpr "setOp___0" "setOp___2"
setOp: "setOp___4" string_literal '=' rhsExpr "setOp___2"
setOp: "setOp___4" string_literal '=' rhsExpr "transform_directive___1"
setOp: "setOp___4" string_literal '=' rhsExpr "transform_directive___1" "setOp___0"
setOp: "setOp___4" string_literal '=' rhsExpr "transform_directive___1" "setOp___0" "setOp___2"
setOp: "setOp___4" string_literal '=' rhsExpr "transform_directive___1" "setOp___2"
share_clause: 'HIERARCHY' hierarchy_ref share_clause___0
share_clause___0: 'LEVEL' identifier
share_clause___0: 'MEMBER' member_expression
share_clause___0: 'PARENT'
share_of_expression: 'SHARE_OF' '(' calc_meas_expression share_clause ')'
sim_expr: arith_expr
sim_expr: multiset_value_expression
sim_expr_DBLDOT__sim_expr: sim_expr '.' '.' sim_expr
simple_case_expression: expr simple_case_expression___0
simple_case_expression___0: simple_case_expression___0 simple_case_expression___0#
simple_case_expression___0: simple_case_expression___0#
simple_case_expression___0#: 'WHEN' expr 'THEN' expr
simple_comparison_condition: '(' "expr_list" ')' cmp_op simple_comparison_condition___0
simple_comparison_condition: '(' "expr_list" ')' simple_comparison_condition___2 '(' "expr_list" ')'
simple_comparison_condition: expr simple_comparison_condition___1 expr
simple_comparison_condition___0: '(' '(' "expr_list" ')' ')'
simple_comparison_condition___0: scalar_subquery_expression
simple_comparison_condition___1: '<'
simple_comparison_condition___1: '<' '='
simple_comparison_condition___1: '>'
simple_comparison_condition___1: '>' '='
simple_comparison_condition___1: cmp_op
simple_comparison_condition___2: '<'
simple_comparison_condition___2: '<' '='
simple_comparison_condition___2: '>'
simple_comparison_condition___2: '>' '='
simple_comparison_condition___2: cmp_op
simple_expression: 'CONNECT_BY_ISCYCLE'
simple_expression: 'CONNECT_BY_ISLEAF'
simple_expression: 'CONNECT_BY_ROOT' expr
simple_expression: 'NULL'
simple_expression: 'ROWID'
simple_expression: 'ROWNUM'
simple_expression: bind_var
simple_expression: col_oj
simple_expression: column
simple_expression: identifier '.' simple_expression___0
simple_expression: literal
simple_expression___0: 'CURRVAL'
simple_expression___0: 'NEXTVAL'
single_column_for_loop: 'FOR' identifier single_column_for_loop___0
single_column_for_loop___0: 'FROM' expr 'TO' expr single_column_for_loop___3 expr
single_column_for_loop___0: 'IN' '(' single_column_for_loop___1 ')'
single_column_for_loop___0: 'LIKE' pattern 'FROM' expr 'TO' expr single_column_for_loop___3 expr
single_column_for_loop___1: expr
single_column_for_loop___1: expr single_column_for_loop___2
single_column_for_loop___1: subquery
single_column_for_loop___2: ',' expr
single_column_for_loop___2: single_column_for_loop___2 ',' expr
single_column_for_loop___3: 'DECREMENT'
single_column_for_loop___3: 'INCREMENT'
single_row_function: JSON_function
single_row_function: NULL_related_function
single_row_function: XML_function
single_row_function: character_function
single_row_function: collect
single_row_function: comparison_function
single_row_function: conversion_function
single_row_function: data_mining_function
single_row_function: encoding_decoding_function
single_row_function: environment_id_function
single_row_function: extract_datetime
single_row_function: hierarchical_function
single_row_function: large_object_function
single_row_function: numeric_function
single_table_insert: insert_into_clause single_table_insert___0
single_table_insert: insert_into_clause single_table_insert___0 error_logging_clause
single_table_insert___0: subquery
single_table_insert___0: values_clause
single_table_insert___0: values_clause returning_clause
sortOp: 'SORT' expr
spatial_types: 'SDO_GEOMETRY'
spatial_types: 'SDO_GEORASTER'
spatial_types: 'SDO_TOPO_GEOMETRY'
sql_query_or_dml_stmt: delete
sql_query_or_dml_stmt: insert
sql_query_or_dml_stmt: merge
sql_query_or_dml_stmt: select
sql_query_or_dml_stmt: update
sql_statement: select '-' '>' '{' javascript '}'
sql_statement: sql_stmt '/'
sql_statements: sql_statement
sql_statements: sql_statements sql_statement
sql_stmt: sql_query_or_dml_stmt
string: id_or_qualid
string: string_literal
stringLiteral: blockString
stringLiteral: quotedString
sub_av_clause: 'USING' identifier
sub_av_clause: 'USING' identifier '.' identifier
sub_av_clause: 'USING' identifier '.' identifier add_meas_clause
sub_av_clause: 'USING' identifier '.' identifier filter_clauses
sub_av_clause: 'USING' identifier '.' identifier filter_clauses add_meas_clause
sub_av_clause: 'USING' identifier '.' identifier hierarchies_clause
sub_av_clause: 'USING' identifier '.' identifier hierarchies_clause add_meas_clause
sub_av_clause: 'USING' identifier '.' identifier hierarchies_clause filter_clauses
sub_av_clause: 'USING' identifier '.' identifier hierarchies_clause filter_clauses add_meas_clause
sub_av_clause: 'USING' identifier add_meas_clause
sub_av_clause: 'USING' identifier filter_clauses
sub_av_clause: 'USING' identifier filter_clauses add_meas_clause
sub_av_clause: 'USING' identifier hierarchies_clause
sub_av_clause: 'USING' identifier hierarchies_clause add_meas_clause
sub_av_clause: 'USING' identifier hierarchies_clause filter_clauses
sub_av_clause: 'USING' identifier hierarchies_clause filter_clauses add_meas_clause
subav_factoring_clause: identifier 'ANALYTIC' 'VIEW' 'AS' '(' sub_av_clause ')'
submultiset_condition: expr 'NOT' 'SUBMULTISET' 'OF' expr
submultiset_condition: expr 'NOT' 'SUBMULTISET' expr
submultiset_condition: expr 'SUBMULTISET' 'OF' expr
submultiset_condition: expr 'SUBMULTISET' expr
subprg_body: ff_w_external ';'
subprg_body: subprg_spec is_or_as 'MLE' 'LANGUAGE' 'JAVASCRIPT' string_literal ';'
subprg_spec: 'FUNCTION' 'IF' 'NOT' 'EXISTS' designator 'RETURN' func_return_prm_spec_unconstrained_type
subprg_spec: 'FUNCTION' 'IF' 'NOT' 'EXISTS' designator fml_part 'RETURN' func_return_prm_spec_unconstrained_type
subprg_spec: 'FUNCTION' designator 'RETURN' func_return_prm_spec_unconstrained_type
subprg_spec: 'FUNCTION' designator fml_part 'RETURN' func_return_prm_spec_unconstrained_type
subprg_spec: 'PROCEDURE' 'IF' 'NOT' 'EXISTS' decl_id
subprg_spec: 'PROCEDURE' 'IF' 'NOT' 'EXISTS' decl_id fml_part
subprg_spec: 'PROCEDURE' 'IF' 'NOT' 'EXISTS' decl_id fml_part proc_interface_opt
subprg_spec: 'PROCEDURE' 'IF' 'NOT' 'EXISTS' decl_id proc_interface_opt
subprg_spec: 'PROCEDURE' decl_id
subprg_spec: 'PROCEDURE' decl_id fml_part
subprg_spec: 'PROCEDURE' decl_id fml_part proc_interface_opt
subprg_spec: 'PROCEDURE' decl_id proc_interface_opt
subquery: '(' subquery ')'
subquery: select_clause
subquery: select_clause from_clause
subquery: subquery "where,gby,hier"
subquery: subquery "where,gby,hier" model_clause
subquery: subquery "where,gby,hier" model_clause order_by_clause
subquery: subquery "where,gby,hier" model_clause order_by_clause row_limiting_clause
subquery: subquery "where,gby,hier" model_clause row_limiting_clause
subquery: subquery "where,gby,hier" model_clause window_clause
subquery: subquery "where,gby,hier" model_clause window_clause order_by_clause
subquery: subquery "where,gby,hier" model_clause window_clause order_by_clause row_limiting_clause
subquery: subquery "where,gby,hier" model_clause window_clause row_limiting_clause
subquery: subquery "where,gby,hier" order_by_clause
subquery: subquery "where,gby,hier" order_by_clause row_limiting_clause
subquery: subquery "where,gby,hier" row_limiting_clause
subquery: subquery "where,gby,hier" window_clause
subquery: subquery "where,gby,hier" window_clause order_by_clause
subquery: subquery "where,gby,hier" window_clause order_by_clause row_limiting_clause
subquery: subquery "where,gby,hier" window_clause row_limiting_clause
subquery: subquery SET_OPER subquery
subquery: subquery model_clause
subquery: subquery model_clause order_by_clause
subquery: subquery model_clause order_by_clause row_limiting_clause
subquery: subquery model_clause row_limiting_clause
subquery: subquery model_clause window_clause
subquery: subquery model_clause window_clause order_by_clause
subquery: subquery model_clause window_clause order_by_clause row_limiting_clause
subquery: subquery model_clause window_clause row_limiting_clause
subquery: subquery order_by_clause
subquery: subquery order_by_clause row_limiting_clause
subquery: subquery row_limiting_clause
subquery: subquery window_clause
subquery: subquery window_clause order_by_clause
subquery: subquery window_clause order_by_clause row_limiting_clause
subquery: subquery window_clause row_limiting_clause
subquery: with_clause subquery
subquery: with_clause subquery "where,gby,hier"
subquery: with_clause subquery "where,gby,hier" model_clause
subquery: with_clause subquery "where,gby,hier" model_clause order_by_clause
subquery: with_clause subquery "where,gby,hier" model_clause order_by_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" model_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" model_clause window_clause
subquery: with_clause subquery "where,gby,hier" model_clause window_clause order_by_clause
subquery: with_clause subquery "where,gby,hier" model_clause window_clause order_by_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" model_clause window_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" order_by_clause
subquery: with_clause subquery "where,gby,hier" order_by_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" row_limiting_clause
subquery: with_clause subquery "where,gby,hier" window_clause
subquery: with_clause subquery "where,gby,hier" window_clause order_by_clause
subquery: with_clause subquery "where,gby,hier" window_clause order_by_clause row_limiting_clause
subquery: with_clause subquery "where,gby,hier" window_clause row_limiting_clause
subquery: with_clause subquery model_clause
subquery: with_clause subquery model_clause order_by_clause
subquery: with_clause subquery model_clause order_by_clause row_limiting_clause
subquery: with_clause subquery model_clause row_limiting_clause
subquery: with_clause subquery model_clause window_clause
subquery: with_clause subquery model_clause window_clause order_by_clause
subquery: with_clause subquery model_clause window_clause order_by_clause row_limiting_clause
subquery: with_clause subquery model_clause window_clause row_limiting_clause
subquery: with_clause subquery order_by_clause
subquery: with_clause subquery order_by_clause row_limiting_clause
subquery: with_clause subquery row_limiting_clause
subquery: with_clause subquery window_clause
subquery: with_clause subquery window_clause order_by_clause
subquery: with_clause subquery window_clause order_by_clause row_limiting_clause
subquery: with_clause subquery window_clause row_limiting_clause
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery cycle_clause
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery cycle_clause subquery_factoring_clause___0
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery search_clause
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery search_clause cycle_clause
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery search_clause cycle_clause subquery_factoring_clause___0
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery search_clause subquery_factoring_clause___0
subquery_factoring_clause: colmapped_query_name 'AS' par_subquery subquery_factoring_clause___0
subquery_factoring_clause: subav_factoring_clause
subquery_factoring_clause: subav_factoring_clause ',' subav_factoring_clause
subquery_factoring_clause___0: subquery_factoring_clause___0 subquery_factoring_clause___0#
subquery_factoring_clause___0: subquery_factoring_clause___0#
subquery_factoring_clause___0#: ',' colmapped_query_name 'AS' par_subquery
subquery_factoring_clause___0#: ',' colmapped_query_name 'AS' par_subquery cycle_clause
subquery_factoring_clause___0#: ',' colmapped_query_name 'AS' par_subquery search_clause
subquery_factoring_clause___0#: ',' colmapped_query_name 'AS' par_subquery search_clause cycle_clause
subquery_restriction_clause: 'WITH' subquery_restriction_clause___0
subquery_restriction_clause: 'WITH' subquery_restriction_clause___0 'CONSTRAINT' constraint
subquery_restriction_clause___0: 'CHECK' 'OPTION'
subquery_restriction_clause___0: 'READ' 'ONLY'
sum: 'SUM' '(' expr ')'
sum: 'SUM' '(' expr ')' sum___1
sum: 'SUM' '(' sum___0 expr ')'
sum: 'SUM' '(' sum___0 expr ')' sum___1
sum___0: 'ALL'
sum___0: 'DISTINCT'
sum___1: 'OVER'
sum___1: 'OVER' '(' ')'
sum___1: 'OVER' '(' analytic_clause ')'
sum___1: 'OVER' analytic_clause
table: identifier
table: identifier '@' dblink
table: identifier partition_extension_clause
table_collection_expression: table_collection_expression___1 '(' collection_expression ')'
table_collection_expression: table_collection_expression___1 '(' collection_expression ')' table_collection_expression___0
table_collection_expression___0: '(' '+' ')'
table_collection_expression___1: 'TABLE'
table_collection_expression___1: identifier
table_collection_expression___1: identifier '.' identifier
table_hier_cons: table_name_id
table_hier_cons: table_name_id json_object
table_hier_cons: table_name_id table_tags_clause json_object
table_identifier: identifier
table_identifier: identifier '.' identifier
table_name_id: table_identifier
table_name_id: table_identifier key_col_clause
table_name_id: table_identifier key_col_clause where_clause
table_name_id: table_identifier table_tags_clause
table_name_id: table_identifier table_tags_clause key_col_clause
table_name_id: table_identifier table_tags_clause key_col_clause where_clause
table_name_id: table_identifier table_tags_clause where_clause
table_name_id: table_identifier where_clause
table_reference: table_reference___3
table_reference: table_reference___3 identifier
table_reference: values_clause
table_reference___0: identifier table_reference___1
table_reference___0: table_reference___1
table_reference___1: nested_clause
table_reference___1: row_pattern_clause
table_reference___1: table_reference___1 table_reference___0#
table_reference___1: table_reference___0#
table_reference___0#: pivot_clause
table_reference___0#: unpivot_clause
table_reference___3: containers_clause
table_reference___3: json_table
table_reference___3: json_table 'AS'
table_reference___3: table_reference___4
table_reference___3: table_reference___4 flashback_query_clause
table_reference___3: table_reference___4 flashback_query_clause table_reference___0
table_reference___3: table_reference___4 table_reference___0
table_reference___4: 'ONLY' '(' query_table_expression ')'
table_reference___4: query_table_expression
table_reference_or_join_clause: '(' table_reference_or_join_clause ')'
table_reference_or_join_clause: '(' table_reference_or_join_clause ')' identifier
table_reference_or_join_clause: '(' table_reference_or_join_clause ')' identifier
table_reference_or_join_clause: join_clause
table_reference_or_join_clause: join_clause row_pattern_clause
table_reference_or_join_clause: join_clause row_pattern_clause identifier
table_reference_or_join_clause: table_reference
table_reference_or_join_clause: external_table
external_table_location: 'LOCATION' '(' filename ')'
external_table_column_description: identifier type '(' digits ')'
external_table_column_description: identifier type
external_table_column_description: external_table_column_description ',' external_table_column_description
external_table_access_parameters: 'RECORDS' 'DELIMITED' 'BY' external_table_delimiter
external_table_access_parameters: 'FIELDS' 'DELIMITED' 'BY' external_table_delimiter
external_table_access_parameters: 'FIELDS' 'TERMINATED' 'BY' external_table_delimiter
external_table_access_parameters: 'OPTIONALLY' 'ENCLOSED' 'BY' string_literal
external_table_access_parameters: '(' external_table_column_description ')'
external_table_access_parameters: 'MISSING' FIELD' 'VALUES' 'ARE' 'NULL'
external_table_access_parameters: 'BADFILE' filename
external_table_access_parameters: 'LOGFILE' filename
external_table_access_parameters: 'DISCARDFILE' filename
external_table_access_parameters: external_table_access_parameters external_table_access_parameters
external_table_delimiter: string_literal
external_table_delimiter: 'NEWLINE'
external_table: 'EXTERNAL' '(' 'TYPE' identifier external_table_location ')'
external_table: 'EXTERNAL' '(' 'TYPE' identifier 'ACCESS' 'PARAMETERS' '(' external_table_access_parameters ')' external_table_location ')'
external_table: 'EXTERNAL' '(' 'TYPE' identifier 'DEFAULT' 'DIRECTORY' identifier 'ACCESS' 'PARAMETERS' '(' identifier ')' external_table_location ')'
table_tags_clause: 'WITH' '(' json_obj_tags "table_tags_clause___0" ')'
table_tags_clause: 'WITH' '(' json_obj_tags "table_tags_clause___2" ')'
table_tags_clause: 'WITH' '(' json_obj_tags ')'
table_tags_clause: 'WITH' json_obj_tags
table_tags_clause: 'WITH' json_obj_tags "table_tags_clause___1"
table_tags_clause: 'WITH' json_obj_tags "table_tags_clause___3"
templatePlaceholder: '$' '{' "templatePlaceholder___0" '}'
templatePlaceholder: '$' '{' '}'
term: factor
term: term mult_op factor
time_zone_specifier: 'LOCAL'
time_zone_specifier: 'TIME' 'ZONE' pri
to_binary_double: 'TO_BINARY_DOUBLE' '(' expr ')'
to_binary_double: 'TO_BINARY_DOUBLE' '(' expr to_binary_double___0 ')'
to_binary_double: 'TO_BINARY_DOUBLE' '(' expr to_binary_double___0 to_binary_double___2 ')'
to_binary_double: 'TO_BINARY_DOUBLE' '(' expr to_binary_double___2 ')'
to_binary_double___0: 'DEFAULT' expr to_binary_double___1
to_binary_double___1: 'ON' 'CONVERSION' 'ERROR'
to_binary_double___2: ',' string_literal
to_binary_double___2: ',' string_literal ',' string_literal
to_binary_float: 'TO_BINARY_FLOAT' '(' expr ')'
to_binary_float: 'TO_BINARY_FLOAT' '(' expr to_binary_float___0 ')'
to_binary_float: 'TO_BINARY_FLOAT' '(' expr to_binary_float___0 to_binary_float___2 ')'
to_binary_float: 'TO_BINARY_FLOAT' '(' expr to_binary_float___2 ')'
to_binary_float___0: 'DEFAULT' expr to_binary_float___1
to_binary_float___1: 'ON' 'CONVERSION' 'ERROR'
to_binary_float___2: ',' string_literal
to_binary_float___2: ',' string_literal ',' string_literal
to_date: 'TO_DATE' '(' expr ')'
to_date: 'TO_DATE' '(' expr to_date___0 ')'
to_date: 'TO_DATE' '(' expr to_date___0 to_date___2 ')'
to_date: 'TO_DATE' '(' expr to_date___2 ')'
to_date___0: 'DEFAULT' expr to_date___1
to_date___1: 'ON' 'CONVERSION' 'ERROR'
to_date___2: ',' string_literal
to_date___2: ',' string_literal ',' string_literal
to_dsinterval: 'TO_DSINTERVAL' '(' expr ')'
to_dsinterval: 'TO_DSINTERVAL' '(' expr to_dsinterval___0 ')'
to_dsinterval___0: 'DEFAULT' expr to_dsinterval___1
to_dsinterval___1: 'ON' 'CONVERSION' 'ERROR'
to_number: 'TO_NUMBER' '(' expr ')'
to_number: 'TO_NUMBER' '(' expr to_number___0 ')'
to_number: 'TO_NUMBER' '(' expr to_number___0 to_number___2 ')'
to_number: 'TO_NUMBER' '(' expr to_number___2 ')'
to_number___0: 'DEFAULT' expr to_number___1
to_number___1: 'ON' 'CONVERSION' 'ERROR'
to_number___2: ',' string_literal
to_number___2: ',' string_literal ',' string_literal
to_timestamp: 'TO_TIMESTAMP' '(' expr ')'
to_timestamp: 'TO_TIMESTAMP' '(' expr to_timestamp___0 ')'
to_timestamp: 'TO_TIMESTAMP' '(' expr to_timestamp___0 to_timestamp___2 ')'
to_timestamp: 'TO_TIMESTAMP' '(' expr to_timestamp___2 ')'
to_timestamp___0: 'DEFAULT' expr to_timestamp___1
to_timestamp___1: 'ON' 'CONVERSION' 'ERROR'
to_timestamp___2: ',' string_literal
to_timestamp___2: ',' string_literal ',' string_literal
to_timestamp_tz: 'TO_TIMESTAMP_TZ' '(' expr ')'
to_timestamp_tz: 'TO_TIMESTAMP_TZ' '(' expr to_timestamp_tz___0 ')'
to_timestamp_tz: 'TO_TIMESTAMP_TZ' '(' expr to_timestamp_tz___0 to_timestamp_tz___2 ')'
to_timestamp_tz: 'TO_TIMESTAMP_TZ' '(' expr to_timestamp_tz___2 ')'
to_timestamp_tz___0: 'DEFAULT' expr to_timestamp_tz___1
to_timestamp_tz___1: 'ON' 'CONVERSION' 'ERROR'
to_timestamp_tz___2: ',' string_literal
to_timestamp_tz___2: ',' string_literal ',' string_literal
to_yminterval: 'TO_YMINTERVAL' '(' expr ')'
to_yminterval: 'TO_YMINTERVAL' '(' expr to_yminterval___0 ')'
to_yminterval___0: 'DEFAULT' expr to_yminterval___1
to_yminterval___1: 'ON' 'CONVERSION' 'ERROR'
translate_using: 'TRANSLATE' '(' expr 'USING' translate_using___0 ')'
translate_using___0: 'CHAR_CS'
translate_using___0: 'NCHAR_CS'
treat: 'TREAT' '(' expr 'AS' treat___0 ')'
treat___0: 'JSON'
treat___0: 'REF' identifier '.' type
treat___0: 'REF' type
treat___0: identifier '.' type
treat___0: type
trim: 'TRIM' '(' expr ')'
trim: 'TRIM' '(' trim___0 'FROM' expr ')'
trim___0: expr#
trim___0: trim___1
trim___0: trim___1 expr#
trim___1: 'BOTH'
trim___1: 'LEADING'
trim___1: 'TRAILING'
trim_options: 'BOTH'
trim_options: 'LEADING'
trim_options: 'TRAILING'
trim_options_sim_expr: sim_expr
trim_options_sim_expr: trim_options
trim_options_sim_expr: trim_options sim_expr
type: 'INTEGER'
type: 'VARCHAR2'
type: identifier
type: listType
type: nonNullType
type_constructor_expression: 'NEW' identifier '.' type_name paren_expr_list
type_constructor_expression: 'NEW' type_name paren_expr_list
type_constructor_expression: identifier '.' type_name paren_expr_list
type_constructor_expression: type_name paren_expr_list
type_name: 'BINARY_DOUBLE'
type_name: 'BINARY_FLOAT'
type_name: 'DATE'
type_name: 'INTEGER'
type_name: 'INTERVAL' 'DAY' 'TO' 'SECOND'
type_name: 'INTERVAL' 'YEAR' 'TO' 'MONTH'
type_name: 'NUMBER'
type_name: 'TIMESTAMP'
type_name: 'TIMESTAMP' '(' digits ')'
type_name: 'TIMESTAMP' 'WITH' 'LOCAL' 'TIME' 'ZONE'
type_name: 'TIMESTAMP' 'WITH' 'TIME' 'ZONE'
type_name: 'VARCHAR2'
type_name: identifier
unary_add_op: '+'
unary_add_op: '-'
unary_add_op: 'PRIOR'
unconstrained_type: 'NATIONAL'
unconstrained_type: unconstrained_type_wo_national
unconstrained_type_wo_datetime_wo_national: 'BFILE'
unconstrained_type_wo_datetime_wo_national: 'BINARY'
unconstrained_type_wo_datetime_wo_national: 'BINARY' 'LARGE' 'OBJECT'
unconstrained_type_wo_datetime_wo_national: 'BLOB'
unconstrained_type_wo_datetime_wo_national: 'CHAR'
unconstrained_type_wo_datetime_wo_national: 'CHAR' 'LARGE' 'OBJECT'
unconstrained_type_wo_datetime_wo_national: 'CHAR' 'VARYING'
unconstrained_type_wo_datetime_wo_national: 'CHARACTER'
unconstrained_type_wo_datetime_wo_national: 'CHARACTER' 'LARGE' 'OBJECT'
unconstrained_type_wo_datetime_wo_national: 'CHARACTER' 'VARYING'
unconstrained_type_wo_datetime_wo_national: 'CLOB'
unconstrained_type_wo_datetime_wo_national: 'COLUMNS'
unconstrained_type_wo_datetime_wo_national: 'COLUMNS' 'WITH' 'TYPE'
unconstrained_type_wo_datetime_wo_national: 'DOUBLE' 'PRECISION'
unconstrained_type_wo_datetime_wo_national: 'LONG'
unconstrained_type_wo_datetime_wo_national: 'LONG' 'RAW'
unconstrained_type_wo_datetime_wo_national: 'MLSLABEL'
unconstrained_type_wo_datetime_wo_national: 'NATIONAL' 'CHAR'
unconstrained_type_wo_datetime_wo_national: 'NATIONAL' 'CHAR' 'VARYING'
unconstrained_type_wo_datetime_wo_national: 'NATIONAL' 'CHARACTER'
unconstrained_type_wo_datetime_wo_national: 'NATIONAL' 'CHARACTER' 'LARGE' 'OBJECT'
unconstrained_type_wo_datetime_wo_national: 'NATIONAL' 'CHARACTER' 'VARYING'
unconstrained_type_wo_datetime_wo_national: 'NCHAR'
unconstrained_type_wo_datetime_wo_national: 'NCHAR' 'LARGE' 'OBJECT'
unconstrained_type_wo_datetime_wo_national: 'NCHAR' 'VARYING'
unconstrained_type_wo_datetime_wo_national: 'NCLOB'
unconstrained_type_wo_datetime_wo_national: 'NVARCHAR2'
unconstrained_type_wo_datetime_wo_national: 'RAW'
unconstrained_type_wo_datetime_wo_national: 'REF' 'XMLTYPE'
unconstrained_type_wo_datetime_wo_national: 'REF' link_expanded_n
unconstrained_type_wo_datetime_wo_national: 'ROWID'
unconstrained_type_wo_datetime_wo_national: 'STRING'
unconstrained_type_wo_datetime_wo_national: 'SYS_REFCURSOR'
unconstrained_type_wo_datetime_wo_national: 'TABLE'
unconstrained_type_wo_datetime_wo_national: 'UROWID'
unconstrained_type_wo_datetime_wo_national: 'VARCHAR'
unconstrained_type_wo_datetime_wo_national: 'VARCHAR2'
unconstrained_type_wo_datetime_wo_national: 'XMLTYPE'
unconstrained_type_wo_datetime_wo_national: datetime_link_expanded_n '%' attribute_designator
unconstrained_type_wo_datetime_wo_national: link_expanded_n
unconstrained_type_wo_datetime_wo_national: link_expanded_n '%' attribute_designator
unconstrained_type_wo_datetime_wo_national: pls_number_datatypes
unconstrained_type_wo_national: alldatetime_d
unconstrained_type_wo_national: unconstrained_type_wo_datetime_wo_national
unionOp: 'UNION' string_literal '=' rhsExpr
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___0"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___0" "unionOp___2"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___2"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___4"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___4" "unionOp___0"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___4" "unionOp___0" "unionOp___2"
unionOp: 'UNION' string_literal '=' rhsExpr "unionOp___4" "unionOp___2"
unique_keys_opt: 'WITH' 'UNIQUE' 'KEYS'
unique_keys_opt: 'WITHOUT' 'UNIQUE' 'KEYS'
unpivot_clause: 'UNPIVOT' '(' unpivot_clause___1 pivot_for_clause unpivot_in_clause ')'
unpivot_clause: 'UNPIVOT' unpivot_clause___0 'NULLS' '(' unpivot_clause___1 pivot_for_clause unpivot_in_clause ')'
unpivot_clause___0: 'EXCLUDE'
unpivot_clause___0: 'INCLUDE'
unpivot_clause___1: '(' column ')'
unpivot_clause___1: '(' column unpivot_clause___2 ')'
unpivot_clause___1: column
unpivot_clause___2: ',' column
unpivot_clause___2: unpivot_clause___2 ',' column
unpivot_in_clause: 'IN' '(' unpivot_in_clause___0 ')'
unpivot_in_clause: 'IN' '(' unpivot_in_clause___0 'AS' unpivot_in_clause___2 ')'
unpivot_in_clause: 'IN' '(' unpivot_in_clause___0 'AS' unpivot_in_clause___2 unpivot_in_clause___4 ')'
unpivot_in_clause: 'IN' '(' unpivot_in_clause___0 unpivot_in_clause___4 ')'
unpivot_in_clause___0: '(' column ')'
unpivot_in_clause___0: '(' column unpivot_in_clause___1 ')'
unpivot_in_clause___0: column
unpivot_in_clause___1: ',' column
unpivot_in_clause___1: unpivot_in_clause___1 ',' column
unpivot_in_clause___2: '(' expr ')'
unpivot_in_clause___2: '(' expr unpivot_in_clause___3 ')'
unpivot_in_clause___2: expr
unpivot_in_clause___3: ',' expr
unpivot_in_clause___3: unpivot_in_clause___3 ',' expr
unpivot_in_clause___4: unpivot_in_clause___4 unpivot_in_clause___0#
unpivot_in_clause___4: unpivot_in_clause___0#
unpivot_in_clause___0#: ',' unpivot_in_clause___6
unpivot_in_clause___0#: ',' unpivot_in_clause___6 'AS' unpivot_in_clause___8
unpivot_in_clause___6: '(' column ')'
unpivot_in_clause___6: '(' column unpivot_in_clause___7 ')'
unpivot_in_clause___6: column
unpivot_in_clause___7: ',' column
unpivot_in_clause___7: unpivot_in_clause___7 ',' column
unpivot_in_clause___8: '(' expr ')'
unpivot_in_clause___8: '(' expr unpivot_in_clause___9 ')'
unpivot_in_clause___8: expr
unpivot_in_clause___9: ',' expr
unpivot_in_clause___9: unpivot_in_clause___9 ',' expr
update: 'UPDATE' update___0 for_compress update_set_clause
update: 'UPDATE' update___0 for_compress update_set_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause order_by_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause order_by_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause order_by_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause order_by_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause from_clause where_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause order_by_clause
update: 'UPDATE' update___0 for_compress update_set_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause order_by_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause order_by_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause order_by_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause returning_clause
update: 'UPDATE' update___0 for_compress update_set_clause where_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause
update: 'UPDATE' update___0 update_set_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause
update: 'UPDATE' update___0 update_set_clause from_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause order_by_clause
update: 'UPDATE' update___0 update_set_clause from_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause order_by_clause returning_clause
update: 'UPDATE' update___0 update_set_clause from_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause returning_clause
update: 'UPDATE' update___0 update_set_clause from_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause order_by_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause order_by_clause returning_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause returning_clause
update: 'UPDATE' update___0 update_set_clause from_clause where_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause order_by_clause
update: 'UPDATE' update___0 update_set_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause order_by_clause returning_clause
update: 'UPDATE' update___0 update_set_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause returning_clause
update: 'UPDATE' update___0 update_set_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause where_clause
update: 'UPDATE' update___0 update_set_clause where_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause where_clause order_by_clause
update: 'UPDATE' update___0 update_set_clause where_clause order_by_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause where_clause order_by_clause returning_clause
update: 'UPDATE' update___0 update_set_clause where_clause order_by_clause returning_clause error_logging_clause
update: 'UPDATE' update___0 update_set_clause where_clause returning_clause
update: 'UPDATE' update___0 update_set_clause where_clause returning_clause error_logging_clause
update___0: 'ONLY' '(' aliased_dml_table_expression_clause ')'
update___0: aliased_dml_table_expression_clause
update_set_clause: 'SET' update_set_clause___0
update_set_clause___0: 'VALUE' '(' id_or_qualid ')' '=' update_set_clause___2
update_set_clause___0: update_set_clause_expr
update_set_clause___0: update_set_clause_expr update_set_clause___1
update_set_clause___1: ',' update_set_clause_expr
update_set_clause___1: update_set_clause___1 ',' update_set_clause_expr
update_set_clause___2: '(' subquery ')'
update_set_clause___2: expr
update_set_clause_expr: '(' column ')' '=' '(' subquery ')'
update_set_clause_expr: '(' column update_set_clause_expr___2 ')' '=' '(' subquery ')'
update_set_clause_expr: column '=' update_set_clause_expr___0
update_set_clause_expr: identifier '.' column '=' update_set_clause_expr___1
update_set_clause_expr___0: '(' subquery ')'
update_set_clause_expr___0: 'DEFAULT'
update_set_clause_expr___0: expr
update_set_clause_expr___1: '(' subquery ')'
update_set_clause_expr___1: 'DEFAULT'
update_set_clause_expr___1: expr
update_set_clause_expr___2: ',' column
update_set_clause_expr___2: update_set_clause_expr___2 ',' column
update_upsert_all: 'UPDATE'
update_upsert_all: 'UPSERT'
update_upsert_all: 'UPSERT' 'ALL'
user_defined_function: user_defined_function___0
user_defined_function: user_defined_function___0 "(x,y,z)"
user_defined_function: user_defined_function___0 '@' dblink
user_defined_function: user_defined_function___0 '@' dblink "(x,y,z)"
user_defined_function___0: identifier
user_defined_function___0: identifier '.' identifier
user_defined_function___0: identifier '.' identifier '.' identifier
user_defined_types: 'REF' identifier
user_defined_types: identifier
user_defined_types: identifier '.' identifier
using_bind: 'IN' 'OUT' name
using_bind: 'IN' pls_expr
using_bind: 'OUT' name
using_bind: pls_expr
using_bind_list: using_bind
using_bind_list: using_bind_list ',' using_bind
using_charset_csname: 'CASE_INSENSITIVE'
using_charset_csname: 'CASE_SENSITIVE'
using_charset_csname: charset_csname
using_clause_opt: 'USING' using_bind_list
using_index_clause: 'USING' 'INDEX'
using_index_clause: 'USING' 'INDEX' using_index_clause___0
using_index_clause___0: identifier
using_index_clause___0: identifier '.' identifier
validate_conversion: 'VALIDATE_CONVERSION' '(' expr 'AS' type_name ')'
validate_conversion: 'VALIDATE_CONVERSION' '(' expr 'AS' type_name validate_conversion___0 ')'
validate_conversion___0: ',' string_literal
validate_conversion___0: ',' string_literal ',' string_literal
value: 'VALUE' '(' correlation_variable ')'
value: 'null'
value: FLOAT
value: NUMBER
value: VARIABLE_NAME
value: arrayValue
value: booleanValue
value: identifier
value: objectValue
value: stringLiteral
value: templatePlaceholder
values_clause: '(' 'VALUES' '(' expr ')' ')'
values_clause: '(' 'VALUES' '(' expr ')' ')' values_clause___6
values_clause: '(' 'VALUES' '(' expr ')' values_clause___3 ')'
values_clause: '(' 'VALUES' '(' expr ')' values_clause___3 ')' values_clause___6
values_clause: '(' 'VALUES' '(' expr values_clause___2 ')' ')'
values_clause: '(' 'VALUES' '(' expr values_clause___2 ')' ')' values_clause___6
values_clause: '(' 'VALUES' '(' expr values_clause___2 ')' values_clause___3 ')'
values_clause: '(' 'VALUES' '(' expr values_clause___2 ')' values_clause___3 ')' values_clause___6
values_clause: 'VALUES' values_clause___0
values_clause___0: expr
values_clause___0: expr par_expr_list
values_clause___0: par_expr_list
values_clause___0: par_expr_list values_clause___1
values_clause___1: ',' par_expr_list
values_clause___1: values_clause___1 ',' par_expr_list
values_clause___2: ',' expr
values_clause___2: values_clause___2 ',' expr
values_clause___3: values_clause___3 values_clause___0#
values_clause___3: values_clause___0#
values_clause___0#: ',' '(' expr ')'
values_clause___0#: ',' '(' expr values_clause___5 ')'
values_clause___5: ',' expr
values_clause___5: values_clause___5 ',' expr
values_clause___6: 'AS' identifier '(' c_alias ')'
values_clause___6: 'AS' identifier '(' c_alias values_clause___7 ')'
values_clause___6: identifier '(' c_alias ')'
values_clause___6: identifier '(' c_alias values_clause___7 ')'
values_clause___7: ',' c_alias
values_clause___7: values_clause___7 ',' c_alias
vertex_pattern: '(' ')'
vertex_pattern: '(' optional_element_pattern_filter ')'
when_clause: 'WHEN' pls_expr
where_clause: 'WHERE' condition
where_clause_w_boolean: 'WHERE' compound_condition_w_boolean
while_clause: 'WHILE' pls_expr
wildcard: "wildcard___0" identifier '.' '*'
wildcard: identifier '.' '*'
window_clause: 'WINDOW' identifier 'AS'
window_clause: 'WINDOW' identifier 'AS' window_clause___0
window_clause: 'WINDOW' identifier 'AS' window_specification
window_clause: 'WINDOW' identifier 'AS' window_specification window_clause___0
window_clause___0: window_clause___0 window_clause___0#
window_clause___0: window_clause___0#
window_clause___0#: ',' identifier 'AS'
window_clause___0#: ',' identifier 'AS' window_specification
window_specification: '(' ')'
window_specification: '(' window_specification1 ')'
window_specification: window_specification1
window_specification1: identifier
window_specification1: identifier order_by_clause
window_specification1: identifier order_by_clause windowing_clause
window_specification1: identifier query_partition_clause
window_specification1: identifier query_partition_clause order_by_clause
window_specification1: identifier query_partition_clause order_by_clause windowing_clause
window_specification1: identifier query_partition_clause windowing_clause
window_specification1: identifier windowing_clause
window_specification1: order_by_clause
window_specification1: order_by_clause windowing_clause
window_specification1: query_partition_clause
window_specification1: query_partition_clause order_by_clause
window_specification1: query_partition_clause order_by_clause windowing_clause
window_specification1: query_partition_clause windowing_clause
window_specification1: windowing_clause
windowing_clause: windowing_clause___2 windowing_clause___3
windowing_clause: windowing_clause___2 windowing_clause___3 windowing_clause___1
windowing_clause___0: 'FOLLOWING'
windowing_clause___0: 'PRECEDING'
windowing_clause___1: 'EXCLUDE' 'CURRENT' 'ROW'
windowing_clause___1: 'EXCLUDE' 'GROUP'
windowing_clause___1: 'EXCLUDE' 'NO' 'OTHERS'
windowing_clause___1: 'EXCLUDE' 'TIES'
windowing_clause___2: 'GROUPS'
windowing_clause___2: 'RANGE'
windowing_clause___2: 'ROWS'
windowing_clause___3: 'BETWEEN' windowing_clause___4 'AND' windowing_clause___6
windowing_clause___3: 'CURRENT' 'ROW'
windowing_clause___3: 'UNBOUNDED' 'PRECEDING'
windowing_clause___3: expr 'PRECEDING'
windowing_clause___4: 'CURRENT' 'ROW'
windowing_clause___4: 'UNBOUNDED' 'PRECEDING'
windowing_clause___4: expr windowing_clause___5
windowing_clause___5: 'FOLLOWING'
windowing_clause___5: 'PRECEDING'
windowing_clause___6: 'CURRENT' 'ROW'
windowing_clause___6: 'UNBOUNDED' 'FOLLOWING'
windowing_clause___6: expr windowing_clause___0
with_clause: 'WITH' with_clause___0
with_clause___0: plsql_declarations
with_clause___0: plsql_declarations subquery_factoring_clause
with_clause___0: subquery_factoring_clause
xmlagg: 'XMLAGG' '(' expr ')'
xmlagg: 'XMLAGG' '(' expr order_by_clause ')'
xmlcast: 'XMLCAST' '(' expr 'AS' datatype ')'
xmlcolattval: 'XMLCOLATTVAL' '(' expr ')'
xmlcolattval: 'XMLCOLATTVAL' '(' expr 'AS' xmlcolattval___0 ')'
xmlcolattval: 'XMLCOLATTVAL' '(' expr 'AS' xmlcolattval___0 xmlcolattval___1 ')'
xmlcolattval: 'XMLCOLATTVAL' '(' expr xmlcolattval___1 ')'
xmlcolattval___0: 'EVALNAME' expr
xmlcolattval___0: c_alias
xmlcolattval___1: xmlcolattval___1 xmlcolattval___0#
xmlcolattval___1: xmlcolattval___0#
xmlcolattval___0#: ',' expr
xmlcolattval___0#: ',' expr 'AS' xmlcolattval___3
xmlcolattval___3: 'EVALNAME' expr
xmlcolattval___3: c_alias
xmlelement: 'XMLELEMENT' '(' 'NAME' xmlelement___1 ')'
xmlelement: 'XMLELEMENT' '(' 'NAME' xmlelement___1 ',' XML_attributes_clause ')'
xmlelement: 'XMLELEMENT' '(' 'NAME' xmlelement___1 ',' XML_attributes_clause xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' 'NAME' xmlelement___1 xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 'NAME' xmlelement___1 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 'NAME' xmlelement___1 ',' XML_attributes_clause ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 'NAME' xmlelement___1 ',' XML_attributes_clause xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 'NAME' xmlelement___1 xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 xmlelement___1 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 xmlelement___1 ',' XML_attributes_clause ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 xmlelement___1 ',' XML_attributes_clause xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___0 xmlelement___1 xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___1 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___1 ',' XML_attributes_clause ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___1 ',' XML_attributes_clause xmlelement___2 ')'
xmlelement: 'XMLELEMENT' '(' xmlelement___1 xmlelement___2 ')'
xmlelement___0: 'ENTITYESCAPING'
xmlelement___0: 'NOENTITYESCAPING'
xmlelement___1: 'EVALNAME' expr
xmlelement___1: identifier
xmlelement___2: xmlelement___2 xmlelement___0#
xmlelement___2: xmlelement___0#
xmlelement___0#: ',' expr
xmlelement___0#: ',' expr "as_alias"
xmlexists: 'XMLEXISTS' '(' expr ')'
xmlexists: 'XMLEXISTS' '(' expr XML_passing_clause ')'
xmlforest: 'XMLFOREST' '(' aux_xml_value_expr ')'
xmlforest: 'XMLFOREST' '(' aux_xml_value_expr xmlforest___0 ')'
xmlforest___0: ',' aux_xml_value_expr
xmlforest___0: xmlforest___0 ',' aux_xml_value_expr
xmlparse: 'XMLPARSE' '(' xmlparse___0 expr ')'
xmlparse: 'XMLPARSE' '(' xmlparse___0 expr 'WELLFORMED' ')'
xmlparse___0: 'CONTENT'
xmlparse___0: 'DOCUMENT'
xmlpi: 'XMLPI' '(' xmlpi___0 ')'
xmlpi: 'XMLPI' '(' xmlpi___0 ',' expr ')'
xmlpi___0: 'EVALNAME' expr
xmlpi___0: 'NAME' identifier
xmlpi___0: identifier
xmlquery: 'XMLQUERY' '(' expr XML_passing_clause xmlquery___0 ')'
xmlquery: 'XMLQUERY' '(' expr XML_passing_clause xmlquery___2 ')'
xmlquery: 'XMLQUERY' '(' expr xmlquery___0 ')'
xmlquery: 'XMLQUERY' '(' expr xmlquery___2 ')'
xmlquery___0: 'RETURNING' 'CONTENT'
xmlquery___0: 'RETURNING' 'CONTENT' xmlquery___1
xmlquery___1: 'NULL' 'ON' 'EMPTY'
xmlquery___2: 'RETURNING' 'SEQUENCE'
xmlquery___2: 'RETURNING' 'SEQUENCE' 'BY' 'REFERENCE'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal 'VERSION' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'ENCODING' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'VERSION' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'VERSION' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'VERSION' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype 'VERSION' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'AS' datatype xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal 'VERSION' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal 'VERSION' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'ENCODING' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'VERSION' string_literal ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'VERSION' string_literal xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'VERSION' string_literal xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr 'VERSION' string_literal xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr xmlserialize___0 'DEFAULTS' ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr xmlserialize___2 ')'
xmlserialize: 'XMLSERIALIZE' '(' xmlserialize___1 expr xmlserialize___2 xmlserialize___0 'DEFAULTS' ')'
xmlserialize___0: 'HIDE'
xmlserialize___0: 'SHOW'
xmlserialize___1: 'CONTENT'
xmlserialize___1: 'DOCUMENT'
xmlserialize___2: 'INDENT'
xmlserialize___2: 'INDENT' xmlserialize___3
xmlserialize___2: 'NO' 'INDENT'
xmlserialize___3: 'SIZE' '=' numeric_literal
xmltable: 'XMLTABLE' '(' XMLnamespaces_clause ',' expr ')'
xmltable: 'XMLTABLE' '(' XMLnamespaces_clause ',' expr ')' xmltable___0
xmltable: 'XMLTABLE' '(' XMLnamespaces_clause ',' expr XMLTABLE_options ')'
xmltable: 'XMLTABLE' '(' XMLnamespaces_clause ',' expr XMLTABLE_options ')' xmltable___0
xmltable: 'XMLTABLE' '(' expr ')'
xmltable: 'XMLTABLE' '(' expr ')' xmltable___0
xmltable: 'XMLTABLE' '(' expr XMLTABLE_options ')'
xmltable: 'XMLTABLE' '(' expr XMLTABLE_options ')' xmltable___0
xmltable___0: '(' '+' ')'
xmltable___0: 'AS' identifier
xmltable___0: 'AS' identifier xmltable___1
xmltable___1: '(' "expression_list" ')'
