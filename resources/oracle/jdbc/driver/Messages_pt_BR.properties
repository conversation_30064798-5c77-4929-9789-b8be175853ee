#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controla os recursos de diagnosticabilidade dos drivers Oracle JDBC.

DiagnosabilityMBeanConstructor()=O construtor de zero argumentos do MBean de Diagnosticabilidade do Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Todo c\u00F3digo de log do Oracle JDBC \u00E9 controlado por este atributo booleano. Se for falso, nenhuma mensagem de log ser\u00E1 produzida. Se for verdadeiro, as mensagens de log ser\u00E3o controladas pelos N\u00EDveis e Filtros do java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Os drivers Oracle JDBC n\u00E3o podem ser reiniciados ou interrompidos. Sempre retornam falso.

DiagnosabilityMBeanStatisticsProviderDescription=Os drivers Oracle JDBC n\u00E3o fornecem estat\u00EDsticas atrav\u00E9s do MBean de Diagnosticabilidade.

DiagnosabilityMBeanTraceControllerDescription=Controlador de Rastreamento de Clio.

DiagnosabilityMBeanSuspendDescription=Opera\u00E7\u00E3o de suspens\u00E3o de clio.

DiagnosabilityMBeanResumeDescription=Opera\u00E7\u00E3o de retomada de clio.

DiagnosabilityMBeanTraceDescription=Opera\u00E7\u00E3o de rastreamento de clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Ativa o log de arquivo cont\u00EDnuo seguro. Os filtros ServiceName e UserName ser\u00E3o aplic\u00E1veis, se definidos. Por padr\u00E3o, ele fica desativado.

DiagnosabilityMBeanDisableContinousLoggingDescription=Desativa o log de arquivo cont\u00EDnuo seguro. Por padr\u00E3o, ele fica desativado.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Ativa o Diagn\u00F3stico da Primeira Falha. Os filtros ServiceName e UserName ser\u00E3o aplic\u00E1veis se definidos. Por padr\u00E3o, permanece ativado.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Desativa o Diagn\u00F3stico da Primeira Falha. Por padr\u00E3o, permanece ativado.

DiagnosabilityMBeanServiceNameFilterDescription=Se o filtro ServiceName estiver definido, o log na mem\u00F3ria/cont\u00EDnuo s\u00F3 ser\u00E1 ativado nas conex\u00F5es do servi\u00E7o configurado.

DiagnosabilityMBeanUserFilterDescription=Se o filtro de Usu\u00E1rio estiver definido, o log na mem\u00F3ria/cont\u00EDnuo s\u00F3 ser\u00E1 ativado nas conex\u00F5es do Usu\u00E1rio configurado.

ReplayStatisticsMBeanDescription=Exp\u00F5e as estat\u00EDsticas da funcionalidade AC (Continuidade do Aplicativo).

ReplayStatisticsMBeanConstructor=O construtor de zero argumentos do MBean de estat\u00EDsticas de AC do Oracle ODBC

ReplayStatisticsMBeanAllStatisticsDescription=Obt\u00E9m estat\u00EDsticas de AC entre todas as inst\u00E2ncias conhecidas da origem de dados do driver.

ReplayStatisticsMBeanGetDSStatisticsDescription=Obt\u00E9m estat\u00EDsticas de AC de uma inst\u00E2ncia espec\u00EDfica da origem de dados do driver.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controla os recursos de diagnosticabilidade dos drivers Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Todo o c\u00F3digo de registro em log do Oracle JDBC \u00E9 controlado por esse atributo booliano. O padr\u00E3o \u00E9 falso. Se ativado, as mensagens de log ser\u00E3o controladas pelos N\u00EDveis e Filtros java.util.logging. Se desativado, nenhuma mensagem de log ser\u00E1 gerada.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Todas as m\u00E9tricas de eventos capturados pelo driver JDBC s\u00E3o controladas por esse atributo booliano. O padr\u00E3o \u00E9 falso. Se ativado, as m\u00E9tricas de eventos ser\u00E3o capturadas pelo driver at\u00E9 a desativa\u00E7\u00E3o.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Retorna verdadeiro se a grava\u00E7\u00E3o de logs no buffer de rastreamento na mem\u00F3ria estiver ativada. O padr\u00E3o \u00E9 falso.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo prefixo de id de conex\u00E3o fornecido, caso ele tenha sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo prefixo de id de conex\u00E3o fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo nome do tenant fornecido, caso ele tenha sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo nome do tenant fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo nome do logger fornecido, caso ele tenha sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo nome do logger fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o ativar\u00E1 o Diagn\u00F3stico da Primeira Falha se ele tiver sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=O Diagn\u00F3stico da Primeira Falha \u00E9 ativado por padr\u00E3o. Essa opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o permite o registro em log pelo prefixo de id de conex\u00E3o fornecido.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o registro em log pelo prefixo de id de conex\u00E3o fornecido, caso ele tenha sido ativado.

DiagnosticsEnableLoggingByTenantNameOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o registro em log pelo nome do tenant fornecido.

DiagnosticsDisableLoggingByTenantNameOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o registro em log pelo nome do tenant fornecido, caso ele tenha sido ativado.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o registro em log pelo nome do logger fornecido.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o registro em log pelo nome do logger fornecido, caso ele tenha sido ativado.

DiagnosticsEnableLoggingOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o registro em log.

DiagnosticsDisableLoggingOperationDescription=O registro em log \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o registro em log, caso ele tenha sido ativado.

DiagnosticsEnableMetricsOperationDescription=Inicie a coleta de m\u00E9tricas de hor\u00E1rio durante o tempo de conex\u00E3o.

DiagnosticsDisableMetricsOperationDescription=Interrompa a coleta de m\u00E9tricas de hor\u00E1rio durante o tempo de conex\u00E3o.

DiagnosticsShowMetricsOperationDescription=Mostre as m\u00E9tricas de hor\u00E1rio coletadas durante o tempo de conex\u00E3o.

DiagnosticsClearMetricsOperationDescription=Limpe as m\u00E9tricas de hor\u00E1rio coletadas durante o tempo de conex\u00E3o.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico com dados confidenciais pelo prefixo de id de conex\u00E3o fornecido.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico com dados confidenciais pelo prefixo de id de conex\u00E3o fornecido, caso ele tenha sido ativado.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico com dados confidenciais pelo nome do tenant fornecido.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico com dados confidenciais pelo nome do tenant fornecido, caso ele tenha sido ativado.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico com dados confidenciais pelo nome do logger fornecido.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico com dados confidenciais pelo nome do logger fornecido, caso ele tenha sido ativado.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico com dados confidenciais.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=O diagn\u00F3stico com dados confidenciais \u00E9 desativado por padr\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico com dados confidenciais, caso ele tenha sido ativado.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo prefixo do ID de conex\u00E3o fornecido. A string do argumento pode consistir em um nome de n\u00EDvel ou um valor inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo nome do tenant fornecido. A string do argumento pode consistir em um nome de n\u00EDvel ou um valor inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo nome do logger fornecido. A string do argumento pode consistir em um nome de n\u00EDvel ou um valor inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico. A string do argumento pode consistir em um nome de n\u00EDvel ou um valor inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Atualize o tamanho do buffer de Diagn\u00F3stico da Primeira Falha pelo prefixo do id de conex\u00E3o fornecido. O tamanho padr\u00E3o do buffer \u00E9 4.000 registros de log.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Atualize o tamanho do buffer de Diagn\u00F3stico da Primeira Falha pelo nome do tenant fornecido. O tamanho padr\u00E3o do buffer \u00E9 4.000 registros de log.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Atualize o tamanho do buffer de Diagn\u00F3stico da Primeira Falha pelo nome do logger fornecido. O tamanho padr\u00E3o do buffer \u00E9 4.000 registros de log.

DiagnosticsUpdateBufferSizeOperationDescription=Atualize o tamanho do buffer de Diagn\u00F3stico da Primeira Falha. O tamanho padr\u00E3o do buffer \u00E9 4.000 registros de log.

DiagnosticsReadLoggingConfigFileOperationDescription=Reinicie as propriedades de registro em log e reveja a configura\u00E7\u00E3o de registro em log do arquivo fornecido, que deve estar no formato java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Adicione o c\u00F3digo de erro a ser verificado para a pr\u00F3xima ocorr\u00EAncia no formato ORA-XXXXX. O driver JDBC grava o Diagn\u00F3stico da Primeira Falha no handler de log configurado quando ocorre um erro na lista de verifica\u00E7\u00E3o.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Remova o c\u00F3digo de erro fornecido na lista de verifica\u00E7\u00E3o. O c\u00F3digo de erro deve estar no formato ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Mostre os c\u00F3digos de erro que est\u00E3o sendo verificados.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configure a lista de verifica\u00E7\u00E3o com os c\u00F3digos de erro padr\u00E3o.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Fa\u00E7a dump do Diagn\u00F3stico da Primeira Falha no handler de destino.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Fa\u00E7a dump do Diagn\u00F3stico da Primeira Falha quando a exce\u00E7\u00E3o futura contiver uma das palavras-chave fornecidas. Os exemplos de palavras-chave s\u00E3o redefinir, viola\u00E7\u00E3o.

DiagnosticsShowExceptionKeywords=As palavras-chave de exce\u00E7\u00E3o adicionadas anteriormente.

DiagnosticsShowRecentOperations=As opera\u00E7\u00F5es mais recentes executadas pelo usu\u00E1rio neste MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Remova as palavras-chave adicionadas anteriormente.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Por padr\u00E3o, os logs s\u00E3o gravados no handler de logs. Essa opera\u00E7\u00E3o ativa ou desativa a grava\u00E7\u00E3o de logs no Diagn\u00F3stico da Primeira Falha.

DiagnosticsConnectionIdPrefixParameterDescription=O Prefixo do ID da Conex\u00E3o.

DiagnosticsTenantNameParameterDescription=O Nome do Tenant.

DiagnosticsLoggerNameParameterDescription=O Nome do Logger.

DiagnosticsLevelParameterDescription=O N\u00EDvel do Logger.

DiagnosticsBufferSizeParameterDescription=O Tamanho do Buffer de Diagn\u00F3stico da Primeira Falha.

DiagnosticsConfigFileParameterDescription=O Arquivo de Configura\u00E7\u00E3o de Registro em Log.

DiagnosticsErrorCodesParameterDescription=Um c\u00F3digo de erro no formato ORA-XXXXX.

DiagnosticsEnabledParameterDescription=O valor verdadeiro ou falso.

DiagnosticsCommaSeparatedKeywordsParameterDescription=As palavras-chave como valores separados por v\u00EDrgulas.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






