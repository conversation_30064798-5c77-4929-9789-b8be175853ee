#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Hallitsee Oracle JDBC -ohjaimien vianm\u00E4\u00E4ritysominaisuuksia.

DiagnosabilityMBeanConstructor()=zero arg -muodostin Oracle JDBC -vianm\u00E4\u00E4rityksen MBean-kohteelle

DiagnosabilityMBeanLoggingEnabledDescription=T\u00E4m\u00E4 totuusarvom\u00E4\u00E4rite ohjaa kaikkea Oracle JDBC -lokiinkirjauskoodia. Jos sen arvo on ep\u00E4tosi, lokisanomia ei tuoteta. <PERSON><PERSON> arvo on tosi, lokisanomat tuotetaan kohteen java.util.logging tasojen ja suodattimien mukaisesti.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC -ohjaimia ei voi k\u00E4ynnist\u00E4\u00E4 ja pys\u00E4ytt\u00E4\u00E4. Palauttaa aina arvon ep\u00E4tosi.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC -ohjaimet eiv\u00E4t anna tilastotietoja viranm\u00E4\u00E4rityksen MBean-kohteen kautta.

DiagnosabilityMBeanTraceControllerDescription=Clio-j\u00E4ljitysohjain.

DiagnosabilityMBeanSuspendDescription=Clio-pys\u00E4ytystoiminto.

DiagnosabilityMBeanResumeDescription=Clio-jatkamistoiminto.

DiagnosabilityMBeanTraceDescription=Clio-j\u00E4ljitystoiminto.

DiagnosabilityMBeanEnableContinousLoggingDescription=Ottaa k\u00E4ytt\u00F6\u00F6n suojatun jatkuvaan lokitiedostoon kirjauksen. ServiceName- ja UserName-suodattimia, k\u00E4ytet\u00E4\u00E4n, jos ne on m\u00E4\u00E4ritetty. Toiminto ei ole oletusarvoisesti k\u00E4yt\u00F6ss\u00E4.

DiagnosabilityMBeanDisableContinousLoggingDescription=Poistaa suojatun jatkuvaan lokitiedostoon kirjauksen k\u00E4yt\u00F6st\u00E4. Se ei ole oletusarvoisesti k\u00E4yt\u00F6ss\u00E4.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Ottaa k\u00E4ytt\u00F6\u00F6n ensimm\u00E4isen virheen m\u00E4\u00E4rityksen. ServiceName- ja UserName-suodattimia k\u00E4ytet\u00E4\u00E4n, jos ne on m\u00E4\u00E4ritetty. Toiminto on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Poistaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4yt\u00F6st\u00E4. Toiminto on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4.

DiagnosabilityMBeanServiceNameFilterDescription=Jos ServiceName-suodatin on m\u00E4\u00E4ritetty, muistiin tallennettava j\u00E4ljitys / jatkuvaan lokitiedostoon kirjoitus on k\u00E4yt\u00F6ss\u00E4 vain m\u00E4\u00E4ritetyn palvelun yhteyksille.

DiagnosabilityMBeanUserFilterDescription=Jos UserName-suodatin on m\u00E4\u00E4ritetty, muistiin tallennettava j\u00E4ljitys / jatkuvaan lokitiedostoon kirjoitus on k\u00E4yt\u00F6ss\u00E4 vain m\u00E4\u00E4ritetyn k\u00E4ytt\u00E4j\u00E4n yhteyksille.

ReplayStatisticsMBeanDescription=N\u00E4ytt\u00E4\u00E4 sovelluksen jatkuvuus (AC) -toiminnon tilastot.

ReplayStatisticsMBeanConstructor=zero arg -muodostin Oracle JDBC AC -tilaston MBeanille

ReplayStatisticsMBeanAllStatisticsDescription=Hakee AC-tilastot kaikista tunnetuista ajurien tietol\u00E4hdeinstansseista.

ReplayStatisticsMBeanGetDSStatisticsDescription=Hakee AC-tilastot tietyst\u00E4 ajurin tietol\u00E4hdeinstanssista.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Hallitsee Oracle JDBC -ohjaimien vianm\u00E4\u00E4ritysominaisuuksia.

DiagnosticsMBeanLoggingEnabledAttributeDescription=T\u00E4m\u00E4 totuusarvom\u00E4\u00E4rite ohjaa kaikkea Oracle JDBC -lokiinkirjauskoodia. Oletusasetus on ep\u00E4tosi. Jos se otetaan k\u00E4ytt\u00F6\u00F6n, lokisanomat tuotetaan kohteen java.util.logging tasojen ja suodattimien mukaisesti. Jos se poistetaan k\u00E4yt\u00F6st\u00E4, lokisanomia ei tuoteta.

DiagnosticsMBeanMetricsEnabledAttributeDescription=T\u00E4m\u00E4 totuusarvom\u00E4\u00E4rite ohjaa kaikkia JDBC-ajurin ker\u00E4\u00E4mien tapahtumien mittareita. Oletusarvo on ep\u00E4tosi. Jos t\u00E4m\u00E4 on k\u00E4yt\u00F6ss\u00E4, ajuri ker\u00E4\u00E4 tapahtumien mittarit, kunnes toiminto poistetaan k\u00E4yt\u00F6st\u00E4.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Palauttaa arvon tosi, jos lokien kirjoittaminen muistinsis\u00E4iseen j\u00E4ljityksen puskurimuistiin on k\u00E4yt\u00F6ss\u00E4. Oletusarvo on ep\u00E4tosi.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto ottaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4ytt\u00F6\u00F6n m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan, jos se on aiemmin poistettu k\u00E4yt\u00F6st\u00E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto poistaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4yt\u00F6st\u00E4 m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto ottaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4ytt\u00F6\u00F6n m\u00E4\u00E4ritetyn asiakkaan nimen mukaan, jos se on aiemmin poistettu k\u00E4yt\u00F6st\u00E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto poistaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4yt\u00F6st\u00E4 m\u00E4\u00E4ritetyn asiakkaan nimen mukaan.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto ottaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4ytt\u00F6\u00F6n m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan, jos se on aiemmin poistettu k\u00E4yt\u00F6st\u00E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto poistaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4yt\u00F6st\u00E4 m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto ottaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4ytt\u00F6\u00F6n, jos se on aiemmin poistettu k\u00E4yt\u00F6st\u00E4.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritys on oletusarvoisesti k\u00E4yt\u00F6ss\u00E4. Toiminto poistaa ensimm\u00E4isen virheen m\u00E4\u00E4rityksen k\u00E4yt\u00F6st\u00E4.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Lokiinkirjaus on oletusarvoisesti poissa k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n lokiinkirjauksen m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 lokiinkirjauksen m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n lokiinkirjauksen m\u00E4\u00E4ritetyn asiakkaan nimen mukaan.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 lokiinkirjauksen m\u00E4\u00E4ritetyn asiakkaan nimen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n lokiinkirjauksen m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 lokiinkirjauksen m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableLoggingOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa lokiinkirjauksen k\u00E4ytt\u00F6\u00F6n.

DiagnosticsDisableLoggingOperationDescription=Lokiinkirjaus on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa lokiinkirjauksen k\u00E4yt\u00F6st\u00E4, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableMetricsOperationDescription=Aloita ajoituksen mittareiden ker\u00E4ys yhteyden aikana.

DiagnosticsDisableMetricsOperationDescription=Lopeta ajoituksen mittareiden ker\u00E4ys yhteyden aikana.

DiagnosticsShowMetricsOperationDescription=N\u00E4yt\u00E4 yhteyden aikana ker\u00E4tyt ajoituksen mittarit.

DiagnosticsClearMetricsOperationDescription=Tyhjenn\u00E4 yhteyden aikana ker\u00E4tyt ajoituksen mittarit.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n tarkan diagnostiikan m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 tarkan diagnostiikan m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n tarkan diagnostiikan m\u00E4\u00E4ritetyn asiakkaan nimen mukaan.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 tarkan diagnostiikan m\u00E4\u00E4ritetyn asiakkaan nimen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa k\u00E4ytt\u00F6\u00F6n tarkan diagnostiikan m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa k\u00E4yt\u00F6st\u00E4 tarkan diagnostiikan m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto ottaa tarkan diagnostiikan k\u00E4ytt\u00F6\u00F6n.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Tarkka diagnostiikka on oletusarvoisesti poistettu k\u00E4yt\u00F6st\u00E4. Toiminto poistaa tarkan diagnostiikan k\u00E4yt\u00F6st\u00E4, jos se oli k\u00E4yt\u00F6ss\u00E4.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=P\u00E4ivit\u00E4 diagnostiikkataso m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan. Argumentin merkkijono voi olla joko tason nimi tai kokonaislukuarvo. Esimerkki: SEVERE tai 1000.

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=P\u00E4ivit\u00E4 diagnostiikkataso m\u00E4\u00E4ritetyn asiakkaan nimen mukaan. Argumentin merkkijono voi olla joko tason nimi tai kokonaislukuarvo. Esimerkki: SEVERE tai 1000.

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=P\u00E4ivit\u00E4 diagnostiikkataso m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan. Argumentin merkkijono voi olla joko tason nimi tai kokonaislukuarvo. Esimerkki: SEVERE tai 1000.

DiagnosticsUpdateDiagnosticLevelOperationDescription=P\u00E4ivit\u00E4 diagnostiikkataso. Argumentin merkkijono voi olla joko tason nimi tai kokonaislukuarvo. Esimerkki: SEVERE tai 1000.

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=P\u00E4ivit\u00E4 ensimm\u00E4isen virheen m\u00E4\u00E4rityksen puskurimuistin koko m\u00E4\u00E4ritetyn yhteystunnuksen etuliitteen mukaan. Puskurimuistin oletuskoko on 4 000 lokitietuetta.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=P\u00E4ivit\u00E4 ensimm\u00E4isen virheen m\u00E4\u00E4rityksen puskurimuistin koko m\u00E4\u00E4ritetyn asiakkaan nimen mukaan. Puskurimuistin oletuskoko on 4 000 lokitietuetta.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=P\u00E4ivit\u00E4 ensimm\u00E4isen virheen m\u00E4\u00E4rityksen puskurimuistin koko m\u00E4\u00E4ritetyn lokiinkirjausohjelman nimen mukaan. Puskurimuistin oletuskoko on 4 000 lokitietuetta.

DiagnosticsUpdateBufferSizeOperationDescription=P\u00E4ivit\u00E4 ensimm\u00E4isen virheen m\u00E4\u00E4rityksen puskurimuistin koko. Puskurimuistin oletuskoko on 4 000 lokitietuetta.

DiagnosticsReadLoggingConfigFileOperationDescription=Alusta lokiinkirjauksen ominaisuudet uudelleen ja lue lokiinkirjauksen konfiguraatio uudelleen m\u00E4\u00E4ritetyst\u00E4 tiedostosta, jonka muodon pit\u00E4isi olla java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Lis\u00E4\u00E4 ORA-XXXXX-muotoinen virhekoodi, jonka seuraavaa esiintymist\u00E4 valvotaan. JDBC-ajuri kirjoittaa ensimm\u00E4isen virheen m\u00E4\u00E4ritysdiagnostiikan konfiguroituun lokink\u00E4sittelij\u00E4\u00E4n, kun valvontalistassa oleva virhe havaitaan.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Poista m\u00E4\u00E4ritetty virhekoodi valvontalistasta. Virhekoodin on oltava muodossa ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=N\u00E4yt\u00E4 valvottavat virhekoodit.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfiguroi valvontalistan oletusvirhekoodit.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Vedosta ensimm\u00E4isen virheen m\u00E4\u00E4ritysdiagnostiikka kohdek\u00E4sittelij\u00E4lle.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Vedosta ensimm\u00E4isen virheen m\u00E4\u00E4ritysdiagnostiikka, kun tuleva poikkeus sis\u00E4lt\u00E4\u00E4 jonkin m\u00E4\u00E4ritetyist\u00E4 avainsanoista. Esimerkkej\u00E4 avainsanoista: "reset" ja "violation".

DiagnosticsShowExceptionKeywords=Aiemmin lis\u00E4tyt poikkeusten avainsanat.

DiagnosticsShowRecentOperations=Viimeisimm\u00E4t k\u00E4ytt\u00E4j\u00E4n t\u00E4lle MBeanille suorittamat toiminnot.

DiagnosticsClearExceptionKeywordsOperationDescription=Tyhjenn\u00E4 aiemmin lis\u00E4tyt avainsanat.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Lokit kirjoitetaan oletusarvoisesti lokink\u00E4sittelij\u00E4\u00E4n. T\u00E4m\u00E4 toiminto ottaa k\u00E4ytt\u00F6\u00F6n tai poistaa k\u00E4yt\u00F6st\u00E4 lokien kirjoittamisen ensimm\u00E4isen virheen m\u00E4\u00E4ritysdiagnostiikkaan.

DiagnosticsConnectionIdPrefixParameterDescription=Yhteystunnuksen etuliite.

DiagnosticsTenantNameParameterDescription=Asiakkaan nimi.

DiagnosticsLoggerNameParameterDescription=Lokiinkirjausohjelman nimi.

DiagnosticsLevelParameterDescription=Lokiinkirjausohjelman taso.

DiagnosticsBufferSizeParameterDescription=Ensimm\u00E4isen virheen m\u00E4\u00E4ritysdiagnostiikan puskurimuistin koko.

DiagnosticsConfigFileParameterDescription=Lokiinkirjauksen konfiguraatiotiedosto.

DiagnosticsErrorCodesParameterDescription=Virhekoodi muodossa ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Arvo tosi tai ep\u00E4tosi.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Avainsanat pilkulla eroteltuina arvoina.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






