#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controleaz\u0103 caracteristicile pentru diagnostic ale driverelor Oracle JDBC.

DiagnosabilityMBeanConstructor()=Constructorul cu argument zero pentru MBean-ul pentru diagnosticare Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Toate codurile de jurnalizare Oracle JDBC sunt controlate de acest atribut boolean. Dac\u0103 este fals, nu vor fi produse mesaje de jurnalizare. Dac\u0103 este adev\u0103rat, mesajele de jurnalizare vor fi controlate prin filtrele \u015Fi nivelurile de jurnalizare java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Driverele Oracle JDBC nu pot fi pornite sau oprite. Returneaz\u0103 \u00EEntotdeauna valoarea Fals.

DiagnosabilityMBeanStatisticsProviderDescription=Driverele Oracle JDBC nu ofer\u0103 statistici prin MBean-ul pentru diagnosticare.

DiagnosabilityMBeanTraceControllerDescription=Controler de urm\u0103rire Clio.

DiagnosabilityMBeanSuspendDescription=Opera\u0163ie de suspendare Clio.

DiagnosabilityMBeanResumeDescription=Opera\u0163ie de reluare Clio.

DiagnosabilityMBeanTraceDescription=Opera\u0163ie de urm\u0103rire Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Activeaz\u0103 jurnalizarea securizat\u0103 continu\u0103 a fi\u015Fierului. Filtrele ServiceName \u015Fi UserName sunt aplicabile dac\u0103 sunt setate. Implicit, este dezactivat\u0103.

DiagnosabilityMBeanDisableContinousLoggingDescription=Dezactiveaz\u0103 jurnalizarea securizat\u0103 continu\u0103 a fi\u015Fierului. Implicit, este dezactivat\u0103.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Activeaz\u0103 caracteristica Diagnosticare la primul e\u015Fec. Filtrele ServiceName \u015Fi UserName sunt aplicabile dac\u0103 sunt setate. \u00CEn mod prestabilit, este activat\u0103.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Dezactiveaz\u0103 caracteristica Diagnosticare la primul e\u015Fec. \u00CEn mod prestabilit este activat\u0103.

DiagnosabilityMBeanServiceNameFilterDescription=Dac\u0103 filtrul ServiceName este setat, atunci jurnalizarea \u00EEn memorie / continu\u0103 este activat\u0103 doar pentru conect\u0103rile serviciului configurat.

DiagnosabilityMBeanUserFilterDescription=Dac\u0103 filtrul User este setat, atunci jurnalizarea \u00EEn memorie / continu\u0103 este activat\u0103 doar pentru conect\u0103rile utilizatorului configurat.

ReplayStatisticsMBeanDescription=Expune statisticile func\u0163iei Application Continuity (AC).

ReplayStatisticsMBeanConstructor=Constructorul cu argument zero pentru MBean-ul pentru statistica Oracle JDBC AC

ReplayStatisticsMBeanAllStatisticsDescription=Preia statisticile AC din toate instan\u0163ele sursei de date cunoscute ale driverului.

ReplayStatisticsMBeanGetDSStatisticsDescription=Preia statisticile AC pentru o anumit\u0103 instan\u0163\u0103 a sursei de date a driverului.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controleaz\u0103 caracteristicile pentru diagnostic ale driverelor Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Toate codurile de jurnalizare Oracle JDBC sunt controlate de acest atribut boolean. Valoarea prestabilit\u0103 este False. Dac\u0103 este activat, mesajele de jurnalizare vor fi controlate prin filtrele \u015Fi nivelurile de jurnalizare java.util.logging. Dac\u0103 este dezactivat, nu se vor genera mesaje de jurnalizare.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Toate metricile evenimentelor captate de driverul JDBC sunt controlate de acest atribut boolean. Valoarea prestabilit\u0103 este Fals. Dac\u0103 este activat, metricile evenimentelor sunt captate de driver p\u00E2n\u0103 la dezactivare.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Returneaz\u0103 True dac\u0103 este activat\u0103 scrierea jurnalelor \u00EEn bufferul de urm\u0103rire in-memory. Valoarea prestabilit\u0103 este False.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune activeaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 prefixul ID-ului de conexiune indicat, dac\u0103 a fost dezactivat\u0103.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune dezactiveaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 prefixul ID-ului de conexiune indicat.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune activeaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 numele tenantului indicat, dac\u0103 a fost dezactivat\u0103.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune dezactiveaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 numele tenantului indicat.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune activeaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 numele programului de jurnalizare indicat, dac\u0103 a fost dezactivat\u0103.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune dezactiveaz\u0103 Diagnosticare la primul e\u015Fec dup\u0103 numele programului de jurnalizare indicat.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune activeaz\u0103 Diagnosticare la primul e\u015Fec dac\u0103 a fost dezactivat\u0103.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Caracteristica Diagnosticare la primul e\u015Fec este activat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163iune dezactiveaz\u0103 Diagnosticare la primul e\u015Fec.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=\u00CEn mod prestabilit, jurnalizarea este dezactivat\u0103. Aceast\u0103 opera\u0163iune activeaz\u0103 jurnalizarea dup\u0103 prefixul ID-ului de conexiune indicat.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 jurnalizarea dup\u0103 prefixul ID-ului de conexiune indicat, dac\u0103 jurnalizarea a fost activat\u0103.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 jurnalizarea dup\u0103 numele de tenant indicat.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 jurnalizarea dup\u0103 numele de tenant indicat, dac\u0103 jurnalizarea a fost activat\u0103.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 jurnalizarea dup\u0103 numele programului de jurnalizare indicat.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 jurnalizarea dup\u0103 numele programului de jurnalizare indicat, dac\u0103 jurnalizarea a fost activat\u0103.

DiagnosticsEnableLoggingOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 jurnalizarea.

DiagnosticsDisableLoggingOperationDescription=Jurnalizarea este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 jurnalizarea dac\u0103 jurnalizarea a fost activat\u0103.

DiagnosticsEnableMetricsOperationDescription=\u00CEncepe\u0163i colectarea metricilor pentru timp pe durata stabilirii conexiunii.

DiagnosticsDisableMetricsOperationDescription=Opri\u0163i colectarea metricilor pentru timp pe durata stabilirii conexiunii.

DiagnosticsShowMetricsOperationDescription=Afi\u015Fa\u0163i metricile pentru timp colectate pe durata stabilirii conexiunii.

DiagnosticsClearMetricsOperationDescription=Goli\u0163i metricile pentru timp colectate pe durata stabilirii conexiunii.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 prefixul ID-ului de conexiune indicat.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 prefixul ID-ului de conexiune indicat, dac\u0103 aceast\u0103 op\u0163iune a fost activat\u0103.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 numele de tenant indicat.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 numele de tenant indicat, dac\u0103 aceast\u0103 op\u0163iune a fost activat\u0103.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 numele programului de jurnalizare indicat.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 includerea informa\u0163iilor confiden\u0163iale dup\u0103 numele programului de jurnalizare indicat, dac\u0103 aceast\u0103 op\u0163iune a fost activat\u0103.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie activeaz\u0103 includerea informa\u0163iilor confiden\u0163iale.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Includerea informa\u0163iilor confiden\u0163iale \u00EEn diagnostic\u0103ri este dezactivat\u0103 \u00EEn mod prestabilit. Aceast\u0103 opera\u0163ie dezactiveaz\u0103 includerea informa\u0163iilor confiden\u0163iale, dac\u0103 aceast\u0103 op\u0163iune a fost activat\u0103.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Actualiza\u0163i nivelul de diagnosticare dup\u0103 prefixul ID-ului de conexiune indicat. \u015Eirul argumentului poate fi un nume de nivel sau un num\u0103r \u00EEntreg. De exemplu: "SEVERE" sau "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Actualiza\u0163i nivelul de diagnosticare dup\u0103 numele de tenant indicat. \u015Eirul argumentului poate fi un nume de nivel sau un num\u0103r \u00EEntreg. De exemplu: "SEVERE" sau "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Actualiza\u0163i nivelul de diagnosticare dup\u0103 numele programului de jurnalizare indicat. \u015Eirul argumentului poate fi un nume de nivel sau un num\u0103r \u00EEntreg. De exemplu: "SEVERE" sau "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Actualiza\u0163i nivelul de diagnosticare. \u015Eirul argumentului poate fi un nume de nivel sau un num\u0103r \u00EEntreg. De exemplu: "SEVERE" sau "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Actualiza\u0163i dimensiunea bufferului de diagnostic\u0103ri pentru Diagnosticare la primul e\u015Fec dup\u0103 prefixul ID-ului de conexiune indicat. Dimensiunea prestabilit\u0103 a bufferului este de 4000 de \u00EEnregistr\u0103ri \u00EEn jurnal.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Actualiza\u0163i dimensiunea bufferului de diagnostic\u0103ri Diagnosticare la primul e\u015Fec dup\u0103 numele de tenant indicat. Dimensiunea prestabilit\u0103 a bufferului este de 4000 de \u00EEnregistr\u0103ri \u00EEn jurnal.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Actualiza\u0163i dimensiunea bufferului de diagnostic\u0103ri pentru Diagnosticare la primul e\u015Fec dup\u0103 numele programului de jurnalizare indicat. Dimensiunea prestabilit\u0103 a bufferului este de 4000 de \u00EEnregistr\u0103ri \u00EEn jurnal.

DiagnosticsUpdateBufferSizeOperationDescription=Actualiza\u0163i dimensiunea bufferului de diagnostic\u0103ri pentru Diagnosticare la primul e\u015Fec. Dimensiunea prestabilit\u0103 a bufferului este de 4000 de \u00EEnregistr\u0103ri \u00EEn jurnal.

DiagnosticsReadLoggingConfigFileOperationDescription=Reini\u0163ializa\u0163i propriet\u0103\u0163ile jurnaliz\u0103rii \u015Fi reciti\u0163i configura\u0163ia jurnaliz\u0103rii din fi\u015Fierul indicat, care ar trebui s\u0103 aib\u0103 formatul java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Ad\u0103uga\u0163i codul de eroare a c\u0103rui urm\u0103toare apari\u0163ie \u00EEn formatul ORA-XXXXX trebuie urm\u0103rit\u0103. Driverul JDBC scrie diagnostic\u0103rile caracteristicii Diagnosticare la primul e\u015Fec \u00EEn rutina pentru jurnal configurat\u0103 atunci c\u00E2nd survine o eroare \u00EEn lista de urm\u0103rire.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Elimina\u0163i codul de eroare indicat din lista de urm\u0103rire. Codul de eroare trebuie s\u0103 fie \u00EEn format ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Afi\u015Fa\u0163i codurile de eroare urm\u0103rite \u00EEn acest moment.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configura\u0163i lista de urm\u0103rire cu codurile de eroare prestabilite.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Exporta\u0163i \u00EEntr-un fi\u015Fier de dump diagnostic\u0103rile caracteristicii Diagnosticare la primul e\u015Fec, \u00EEn rutina pentru destina\u0163ie.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Exporta\u0163i \u00EEntr-un fi\u015Fier de dump diagnostic\u0103rile caracteristicii Diagnosticare la primul e\u015Fec, dac\u0103 excep\u0163ia viitoare con\u0163ine unul dintre cuvintele cheie indicate. Exemple de cuvinte cheie sunt: resetare, \u00EEnc\u0103lcare.

DiagnosticsShowExceptionKeywords=Cuvintele cheie din excep\u0163ie, care au fost ad\u0103ugate anterior.

DiagnosticsShowRecentOperations=Cele mai recente opera\u0163iuni efectuate de utilizator asupra acestui MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=\u015Eterge\u0163i cuvintele cheie ad\u0103ugate anterior.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=\u00CEn mod prestabilit, jurnalele sunt scrise \u00EEn aceast\u0103 rutin\u0103 pentru jurnale. Aceast\u0103 opera\u0163iune activeaz\u0103 sau dezactiveaz\u0103 scrierea jurnalelor \u00EEn diagnostic\u0103rile caracteristicii Diagnosticare la primul e\u015Fec.

DiagnosticsConnectionIdPrefixParameterDescription=Prefixul ID-ului conexiunii.

DiagnosticsTenantNameParameterDescription=Numele de tenant.

DiagnosticsLoggerNameParameterDescription=Numele programului de jurnalizare.

DiagnosticsLevelParameterDescription=Nivelul programului de jurnalizare.

DiagnosticsBufferSizeParameterDescription=Dimensiunea bufferului de diagnostic\u0103ri pentru caracteristica Diagnosticare la primul e\u015Fec.

DiagnosticsConfigFileParameterDescription=Fi\u015Fierul de configurare a jurnaliz\u0103rii.

DiagnosticsErrorCodesParameterDescription=Un cod de eroare \u00EEn formatul ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Valoarea True sau False.

DiagnosticsCommaSeparatedKeywordsParameterDescription=Cuvintele cheie ca valori separate prin virgule.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






