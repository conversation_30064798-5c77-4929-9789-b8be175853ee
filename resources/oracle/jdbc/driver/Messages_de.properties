#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Kontrolliert die Diagnosabilty Features der Oracle JDBC-Treiber.

DiagnosabilityMBeanConstructor()=Der Null-Arg-Konstruktor f\u00FCr das Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=Der gesamte Oracle JDBC-Loggingcode wird von diesem Booleschen Attribut kontrolliert. Wenn das Attribut False ist, werden keine Logmeldungen erzeugt. Wenn es True ist, werden Logmeldungen mit java.util.logging-Ebenen und Filtern kontrolliert.

DiagnosabilityMBeanStateManageableDescription=Die Oracle JDBC-Treiber k\u00F6nnen nicht gestartet und gestoppt werden. Gibt immer False zur\u00FCck.

DiagnosabilityMBeanStatisticsProviderDescription=Die Oracle JDBC-Treiber stellen keine Statistiken \u00FCber das Diagnosability MBean bereit.

DiagnosabilityMBeanTraceControllerDescription=Clio-Tracecontroller.

DiagnosabilityMBeanSuspendDescription=Clio-Unterbrechungsvorgang.

DiagnosabilityMBeanResumeDescription=Clio-Wiederaufnahmevorgang.

DiagnosabilityMBeanTraceDescription=Clio-Tracevorgang.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktiviert das gesicherte kontinuierliche Dateilogging. Festgelegte ServiceName- und UserName-Filter sind anwendbar. Diese Option ist standardm\u00E4\u00DFig deaktiviert.

DiagnosabilityMBeanDisableContinousLoggingDescription=Deaktiviert das gesicherte kontinuierliche Dateilogging. Diese Option ist standardm\u00E4\u00DFig deaktiviert.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktiviert Diagnose beim ersten Fehler. Festgelegte ServiceName- und UserName-Filter sind anwendbar. Diese Funktion ist standardm\u00E4\u00DFig aktiviert.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Deaktiviert die Diagnose beim ersten Fehler. Diese Funktion ist standardm\u00E4\u00DFig aktiviert.

DiagnosabilityMBeanServiceNameFilterDescription=Wenn ein ServiceName-Filter festgelegt ist, wird das In-Memory-Tracing/kontinuierliche Logging nur f\u00FCr die Verbindungen des konfigurierten Service aktiviert.

DiagnosabilityMBeanUserFilterDescription=Wenn ein UserName-Filter festgelegt ist, wird das In-Memory-Tracing/kontinuierliche Logging nur f\u00FCr die Verbindungen des konfigurierten Benutzers aktiviert.

ReplayStatisticsMBeanDescription=Macht die Statistiken des Features "Application Continuity" (AC) verf\u00FCgbar.

ReplayStatisticsMBeanConstructor=Der Null-Arg-Konstruktor f\u00FCr das Oracle JDBC-AC-Statistik-MBean

ReplayStatisticsMBeanAllStatisticsDescription=Ruft AC-Statistiken f\u00FCr alle bekannten Treiberdatenquellen-Instanzen ab.

ReplayStatisticsMBeanGetDSStatisticsDescription=Ruft AC-Statistiken f\u00FCr eine bestimmte Treiberdatenquellen-Instanz ab.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Kontrolliert die Diagnosabilty Features der Oracle JDBC-Treiber.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Der gesamte Oracle JDBC-Loggingcode wird von diesem booleschen Attribut kontrolliert. Der Standardwert ist "false". Wenn das Attribut aktiviert ist, werden Logmeldungen mit java.util.logging-Ebenen und -Filtern kontrolliert. Wenn es deaktiviert ist, werden keine Logmeldungen erzeugt.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Alle Metriken von Ereignissen, die vom JDBC-Treiber erfasst werden, werden von diesem booleschen Attribut gesteuert. Der Standardwert ist "false". Bei Aktivierung werden die Metriken von Ereignissen vom Treiber erfasst, bis die Option deaktiviert wird.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Gibt "true" zur\u00FCck, wenn das Schreiben von Logs in den In-Memory-Tracepuffer aktiviert ist. Der Standardwert ist "false".

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Die Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang aktiviert die Diagnose beim ersten Fehler anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes, falls deaktiviert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Die Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang deaktiviert die Diagnose beim ersten Fehler anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Die Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang aktiviert die Diagnose beim ersten Fehler anhand des angegebenen Mandantennamens, falls deaktiviert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Die Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang deaktiviert die Diagnose beim ersten Fehler anhand des angegebenen Mandantennamens.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang aktiviert die Diagnose beim ersten Fehler anhand des angegebenen Loggernamens, falls deaktiviert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang deaktiviert die Diagnose beim ersten Fehler anhand des angegebenen Loggernamens.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang aktiviert die Diagnose beim ersten Fehler, falls sie deaktiviert wurde.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnose beim ersten Fehler ist standardm\u00E4\u00DFig aktiviert. Dieser Vorgang deaktiviert die Diagnose beim ersten Fehler.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert das Logging anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert das Logging anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes, wenn es aktiviert wurde.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert das Logging anhand des angegebenen Mandantennamens.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert das Logging anhand des angegebenen Mandantennamens, wenn es aktiviert wurde.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert das Logging anhand des angegebenen Loggernamens.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert das Logging anhand des angegebenen Loggernamens, wenn es aktiviert wurde.

DiagnosticsEnableLoggingOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert das Logging.

DiagnosticsDisableLoggingOperationDescription=Logging ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert das Logging, wenn es aktiviert wurde.

DiagnosticsEnableMetricsOperationDescription=Beginn der Erfassung von Timingmetriken w\u00E4hrend der Verbindungszeit.

DiagnosticsDisableMetricsOperationDescription=Ende der Erfassung von Timingmetriken w\u00E4hrend der Verbindungszeit.

DiagnosticsShowMetricsOperationDescription=Die w\u00E4hrend der Verbindungszeit erfassten Timingmetriken anzeigen.

DiagnosticsClearMetricsOperationDescription=Die w\u00E4hrend der Verbindungszeit erfassten Timingmetriken l\u00F6schen.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert die sensible Diagnose anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert die sensible Diagnose anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes, wenn sie aktiviert wurde.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert die sensible Diagnose anhand des angegebenen Mandantennamens.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert die sensible Diagnose anhand des angegebenen Mandantennamens, wenn sie aktiviert wurde.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert die sensible Diagnose anhand des angegebenen Loggernamens.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert die sensible Diagnose anhand des angegebenen Loggernamens, wenn sie aktiviert wurde.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang aktiviert die sensible Diagnose.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Die sensible Diagnose ist standardm\u00E4\u00DFig deaktiviert. Dieser Vorgang deaktiviert die sensible Diagnose, wenn sie aktiviert wurde.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Aktualisieren Sie die Diagnoseebene anhand des angegebenen Verbindungs-ID-Pr\u00E4fix. Die Argumentzeichenfolge kann in einem Ebenennamen oder einem Ganzzahlwert bestehen. Beispiel: "SEVERE" oder "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Aktualisieren Sie die Diagnoseebene anhand des angegebenen Mandantennamens. Die Argumentzeichenfolge kann in einem Ebenennamen oder einem Ganzzahlwert bestehen. Beispiel: "SEVERE" oder "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Aktualisieren Sie die Diagnoseebene anhand des angegebenen Loggernamens. Die Argumentzeichenfolge kann in einem Ebenennamen oder einem Ganzzahlwert bestehen. Beispiel: "SEVERE" oder "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Aktualisieren Sie die Diagnoseebene. Die Argumentzeichenfolge kann in einem Ebenennamen oder einem Ganzzahlwert bestehen. Beispiel: "SEVERE" oder "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Aktualisieren Sie die Gr\u00F6\u00DFe des Puffers f\u00FCr die Diagnose beim ersten Fehler anhand des angegebenen Verbindungs-ID-Pr\u00E4fixes. Die Standardpuffergr\u00F6\u00DFe betr\u00E4gt 4000 Logdatens\u00E4tze.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Aktualisieren Sie die Gr\u00F6\u00DFe des Puffers f\u00FCr die Diagnose beim ersten Fehler anhand des angegebenen Mandantennamens. Die Standardpuffergr\u00F6\u00DFe betr\u00E4gt 4000 Logdatens\u00E4tze.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Aktualisieren Sie die Gr\u00F6\u00DFe des Puffers f\u00FCr die Diagnose beim ersten Fehler anhand des angegebenen Loggernamens. Die Standardpuffergr\u00F6\u00DFe betr\u00E4gt 4000 Logdatens\u00E4tze.

DiagnosticsUpdateBufferSizeOperationDescription=Aktualisieren Sie die Gr\u00F6\u00DFe des Puffers f\u00FCr die Diagnose beim ersten Fehler. Die Standardpuffergr\u00F6\u00DFe betr\u00E4gt 4000 Logdatens\u00E4tze.

DiagnosticsReadLoggingConfigFileOperationDescription=Wiederholen Sie die Initialisierung der Loggingeigenschaften und das Lesen der Loggingkonfiguration aus der angegebenen Datei. Diese muss das java.util.Properties-Format aufweisen.

DiagnosticsAddErrorCodeToWatchListOperationDescription=F\u00FCgen Sie den Fehlercode, dessen n\u00E4chstes Vorkommen \u00FCberwacht werden soll, im Format ORA-XXXXX hinzu. Der JDBC-Treiber schreibt die Diagnosedaten der Diagnose beim ersten Fehler in den konfigurierten Log-Handler, wenn ein Fehler in der Watchlist auftritt.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Entfernen Sie den angegebenen Fehlercode aus der \u00DCberwachungsliste. Der Fehlercode muss das Format ORA-XXXXX haben.

DiagnosticsShowErrorCodesWatchListOperationDescription=Zeigen Sie die \u00FCberwachten Fehlercodes an.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurieren Sie die \u00DCberwachsungsliste mit den Standardfehlercodes.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Geben Sie die Diagnosedaten der Diagnose beim ersten Fehler im Ziel-H\u00E4ndler aus.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Geben Sie die Diagnosedaten der Diagnose beim ersten Fehler aus, wenn die zuk\u00FCnftige Ausnahme eines der angegebenen Schl\u00FCsselw\u00F6rter enth\u00E4lt. Die Beispielschl\u00FCsselw\u00F6rter sind reset, violation.

DiagnosticsShowExceptionKeywords=Die zuvor hinzugef\u00FCgten Ausnahmeschl\u00FCsselw\u00F6rter.

DiagnosticsShowRecentOperations=Die zuletzt von dem Benutzer auf diesem MBean ausgef\u00FChrten Vorg\u00E4nge.

DiagnosticsClearExceptionKeywordsOperationDescription=L\u00F6schen Sie die zuvor hinzugef\u00FCgten Schl\u00FCsselw\u00F6rter.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Standardm\u00E4\u00DFig werden Logs in den Log-Handler geschrieben. Dieser Vorgang aktiviert/deaktiviert das Schreiben von Logs in die Diagnosedaten der Diagnose beim ersten Fehler.

DiagnosticsConnectionIdPrefixParameterDescription=Das Verbindungs-ID-Pr\u00E4fix.

DiagnosticsTenantNameParameterDescription=Der Mandantenname.

DiagnosticsLoggerNameParameterDescription=Der Loggername.

DiagnosticsLevelParameterDescription=Die Loggerebene.

DiagnosticsBufferSizeParameterDescription=Die Puffergr\u00F6\u00DFe f\u00FCr die Diagnose beim ersten Fehler.

DiagnosticsConfigFileParameterDescription=Die Loggingkonfigurationsdatei.

DiagnosticsErrorCodesParameterDescription=Ein Fehlercode im Format ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Der Wert "true" oder "false".

DiagnosticsCommaSeparatedKeywordsParameterDescription=Die Schl\u00FCsselw\u00F6rter als kommagetrennte Werte.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






