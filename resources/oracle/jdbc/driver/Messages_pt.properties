#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controla as funcionalidades de diagn\u00F3stico dos drivers JDBC Oracle.

DiagnosabilityMBeanConstructor()=O criador de argumentos zero do MBean de diagn\u00F3stico do Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Todo o c\u00F3digo de registo no di\u00E1rio do Oracle JDBC \u00E9 controlado por este atributo booleano. Se for false, n\u00E3o ser\u00E3o produzidas mensagens de di\u00E1rio. Se for true, as mensagens de di\u00E1rio ser\u00E3o controladas pelos N\u00EDveis e Filtros de java.util.logging.

DiagnosabilityMBeanStateManageableDescription=N\u00E3o \u00E9 poss\u00EDvel iniciar e parar os drivers JDBC Oracle. \u00C9 sempre devolvido false.

DiagnosabilityMBeanStatisticsProviderDescription=Os drivers JDBC Oracle n\u00E3o fornecem estat\u00EDsticas atrav\u00E9s do MBean de diagn\u00F3stico.

DiagnosabilityMBeanTraceControllerDescription=Controlador de Diagn\u00F3stico de Clio.

DiagnosabilityMBeanSuspendDescription=Opera\u00E7\u00E3o de suspens\u00E3o de Clio.

DiagnosabilityMBeanResumeDescription=Opera\u00E7\u00E3o de retoma de Clio.

DiagnosabilityMBeanTraceDescription=Opera\u00E7\u00E3o de diagn\u00F3stico de Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Ativa o registo no di\u00E1rio de ficheiros cont\u00EDnuo e restrito. Os filtros ServiceName e UserName s\u00E3o aplic\u00E1veis se estiverem definidos. Est\u00E1 desativado por omiss\u00E3o.

DiagnosabilityMBeanDisableContinousLoggingDescription=Desativa o registo no di\u00E1rio de ficheiros cont\u00EDnuo e restrito. Est\u00E1 desativado por omiss\u00E3o.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Ativa a funcionalidade Diagnosticar Primeira Falha. Os filtros ServiceName e UserName s\u00E3o aplic\u00E1veis se estiverem definidos. Est\u00E1 ativada por omiss\u00E3o.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Desativa a funcionalidade Diagnosticar Primeira Falha. Est\u00E1 ativada por omiss\u00E3o.

DiagnosabilityMBeanServiceNameFilterDescription=Se o filtro ServiceName estiver definido, o registo no di\u00E1rio na mem\u00F3ria/cont\u00EDnuo s\u00F3 \u00E9 ativado para as liga\u00E7\u00F5es do servi\u00E7o configurado.

DiagnosabilityMBeanUserFilterDescription=Se o filtro do Utilizador estiver definido, o registo no di\u00E1rio na mem\u00F3ria/cont\u00EDnuo s\u00F3 \u00E9 ativado para as liga\u00E7\u00F5es do Utilizador configurado.

ReplayStatisticsMBeanDescription=Exp\u00F5e as estat\u00EDsticas da funcionalidade Application Continuity (AC).

ReplayStatisticsMBeanConstructor=O criador de argumentos zero do MBean de estat\u00EDsticas de AC do Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Obt\u00E9m as estat\u00EDsticas de AC em todas as inst\u00E2ncias de origem de dados do driver conhecidas.

ReplayStatisticsMBeanGetDSStatisticsDescription=Obt\u00E9m as estat\u00EDsticas de AC para uma inst\u00E2ncia da origem de dados do driver espec\u00EDfica.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controla as funcionalidades de diagn\u00F3stico dos drivers JDBC Oracle.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Todo o c\u00F3digo de registo no di\u00E1rio do Oracle JDBC \u00E9 controlado por este atributo booleano. O valor por omiss\u00E3o \u00E9 false. Se estiver ativado, as mensagens de di\u00E1rio ser\u00E3o controladas pelos N\u00EDveis e Filtros de java.util.logging. Se estiver desativado, n\u00E3o ser\u00E3o produzidas mensagens de di\u00E1rio.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Todas as m\u00E9tricas dos eventos capturados pelo driver JDBC s\u00E3o controladas por este atributo booleano. O valor por omiss\u00E3o \u00E9 false. Se estiver ativado, as m\u00E9tricas dos eventos s\u00E3o capturadas pelo driver at\u00E9 \u00E0 desativa\u00E7\u00E3o.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Devolve true se a escrita de di\u00E1rios no buffer de diagn\u00F3stico em mem\u00F3ria estiver ativada. O valor por omiss\u00E3o \u00E9 false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo prefixo da ID da liga\u00E7\u00E3o fornecido, se tiver sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo prefixo da ID da liga\u00E7\u00E3o fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo nome do tenant fornecido, se tiver sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo nome do tenant fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha pelo nome do registo no di\u00E1rio fornecido, se tiver sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha pelo nome do registo no di\u00E1rio fornecido.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o Diagn\u00F3stico da Primeira Falha se tiver sido desativado.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnosticar Primeira Falha est\u00E1 ativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o Diagn\u00F3stico da Primeira Falha.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o registo no di\u00E1rio pelo prefixo da ID da liga\u00E7\u00E3o fornecido.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o registo no di\u00E1rio pelo prefixo da ID da liga\u00E7\u00E3o fornecido se tiver sido ativado.

DiagnosticsEnableLoggingByTenantNameOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o registo no di\u00E1rio pelo nome do tenant fornecido.

DiagnosticsDisableLoggingByTenantNameOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o registo no di\u00E1rio pelo nome do tenant fornecido se tiver sido ativado.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o registo no di\u00E1rio pelo nome do registo no di\u00E1rio fornecido.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o registo no di\u00E1rio pelo nome do registo no di\u00E1rio fornecido se tiver sido ativado.

DiagnosticsEnableLoggingOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o registo no di\u00E1rio.

DiagnosticsDisableLoggingOperationDescription=O registo no di\u00E1rio est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o registo no di\u00E1rio se tiver sido ativado.

DiagnosticsEnableMetricsOperationDescription=Inicie a recolha de m\u00E9tricas de tempo durante o tempo de liga\u00E7\u00E3o.

DiagnosticsDisableMetricsOperationDescription=Pare a recolha de m\u00E9tricas de tempo durante o tempo de liga\u00E7\u00E3o.

DiagnosticsShowMetricsOperationDescription=Mostre as m\u00E9tricas de tempo recolhidas durante o tempo de liga\u00E7\u00E3o.

DiagnosticsClearMetricsOperationDescription=Limpe as m\u00E9tricas de tempo recolhidas durante o tempo de liga\u00E7\u00E3o.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico de dados sens\u00EDveis pelo prefixo da ID da liga\u00E7\u00E3o fornecido.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico de dados sens\u00EDveis pelo prefixo da ID da liga\u00E7\u00E3o fornecido se tiver sido ativado.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico de dados sens\u00EDveis pelo nome do tenant fornecido.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico de dados sens\u00EDveis pelo nome do tenant fornecido se tiver sido ativado.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico de dados sens\u00EDveis pelo nome do registo no di\u00E1rio fornecido.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico de dados sens\u00EDveis pelo nome do registo no di\u00E1rio fornecido se tiver sido ativado.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o ativa o diagn\u00F3stico de dados sens\u00EDveis.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=O diagn\u00F3stico de dados sens\u00EDveis est\u00E1 desativado por omiss\u00E3o. Esta opera\u00E7\u00E3o desativa o diagn\u00F3stico de dados sens\u00EDveis se tiver sido ativado.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo prefixo da ID da liga\u00E7\u00E3o fornecido. A cadeia de caracteres do argumento poder\u00E1 consistir num nome de n\u00EDvel ou num valor de n\u00FAmero inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo nome do tenant fornecido. A cadeia de caracteres do argumento poder\u00E1 consistir num nome de n\u00EDvel ou num valor de n\u00FAmero inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico pelo nome do registo no di\u00E1rio fornecido. A cadeia de caracteres do argumento poder\u00E1 consistir num nome de n\u00EDvel ou num valor de n\u00FAmero inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Atualize o n\u00EDvel de diagn\u00F3stico. A cadeia de caracteres do argumento poder\u00E1 consistir num nome de n\u00EDvel ou num valor de n\u00FAmero inteiro. Por exemplo: "SEVERE" ou "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Atualize o tamanho do buffer de diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha pelo prefixo da ID da liga\u00E7\u00E3o fornecido. O tamanho do buffer por omiss\u00E3o \u00E9 de 4000 registos de di\u00E1rio.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Atualize o tamanho do buffer de diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha pelo nome do tenant fornecido. O tamanho do buffer por omiss\u00E3o \u00E9 de 4000 registos de di\u00E1rio.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Atualize o tamanho do buffer de diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha pelo nome do registo no di\u00E1rio fornecido. O tamanho do buffer por omiss\u00E3o \u00E9 de 4000 registos de di\u00E1rio.

DiagnosticsUpdateBufferSizeOperationDescription=Atualize o tamanho do buffer de diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha. O tamanho do buffer por omiss\u00E3o \u00E9 de 4000 registos de di\u00E1rio.

DiagnosticsReadLoggingConfigFileOperationDescription=Reinicialize as propriedades de registo no di\u00E1rio e leia novamente a configura\u00E7\u00E3o do registo no di\u00E1rio a partir do ficheiro fornecido, que deve estar no formato de java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Acrescente o c\u00F3digo de erro a ser observado na ocorr\u00EAncia seguinte no formato ORA-XXXXX. O driver JDBC escreve o diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha na rotina de tratamento do di\u00E1rio configurada quando ocorre um erro na lista de controlo.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Retire o c\u00F3digo de erro fornecido da lista de controlo. O c\u00F3digo de erro deve estar no formato ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Mostre os c\u00F3digos de erro a serem observados.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configure a lista de controlo com os c\u00F3digos de erro por omiss\u00E3o.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Despeje o diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha na rotina de tratamento de destino.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Despeje o diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha quando a exce\u00E7\u00E3o futura contiver uma das palavras-chave fornecidas. As palavras-chave de exemplo s\u00E3o redefinir, viola\u00E7\u00E3o.

DiagnosticsShowExceptionKeywords=As palavras-chave de exce\u00E7\u00E3o acrescentadas anteriormente.

DiagnosticsShowRecentOperations=As opera\u00E7\u00F5es mais recentes efetuadas pelo utilizador neste MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Limpe as palavras-chave acrescentadas anteriormente.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Por omiss\u00E3o, os registos no di\u00E1rio s\u00E3o escritos na rotina de tratamento do di\u00E1rio. Esta opera\u00E7\u00E3o ativa ou desativa a escrita de di\u00E1rios no diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha.

DiagnosticsConnectionIdPrefixParameterDescription=O Prefixo da ID da Liga\u00E7\u00E3o.

DiagnosticsTenantNameParameterDescription=O Nome do Tenant.

DiagnosticsLoggerNameParameterDescription=O Nome do Registo no Di\u00E1rio.

DiagnosticsLevelParameterDescription=O N\u00EDvel do Registo no Di\u00E1rio.

DiagnosticsBufferSizeParameterDescription=O Tamanho do Buffer de diagn\u00F3stico da funcionalidade Diagnosticar Primeira Falha.

DiagnosticsConfigFileParameterDescription=O Ficheiro de Configura\u00E7\u00E3o do Registo no Di\u00E1rio.

DiagnosticsErrorCodesParameterDescription=Um c\u00F3digo de erro no formato ORA-XXXXX.

DiagnosticsEnabledParameterDescription=O valor true ou false.

DiagnosticsCommaSeparatedKeywordsParameterDescription=As palavras-chave como valores separados por v\u00EDrgulas.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






