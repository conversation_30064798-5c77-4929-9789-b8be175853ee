#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Kontrollerer fejlfindingsfunktionerne i Oracle JDBC-driverne.

DiagnosabilityMBeanConstructor()=Zero arg-constructor til Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=Al Oracle JDBC-logkode kontrolleres af denne booleske attribut. Hvis den er falsk, oprettes der ikke en logmeddelelse. Hvis den er sand, kontrolleres logmeddelelserne af java.util.logging Levels and Filters.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC-driverne kan ikke startes eller stoppes. Returnerer altid falsk.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC-driverne leverer ikke statistik via fejlfindings-MBean.

DiagnosabilityMBeanTraceControllerDescription=Clio-trace-controller.

DiagnosabilityMBeanSuspendDescription=Clio-suspenderingsoperation.

DiagnosabilityMBeanResumeDescription=Clio-forts\u00E6ttelsesoperation.

DiagnosabilityMBeanTraceDescription=Clio-trace-operation.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktiverer den sikrede l\u00F8bende fillogning. Filtrene ServiceName og UserName kan anvendes, hvis de er sat. Den er som standard deaktiveret.

DiagnosabilityMBeanDisableContinousLoggingDescription=Deaktiverer den sikrede l\u00F8bende fillogning. Den er som standard deaktiveret.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktiverer Diagnose ved f\u00F8rste fejl. Filtrene ServiceName og UserName anvendes, hvis de er sat. Som standard er dette aktiveret.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Deaktiverer Diagnose ved f\u00F8rste fejl. Som standard er dette aktiveret.

DiagnosabilityMBeanServiceNameFilterDescription=Hvis filteret ServiceName er sat, er sporing i hukommelse/l\u00F8bende logning kun aktiveret for forbindelserne i den konfigurerede service.

DiagnosabilityMBeanUserFilterDescription=Hvis brugerfilteret er sat, er sporing i hukommelse/l\u00F8bende logning kun aktiveret for forbindelserne for den konfigurerede bruger.

ReplayStatisticsMBeanDescription=Eksponerer statistikken for funktionen Applikationskontinuitet (AC).

ReplayStatisticsMBeanConstructor=Zero arg-konstrukt\u00F8r til Oracle JDBC AC-statistik MBean

ReplayStatisticsMBeanAllStatisticsDescription=Henter AC-statistik p\u00E5 tv\u00E6rs af alle kendte driverdatakildeinstanser.

ReplayStatisticsMBeanGetDSStatisticsDescription=Henter AC-statistik for en bestemt driverdatakildeinstans.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Kontrollerer fejlfindingsfunktionerne i Oracle JDBC-driverne.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Al Oracle JDBC-logkode kontrolleres af denne booleske attribut. Standardv\u00E6rdien er falsk. Hvis den er sl\u00E5et til, kontrolleres logmeddelelserne af java.util.logging Levels and Filters. Hvis den er sl\u00E5et fra, oprettes der ingen logmeddelelser.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Alle metrikker af h\u00E6ndelser, der registreres af JDBC-driveren, kontrolleres af denne booleske attribut. Standarden er falsk. Hvis den er aktiveret, bliver alle metrikker af h\u00E6ndelser registreret af driveren, indtil det deaktiveres.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Returnerer sand, hvis skrivning af logge til in-memory-trace-buffer er aktiveret. Standardv\u00E6rdien er falsk.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation aktiverer Diagnose ved f\u00F8rste fejl ved det angivne forbindelses-id-pr\u00E6fiks, hvis det var deaktiveret.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation deaktiverer Diagnose ved f\u00F8rste fejl ved det angivne forbindelses-id-pr\u00E6fiks.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation aktiverer Diagnose ved f\u00F8rste fejl ved det angivne lejernavn, hvis det var deaktiveret.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation deaktiverer Diagnose ved f\u00F8rste fejl med det angivne lejernavn.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation aktiverer Diagnose ved f\u00F8rste fejl ved det angivne logger-navn, hvis det var deaktiveret.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation deaktiverer Diagnose ved f\u00F8rste fejl ved det angivne logger-navn.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation aktiverer Diagnose ved f\u00F8rste fejl, hvis det var deaktiveret.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnose ved f\u00F8rste fejl er som standard aktiveret. Denne operation deaktiverer Diagnose ved f\u00F8rste fejl.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Logning er som standard deaktiveret. Denne operation aktiverer logning ved det angivne forbindelses-ID-pr\u00E6fiks.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Logning er som standard deaktiveret. Denne operation deaktiverer logning ved det angivne forbindelses-id-pr\u00E6fiks, hvis det er aktiveret.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Logning er som standard deaktiveret. Denne operation aktiverer logning ved det angivne lejernavn.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Logning er som standard deaktiveret. Denne operation deaktiverer logning ved det angivne lejernavn, hvis det er aktiveret.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Logning er som standard deaktiveret. Denne operation aktiverer logning ved det angivne logger-navn.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Logning er som standard deaktiveret. Denne operation deaktiverer logning ved det angivne logger-navn, hvis det er aktiveret.

DiagnosticsEnableLoggingOperationDescription=Logning er som standard deaktiveret. Denne operation aktiverer logning.

DiagnosticsDisableLoggingOperationDescription=Logning er som standard deaktiveret. Denne operation deaktiverer logning, hvis det er aktiveret.

DiagnosticsEnableMetricsOperationDescription=Start indsamling af timing-metrikker under forbindelsestid.

DiagnosticsDisableMetricsOperationDescription=Stop indsamling af timing-metrikker under forbindelsestid.

DiagnosticsShowMetricsOperationDescription=Vis timing-metrikker, der er indsamlet under forbindelsestid.

DiagnosticsClearMetricsOperationDescription=Ryd timing-metrikker, der er indsamlet under forbindelsestid.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation aktiverer f\u00F8lsom diagnostik ved det angivne forbindelses-id-pr\u00E6fiks.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation deaktiverer f\u00F8lsom diagnostik ved det angivne forbindelses-id-pr\u00E6fiks, hvis det er aktiveret.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation aktiverer f\u00F8lsom diagnostik ved det angivne lejernavn.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation deaktiverer f\u00F8lsom diagnostik ved det angivne lejernavn, hvis det er aktiveret.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation aktiverer f\u00F8lsom diagnostik ved det angivne logger-navn.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation deaktiverer f\u00F8lsom diagnostik ved det angivne logger-navn, hvis det er aktiveret.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation aktiverer f\u00F8lsom diagnostik.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=F\u00F8lsom diagnostik er som standard deaktiveret. Denne operation deaktiverer f\u00F8lsom diagnostik, hvis det er aktiveret.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Opdater diagnosticeringsniveauet ved det angivne forbindelses-id-pr\u00E6fiks. Argumentstrengen kan best\u00E5 af enten et niveaunavn eller en heltalsv\u00E6rdi. For eksempel: "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Opdater diagnosticeringsniveauet ved det angivne lejernavn. Argumentstrengen kan best\u00E5 af enten et niveaunavn eller en heltalsv\u00E6rdi. For eksempel: "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Opdater diagnosticeringsniveauet ved det angivne logger-navn. Argumentstrengen kan best\u00E5 af enten et niveaunavn eller en heltalsv\u00E6rdi. For eksempel: "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Opdater diagnosticeringsniveauet. Argumentstrengen kan best\u00E5 af enten et niveaunavn eller en heltalsv\u00E6rdi. For eksempel: "SEVERE" eller "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Opdater st\u00F8rrelsen p\u00E5 diagnosticeringsbufferen for Diagnose ved f\u00F8rste fejl ved det angivne forbindelses-id-pr\u00E6fiks. Standardbufferst\u00F8rrelsen er 4000 log-records.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Opdater st\u00F8rrelsen p\u00E5 diagnosticeringsbufferen for Diagnose ved f\u00F8rste fejl ved det angivne lejernavn. Standardbufferst\u00F8rrelsen er 4000 log-records.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Opdater st\u00F8rrelsen p\u00E5 diagnosticeringsbufferen for Diagnose ved f\u00F8rste fejl ved det angivne logger-navn. Standardbufferst\u00F8rrelsen er 4000 log-records.

DiagnosticsUpdateBufferSizeOperationDescription=Opdater st\u00F8rrelsen p\u00E5 diagnosticeringsbufferen for Diagnose ved f\u00F8rste fejl. Standardbufferst\u00F8rrelsen er 4000 log-records.

DiagnosticsReadLoggingConfigFileOperationDescription=Initialiser logningsegenskaberne igen, og genl\u00E6s logningskonfigurationen i den angivne fil, som skal v\u00E6re i java.util.Properties-format.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Tilf\u00F8j den fejlkode, som overv\u00E5gning af n\u00E6ste forekomst skal foretages for, i formatet ORA-XXXXX. JDBC-driveren skriver Diagnose ved f\u00F8rste fejl-diagnostik til den konfigurerede log-handler, n\u00E5r der opst\u00E5r en fejl p\u00E5 overv\u00E5gningslisten.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Fjern den angivne fejlkode fra overv\u00E5gningslisten. Fejlkoden skal have formatet ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Vis de fejlkoder, der overv\u00E5ges.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurer overv\u00E5gningslisten med standardfejlkoderne.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Dump Diagnose ved f\u00F8rste fejl-diagnostik til m\u00E5l-handleren

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Dump Diagnose ved f\u00F8rste fejl-diagnostik, n\u00E5r den fremtidige undtagelse indeholder et af de angivne n\u00F8gleord. Eksempeln\u00F8gleordene er reset, violation.

DiagnosticsShowExceptionKeywords=Tidligere tilf\u00F8jede undtagelsesn\u00F8gleord.

DiagnosticsShowRecentOperations=De seneste handlinger, som brugeren har udf\u00F8rt p\u00E5 denne MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Ryd de tidligere tilf\u00F8jede n\u00F8gleord.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Logge skrives som standard til log-handleren. Denne operation aktiverer eller deaktiverer skrivning af logge til Diagnose ved f\u00F8rste fejl-diagnostik.

DiagnosticsConnectionIdPrefixParameterDescription=Forbindelses-id-pr\u00E6fikset.

DiagnosticsTenantNameParameterDescription=Lejernavnet.

DiagnosticsLoggerNameParameterDescription=Logger-navnet.

DiagnosticsLevelParameterDescription=Logningsniveauet.

DiagnosticsBufferSizeParameterDescription=Diagnostikbufferst\u00F8rrelsen for Diagnose ved f\u00F8rste fejl.

DiagnosticsConfigFileParameterDescription=Logningskonfigurationsfilen.

DiagnosticsErrorCodesParameterDescription=En fejlkode i formatet ORA-XXXXX.

DiagnosticsEnabledParameterDescription=V\u00E6rdien sand eller falsk.

DiagnosticsCommaSeparatedKeywordsParameterDescription=N\u00F8gleordene som kommaseparerede v\u00E6rdier.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






