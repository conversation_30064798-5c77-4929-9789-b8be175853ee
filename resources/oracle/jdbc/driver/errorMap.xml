EXCEPTION" comment="NULL value returned by EXEC TOOLS statement" />
  <error oraErrorFrom="2125" sqlState="82119" sqlException="SQLEXCEPTION" comment="connect error; can't get error text" />
  <error oraErrorFrom="2126" sqlState="07008" sqlException="SQLDATAEXCEPTION" comment="Count of array elements cannot be negative" />
  <error oraErrorFrom="2127" sqlState="82120" sqlException="SQLEXCEPTION" comment="precompiler/SQLLIB version mismatch" />
  <error oraErrorFrom="2129" sqlState="82121" sqlException="SQLEXCEPTION" comment="FETCHed number of bytes is odd" />
  <error oraErrorFrom="2130" sqlState="82122" sqlException="SQLEXCEPTION" comment="EXEC TOOLS interface is not available" />
  <error oraErrorFrom="2131" sqlState="82123" sqlException="SQLEXCEPTION" comment="runtime context in use" />
  <error oraErrorFrom="2132" sqlState="82124" sqlException="SQLEXCEPTION" comment="unable to allocate runtime context" />
  <error oraErrorFrom="2133" sqlState="82125" sqlException="SQLEXCEPTION" comment="unable to initialize process for use with threads" />
  <error oraErrorFrom="2134" sqlState="82126" sqlException="SQLEXCEPTION" comment="invalid runtime context" />
  <error oraErrorFrom="3113" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="End-of-file on communication channel" />
  <error oraErrorFrom="3114" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="Not connected to Oracle database" />
  <error oraErrorFrom="8006" sqlState="24000" sqlException="SQLEXCEPTION" comment="Invalid cursor state" />
  <error oraErrorFrom="12514" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="Listener does not currently know of service requested in connect descriptor" />
  <error oraErrorFrom="16456" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="Switchover to standby in progress or completed" />
  <error oraErrorFrom="12170" sqlState="08006" sqlException="SQLTIMEOUTEXCEPTION" comment="Outbound connection timeout expired" />

  <!-- JDBC ORA error numbers follow -->

  <error oraErrorFrom="17000" sqlState="00000" sqlException="SQLEXCEPTION" comment="JDBC driver success" />
  <error oraErrorFrom="17001" sqlState="72000" sqlException="SQLEXCEPTION" comment="JDBC driver internal error" />
  <error oraErrorFrom="17002" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="I/O exception" />
  <error oraErrorFrom="17003" oraErrorTo="17007" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17008" sqlState="08003" sqlException="SQLRECOVERABLEEXCEPTION" comment="Operation on closed connection" />
  <error oraErrorFrom="17009" oraErrorTo="17023" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17024" sqlState="02000" sqlException="SQLEXCEPTION" comment="No data read" />
  <error oraErrorFrom="17025" oraErrorTo="17078" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17079" sqlState="08000" sqlException="SQLNONTRANSIENTCONNECTIONEXCEPTION" comment="user credentials error" />
  <error oraErrorFrom="17080" oraErrorTo="17088" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17089" sqlState="72000" sqlException="SQLRECOVERABLEEXCEPTION" comment="Driver internal error" />
  <error oraErrorFrom="17090" oraErrorTo="17161" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17162" sqlState="99999" sqlException="SQLFEATURENOTSUPPORTEDEXCEPTION" comment="Unsupported holdability value" />
  <error oraErrorFrom="17163" oraErrorTo="17252" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17253" sqlState="00000" sqlException="SQLCLIENTINFOEXCEPTION" comment="invalid clientInfo name" />
  <error oraErrorFrom="17254" oraErrorTo="17355" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17365" oraErrorTo="17409" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="17410" sqlState="08000" sqlException="SQLRECOVERABLEEXCEPTION" comment="No more data to read from socket" />
  <error oraErrorFrom="17411" oraErrorTo="17499" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="18700" oraErrorTo="18713" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />
  <error oraErrorFrom="18714" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="Login timeout expired" />
  <!--
    Prior to the 23.1 release, a login timeout would be thrown as a
    SQLRecoverableException. This was not correct. JDBC drivers are specified to
    throw a SQLTimeoutException in this case. The behavior is specified in the
    JavaDoc of the java.sql.SQLTimeoutException class.
  -->
  <error oraErrorFrom="18714" sqlState="08006" sqlException="SQLTIMEOUTEXCEPTION" comment="Login timeout expired" />
  <error oraErrorFrom="18730" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="Interrupted IO error" />
  <error oraErrorFrom="18731" oraErrorTo="18899" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC driver errors" />

  <!-- JDBC thin client Net ORA errors -->

  <error oraErrorFrom="17820" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="17821" sqlState="08000" sqlException="SQLEXCEPTION" comment="connection exception" />
  <error oraErrorFrom="17822" oraErrorTo="17823" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="17824" oraErrorTo="17826" sqlState="22000" sqlException="SQLRECOVERABLEEXCEPTION" comment="data exception" />
  <error oraErrorFrom="17827" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17828" oraErrorTo="17849" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17850" oraErrorTo="17857" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17858" oraErrorTo="17860" sqlState="61000" sqlException="SQLEXCEPTION" comment="resource error" />
  <error oraErrorFrom="17861" oraErrorTo="17864" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17865" oraErrorTo="17867" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17868" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="17869" oraErrorTo="17870" sqlState="22004" sqlException="SQLEXCEPTION" comment="null value not allowed" />
  <error oraErrorFrom="17871" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="17872" oraErrorTo="17873" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17874" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17875" oraErrorTo="17899" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17900" sqlState="08003" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection does not exist" />
  <error oraErrorFrom="17901" sqlState="08000" sqlException="SQLRECOVERABLEEXCEPTION" comment="already connected" />
  <error oraErrorFrom="17902" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="17903" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17904" oraErrorTo="17906" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="17907" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17908" sqlState="22004" sqlException="SQLEXCEPTION" comment="null value not allowed" />
  <error oraErrorFrom="17909" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="17910" oraErrorTo="17949" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17950" sqlState="99999" sqlException="SQLEXCEPTION" comment="invalid version" />
  <error oraErrorFrom="17951" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="17952" sqlState="99999" sqlException="SQLEXCEPTION" comment="invalid value" />
  <error oraErrorFrom="17953" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="17954" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="17955" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17956" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17957" oraErrorTo="17958" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="17959" oraErrorTo="17960" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="17961" oraErrorTo="17962" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="17963" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="17964" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="17965" oraErrorTo="17966" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="17967" oraErrorTo="17999" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18900" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="operation failed" />
  <error oraErrorFrom="18901" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18902" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18903" sqlState="0A000" sqlException="SQLRECOVERABLEEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="18904" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="18905" oraErrorTo="18907" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18908" oraErrorTo="18909" sqlState="61000" sqlException="SQLEXCEPTION" comment="resource error" />
  <error oraErrorFrom="18910" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18911" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18912" sqlState="22000" sqlException="SQLRECOVERABLEEXCEPTION" comment="data exception" />
  <error oraErrorFrom="18913" oraErrorTo="18914" sqlState="22000" sqlException="SQLRECOVERABLEEXCEPTION" comment="data exception" />
  <error oraErrorFrom="18915" oraErrorTo="18916" sqlState="0A000" sqlException="SQLRECOVERABLEEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="18917" sqlState="61000" sqlException="SQLEXCEPTION" comment="resource error" />
  <error oraErrorFrom="18918" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18919" oraErrorTo="18920" sqlState="0A000" sqlException="SQLRECOVERABLEEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="18921" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="incomplete value" />
  <error oraErrorFrom="18922" oraErrorTo="18923" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="18924" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18925" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18926" oraErrorTo="18949" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18950" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18951" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="18952" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="18953" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18954" oraErrorTo="18956" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  <error oraErrorFrom="18957" sqlState="61000" sqlException="SQLEXCEPTION" comment="resource error" />
  <error oraErrorFrom="18958" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="18959" oraErrorTo="18999" sqlState="99999" sqlException="SQLRECOVERABLEEXCEPTION" comment="JDBC thin driver network errors" />
  
  <!-- JDBC thin client JSON ORA errors -->

  <error oraErrorFrom="26301" sqlState="08006" sqlException="SQLRECOVERABLEEXCEPTION" comment="connection failure" />
  <error oraErrorFrom="26302" oraErrorTo="26303" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="26304" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="26305" sqlState="22023" sqlException="SQLEXCEPTION" comment="invalid parameter value" />
  <error oraErrorFrom="26306" sqlState="0A000" sqlException="SQLEXCEPTION" comment="feature not supported" />
  <error oraErrorFrom="26307" sqlState="22026" sqlException="SQLEXCEPTION" comment="string data - length mismatch" />
  <error oraErrorFrom="26308" oraErrorTo="26309" sqlState="22003" sqlException="SQLEXCEPTION" comment="numeric value out of range" />
  <error oraErrorFrom="26310" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="26311" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="26312" sqlState="02000" sqlException="SQLEXCEPTION" comment="no data exception" />
  <error oraErrorFrom="26313" oraErrorTo="26314" sqlState="42000" sqlException="SQLEXCEPTION" comment="syntax error or access rule violation" />
  <error oraErrorFrom="26315" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="26316" sqlState="02000" sqlException="SQLEXCEPTION" comment="no data exception" />
  <error oraErrorFrom="26317" oraErrorTo="26319" sqlState="99999" sqlException="SQLEXCEPTION" comment="invalid parser state/value" />
  <error oraErrorFrom="26320" sqlState="07006" sqlException="SQLEXCEPTION" comment="restricted datatype attribute violation" />
  <error oraErrorFrom="26321" oraErrorTo="26322" sqlState="99999" sqlException="SQLEXCEPTION" comment="invalid modification" />
  <error oraErrorFrom="26323" oraErrorTo="26328" sqlState="22000" sqlException="SQLEXCEPTION" comment="data exception" />
  <error oraErrorFrom="26329" oraErrorTo="26335" sqlState="99999" sqlException="SQLEXCEPTION" comment="invalid state" />
  <error oraErrorFrom="26336" sqlState="22007" sqlException="SQLEXCEPTION" comment="invalid date-time format" />
  <error oraErrorFrom="26337" oraErrorTo="26399" sqlState="99999" sqlException="SQLEXCEPTION" comment="JDBC thin driver JSON errors" />

  <!-- JDBC ORA error numbers above -->

  <error oraErrorFrom="18" oraErrorTo="21" sqlState="61000" sqlException="SQLRECOVERABLEEXCEPTION" comment="Resource failure. Unable to create session" />
  <error oraErrorFrom="24" oraErrorTo="35" sqlState="61000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="50" oraErrorTo="68" sqlState="61000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="101" oraErrorTo="120" sqlState="62000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="150" oraErrorTo="159" sqlState="63000" sqlException="SQLEXCEPTION" comment="XA/two task error" />
  <error oraErrorFrom="200" oraErrorTo="369" sqlState="64000" sqlException="SQLEXCEPTION" comment="Cntl/dbase/redo log errors" />
  <error oraErrorFrom="370" oraErrorTo="429" sqlState="60000" sqlException="SQLEXCEPTION" comment="System error" />
  <error oraErrorFrom="430" oraErrorTo="439" sqlState="67000" sqlException="SQLEXCEPTION" comment="Licensing error" />
  <error oraErrorFrom="440" oraErrorTo="569" sqlState="62000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="570" oraErrorTo="599" sqlState="69000" sqlException="SQLEXCEPTION" comment="SQL*Connect error" />
  <error oraErrorFrom="600" oraErrorTo="899" sqlState="60000" sqlException="SQLEXCEPTION" comment="System error" />
  <error oraErrorFrom="900" oraErrorTo="999" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="1000" oraErrorTo="1099" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="1001" oraErrorTo="1003" sqlState="24000" sqlException="SQLEXCEPTION" comment="Invalid cursor state" />
  <error oraErrorFrom="1100" oraErrorTo="1250" sqlState="64000" sqlException="SQLEXCEPTION" comment="Cntl/dbase/redo log error" />
  <error oraErrorFrom="1402" oraErrorTo="1478" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="1479" oraErrorTo="1480" sqlState="22024" sqlException="SQLDATAEXCEPTION" comment="Unterminated C string" />
  <error oraErrorFrom="1481" oraErrorTo="1489" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="1490" oraErrorTo="1493" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="1494" oraErrorTo="1499" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="1500" oraErrorTo="1699" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="1700" oraErrorTo="1799" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="1800" oraErrorTo="1899" sqlState="22008" sqlException="SQLDATAEXCEPTION" comment="Datetime field overflow" />
  <error oraErrorFrom="2091" oraErrorTo="2092" sqlState="40000" sqlException="SQLTRANSACTIONROLLBACKEXCEPTION" comment="Transaction rollback" />
  <error oraErrorFrom="1900" oraErrorTo="2099" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="2140" oraErrorTo="2289" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="2290" oraErrorTo="2299" sqlState="23000" sqlException="SQLINTEGRITYCONSTRAINTVIOLATIONEXCEPTION" comment="Constraint violation" />
  <error oraErrorFrom="2376" oraErrorTo="2399" sqlState="61000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="2400" oraErrorTo="2419" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="2420" oraErrorTo="2424" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="2425" oraErrorTo="2449" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="2450" oraErrorTo="2499" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="2700" oraErrorTo="2899" sqlState="63000" sqlException="SQLEXCEPTION" comment="XA/two task error" />
  <error oraErrorFrom="3000" oraErrorTo="3099" sqlState="0A000" sqlException="SQLEXCEPTION" comment="Feature not supported" />
  <error oraErrorFrom="3100" oraErrorTo="3199" sqlState="63000" sqlException="SQLEXCEPTION" comment="XA/two task error" />
  <error oraErrorFrom="3276" oraErrorTo="3299" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="4000" oraErrorTo="4019" sqlState="22023" sqlException="SQLDATAEXCEPTION" comment="Invalid parameter value" />
  <error oraErrorFrom="4020" oraErrorTo="4039" sqlState="61000" sqlException="SQLEXCEPTION" comment="MTS server/detached process" />
  <error oraErrorFrom="4040" oraErrorTo="4059" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="4060" oraErrorTo="4069" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="4070" oraErrorTo="4099" sqlState="42000" sqlException="SQLSYNTAXERROREXCEPTION" comment="Syntax error, access rule" />
  <error oraErrorFrom="6000" oraErrorTo="6149" sqlState="66000" sqlException="SQLEXCEPTION" comment="SQL*Net driver error" />
  <error oraErrorFrom="6200" oraErrorTo="6249" sqlState="63000" sqlException="SQLEXCEPTION" comment="XA/two task error" />
  <error oraErrorFrom="6250" oraErrorTo="6429" sqlState="66000" sqlException="SQLEXCEPTION" comment="SQL*Net driver error" />
  <error oraErrorFrom="6430" oraErrorTo="6449" sqlState="60000" sqlException="SQLEXCEPTION" comment="System error" />
  <error oraErrorFrom="6511" oraErrorTo="6511" sqlState="24000" sqlException="SQLEXCEPTION" comment="Invalid cursor state" />
  <error oraErrorFrom="6500" oraErrorTo="6599" sqlState="65000" sqlException="SQLEXCEPTION" comment="PL/SQL error" />
  <error oraErrorFrom="6600" oraErrorTo="6999" sqlState="66000" sqlException="SQLEXCEPTION" comment="SQL*Net driver error" />
  <error oraErrorFrom="7000" oraErrorTo="7199" sqlState="69000" sqlException="SQLEXCEPTION" comment="SQL*Connect error" />
  <error oraErrorFrom="7200" oraErrorTo="7999" sqlState="60000" sqlException="SQLEXCEPTION" comment="System error" />
  <error oraErrorFrom="8000" oraErrorTo="8190" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="9700" oraErrorTo="9999" sqlState="60000" sqlException="SQLEXCEPTION" comment="System error" />
  <error oraErrorFrom="10000" oraErrorTo="10999" sqlState="90000" sqlException="SQLEXCEPTION" comment="Debugging event" />
  <error oraErrorFrom="12000" oraErrorTo="12019" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="12100" oraErrorTo="12299" sqlState="66000" sqlException="SQLEXCEPTION" comment="SQL*Net driver error" />
  <error oraErrorFrom="12300" oraErrorTo="12499" sqlState="72000" sqlException="SQLEXCEPTION" comment="SQL execute phase error" />
  <error oraErrorFrom="12500" oraErrorTo="12599" sqlState="66000" sqlException="SQLEXCEPTION" comment="SQL*Net driver error" />
  <error oraErrorFrom="12700" oraErrorTo="21999" sqlState="72000" sqlException="SQLEXCEPTION" comment=" SQL execute phase error" />
</oraErrorSqlStateSqlExceptionMapping>

