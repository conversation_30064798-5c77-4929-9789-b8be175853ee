#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Controls the diagnosabilty features of the Oracle JDBC drivers.

DiagnosabilityMBeanConstructor()=The zero arg constructor for the Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=All Oracle JDBC logging code is controlled by this boolean attribute. If false, no log messages will be produced. If true, log messages will be controlled by java.util.logging Levels and Filters.

DiagnosabilityMBeanStateManageableDescription=The Oracle JDBC drivers cannot be started and stopped. Always returns false.

DiagnosabilityMBeanStatisticsProviderDescription=The Oracle JDBC drivers do not provide statistics via the Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Clio Trace Controller.

DiagnosabilityMBeanSuspendDescription=Clio suspend operation.

DiagnosabilityMBeanResumeDescription=Clio resume operation.

DiagnosabilityMBeanTraceDescription=Clio trace operation.

DiagnosabilityMBeanEnableContinousLoggingDescription=Enables the secured continuous file logging. ServiceName and UserName filters are applicable if set. By default it is disabled.

DiagnosabilityMBeanDisableContinousLoggingDescription=Disables the secured continuous file logging. By default it is disabled.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Enables Diagnose First Failure. ServiceName and UserName filters are applicable if set. By default it is enabled.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Disables Diagnose First Failure. By default it is enabled.

DiagnosabilityMBeanServiceNameFilterDescription=If ServiceName filter is set then the in-memory / continuous logging is enabled only for the connections of the configured service.

DiagnosabilityMBeanUserFilterDescription=If User filter is set then the in-memory / continuous logging is enabled only for the connections of the configured User.

ReplayStatisticsMBeanDescription=Exposes the statistics of the Application Continuity (AC) feature.

ReplayStatisticsMBeanConstructor=The zero arg constructor for the Oracle JDBC AC statistics MBean

ReplayStatisticsMBeanAllStatisticsDescription=Gets AC statistics across all known driver data source instances.

ReplayStatisticsMBeanGetDSStatisticsDescription=Gets AC statistics for a particular driver data source instance.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Controls the diagnosabilty features of the Oracle JDBC drivers.

DiagnosticsMBeanLoggingEnabledAttributeDescription=All Oracle JDBC logging code is controlled by this boolean attribute. Default is false. If turned on, log messages will be controlled by java.util.logging Levels and Filters. If turned off, no log messages will be produced.

DiagnosticsMBeanMetricsEnabledAttributeDescription=All metrics of events captured by the JDBC driver are controlled by this boolean attribute. Default is false. If enabled, the metrics of events are captured by the driver until disabled.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Returns true if writing logs to in-memory trace buffer is enabled. Default is false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose First Failure is enabled by default. This operation enables Diagnose First Failure by the given connection id prefix if it was disabled.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose First Failure is enabled by default. This operation disables Diagnose First Failure by the given connection id prefix.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose First Failure is enabled by default. This operation enables Diagnose First Failure by the given tenant name if it was disabled.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose First Failure is enabled by default. This operation disables Diagnose First Failure by the given tenant name.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose First Failure is enabled by default. This operation enables Diagnose First Failure by the given logger name if it was disabled.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose First Failure is enabled by default. This operation disables Diagnose First Failure by the given logger name.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnose First Failure is enabled by default. This operation enables Diagnose First Failure if it was disabled.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnose First Failure is enabled by default. This operation disables Diagnose First Failure.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Logging is disabled by default. This operation enables logging by the given connection id prefix.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Logging is disabled by default. This operation disables logging by the given connection id prefix if it was enabled.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Logging is disabled by default. This operation enables logging by the given tenant name.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Logging is disabled by default. This operation disables logging by the given tenant name if it was enabled.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Logging is disabled by default. This operation enables logging by the given logger name.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Logging is disabled by default. This operation disables logging by the given logger name if it was enabled.

DiagnosticsEnableLoggingOperationDescription=Logging is disabled by default. This operation enables logging.

DiagnosticsDisableLoggingOperationDescription=Logging is disabled by default. This operation disables logging if it was enabled.

DiagnosticsEnableMetricsOperationDescription=Start collecting timing metrics during connection time.

DiagnosticsDisableMetricsOperationDescription=Stop collecting timing metrics during connection time.

DiagnosticsShowMetricsOperationDescription=Show the timing metrics collected during connection time.

DiagnosticsClearMetricsOperationDescription=Clear the timing metrics collected during connection time.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Sensitive diagnostics is disabled by default. This operation enables sensitive diagnostics by the given connection id prefix.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Sensitive diagnostics is disabled by default. This operation disables sensitive diagnostics by the given connection id prefix if it was enabled.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Sensitive diagnostics is disabled by default. This operation enables sensitive diagnostics by the given tenant name.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Sensitive diagnostics is disabled by default. This operation disables sensitive diagnostics by the given tenant name if it was enabled.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Sensitive diagnostics is disabled by default. This operation enables sensitive diagnostics by the given logger name.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Sensitive diagnostics is disabled by default. This operation disables sensitive diagnostics by the given logger name if it was enabled.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Sensitive diagnostics is disabled by default. This operation enables sensitive diagnostics.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Sensitive diagnostics is disabled by default. This operation disables sensitive diagnostics if it was enabled.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Update the diagnostic level by the given connection id prefix. The argument string may consist of either a level name or an integer value. For example: "SEVERE" or "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Update the diagnostic level by the given tenant name. The argument string may consist of either a level name or an integer value. For example: "SEVERE" or "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Update the diagnostic level by the given logger name. The argument string may consist of either a level name or an integer value. For example: "SEVERE" or "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Update the diagnostic level. The argument string may consist of either a level name or an integer value. For example: "SEVERE" or "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Update Diagnose First Failure diagnostic buffer size by the given connection id prefix. The default buffer size is 4000 log records.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Update Diagnose First Failure diagnostic buffer size by the given tenant name. The default buffer size is 4000 log records.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Update Diagnose First Failure diagnostic buffer size by the given logger name. The default buffer size is 4000 log records.

DiagnosticsUpdateBufferSizeOperationDescription=Update Diagnose First Failure diagnostic buffer size. The default buffer size is 4000 log records.

DiagnosticsReadLoggingConfigFileOperationDescription=Reinitialize the logging properties and reread the logging configuration from the given file, which should be in java.util.Properties format.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Add the error code to be watched for next occurrence in the format ORA-XXXXX. The JDBC driver writes Diagnose First Failure diagnostics to the configured log handler when an error in the watch list occurs.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Remove the given error code from the watch list. The error code should be in the format ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Show the error codes being watched.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configure the watch list with the default error codes.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Dump Diagnose First Failure diagnostics to the target handler.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Dump Diagnose First Failure diagnostics when the future exception contains one of the keywords given. The example keywords are reset, violation.

DiagnosticsShowExceptionKeywords=The exception keywords added previously.

DiagnosticsShowRecentOperations=The most recent operations performed by the user on this MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Clear the keywords added previously.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=By default logs are written to the log handler. This operation enables or disables writing logs to Diagnose First Failure diagnostics.

DiagnosticsConnectionIdPrefixParameterDescription=The Connection ID Prefix.

DiagnosticsTenantNameParameterDescription=The Tenant Name.

DiagnosticsLoggerNameParameterDescription=The Logger Name.

DiagnosticsLevelParameterDescription=The Logger Level.

DiagnosticsBufferSizeParameterDescription=The Diagnose First Failure diagnostic Buffer Size.

DiagnosticsConfigFileParameterDescription=The Logging Config File.

DiagnosticsErrorCodesParameterDescription=An error code in the format ORA-XXXXX.

DiagnosticsEnabledParameterDescription=The true or false value.

DiagnosticsCommaSeparatedKeywordsParameterDescription=The keywords as comma separated values.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






