#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Beheert de diagnosabilty-functies van de Oracle JDBC-drivers.

DiagnosabilityMBeanConstructor()=De nulargumentconstructor voor de diagnosability-MBean van Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Alle Oracle JDBC-logcode wordt beheerd door dit Boole-attribuut. Bij 'false' worden geen logberichten geproduceerd. Bij 'true' worden logberichten beheerd door java.util.logging-niveaus en -filters.

DiagnosabilityMBeanStateManageableDescription=De Oracle JDBC-drivers kunnen niet worden gestart en gestopt. Retourneert altijd 'false'.

DiagnosabilityMBeanStatisticsProviderDescription=De Oracle JDBC-drivers leveren geen statistieken via de diagnosability-MBean.

DiagnosabilityMBeanTraceControllerDescription=Clio-traceercontroleprogramma

DiagnosabilityMBeanSuspendDescription=Clio-onderbrekingsbewerking

DiagnosabilityMBeanResumeDescription=Clio-hervattingsbewerking

DiagnosabilityMBeanTraceDescription=Clio-traceerbewerking

DiagnosabilityMBeanEnableContinousLoggingDescription=Hiermee wordt beveiligde, doorlopende bestandslogging geactiveerd. De filters 'ServiceName' en 'UserName' worden toegepast, als deze zijn ingesteld. De functie is standaard inactief.

DiagnosabilityMBeanDisableContinousLoggingDescription=Hiermee wordt beveiligde, doorlopende bestandslogging gedeactiveerd. De functie is standaard inactief.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Hiermee wordt 'Eerste fout diagnosticeren' geactiveerd. De filters 'ServiceName' en 'UserName' worden toegepast, als deze zijn ingesteld. De functie is standaard actief.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Hiermee wordt 'Eerste fout diagnosticeren' gedeactiveerd. De functie is standaard actief.

DiagnosabilityMBeanServiceNameFilterDescription=Als het filter 'ServiceName' is ingesteld, is geheugentracering/doorlopend loggen alleen actief voor de verbindingen van de geconfigureerde service.

DiagnosabilityMBeanUserFilterDescription=Als het filter 'Gebruiker' is ingesteld, is geheugentracering/doorlopend loggen alleen actief voor de verbindingen van de geconfigureerde gebruiker.

ReplayStatisticsMBeanDescription=Hiermee worden de statistieken van de functie Continu\u00EFteit van de applicatie (AC) zichtbaar gemaakt.

ReplayStatisticsMBeanConstructor=De nulargumentconstructor voor de AC-statistieken-MBean van Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Haalt de AC-statistieken op voor alle bekende gegevensbroninstances voor drivers.

ReplayStatisticsMBeanGetDSStatisticsDescription=Haalt de AC-statistieken op voor een bepaalde gegevensbroninstance voor drivers.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Beheert de diagnosabilty-functies van de Oracle JDBC-drivers.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Alle Oracle JDBC-logcode wordt beheerd door dit Boole-attribuut. De standaardwaarde is 'Niet waar'. Als deze optie is ingeschakeld, worden logberichten beheerd door java.util.logging-niveaus en -filters. Als deze optie is uitgeschakeld, worden er geen logberichten geproduceerd.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Alle metrics van events die door de JDBC-driver worden vastgelegd, worden beheerd door dit Boole-attribuut. De standaardwaarde is 'Niet waar'. Als u deze optie activeert, worden de metrics van events door de driver vastgelegd tot de optie wordt gedeactiveerd.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Hiermee wordt 'Waar' geretourneerd als het schrijven van logs naar de geheugentraceringsbuffer is geactiveerd. De standaardwaarde is 'Niet waar'.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven prefix 'Verbindings-ID' geactiveerd als deze optie is gedeactiveerd.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven prefix 'Verbindings-ID' gedeactiveerd.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven tenantnaam geactiveerd als deze optie is gedeactiveerd.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven tenantnaam gedeactiveerd.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven loggernaam geactiveerd als deze optie is gedeactiveerd.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' op de opgegeven loggernaam gedeactiveerd.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' geactiveerd als deze optie is gedeactiveerd.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription='Eerste fout diagnosticeren' is standaard geactiveerd. Met deze bewerking wordt 'Eerste fout diagnosticeren' gedeactiveerd.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven prefix 'Verbindings-ID' geactiveerd.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven prefix 'Verbindings-ID' gedeactiveerd als dit is geactiveerd.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven tenantnaam geactiveerd.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven tenantnaam gedeactiveerd als dit is geactiveerd.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven loggernaam geactiveerd.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen op de opgegeven loggernaam gedeactiveerd als dit is geactiveerd.

DiagnosticsEnableLoggingOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen geactiveerd.

DiagnosticsDisableLoggingOperationDescription=Loggen is standaard gedeactiveerd. Met deze bewerking wordt loggen gedeactiveerd als dit is geactiveerd.

DiagnosticsEnableMetricsOperationDescription=Begin met het verzamelen van timingmetrics tijdens verbindingstijd.

DiagnosticsDisableMetricsOperationDescription=Stop met het verzamelen van timingmetrics tijdens verbindingstijd.

DiagnosticsShowMetricsOperationDescription=Toon de tijdens verbindingstijd verzamelde timingmetrics.

DiagnosticsClearMetricsOperationDescription=Wis de tijdens verbindingstijd verzamelde timingmetrics.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven prefix 'Verbindings-ID' geactiveerd.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven prefix 'Verbindings-ID' gedeactiveerd als deze is geactiveerd.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven tenantnaam geactiveerd.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven tenantnaam gedeactiveerd als deze is geactiveerd.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven tenantnaam geactiveerd.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose op de opgegeven loggernaam gedeactiveerd als deze is geactiveerd.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose geactiveerd.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Gevoelige diagnose is standaard gedeactiveerd. Met deze bewerking wordt gevoelige diagnose gedeactiveerd als deze is geactiveerd.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Werk het diagnoseniveau op de opgegeven prefix voor de verbindings-ID bij. De argumentstring kan bestaan uit een niveaunaam of een geheel getal, bijvoorbeeld "SEVERE" of 1000.

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Werk het diagnoseniveau op de opgegeven tenantnaam bij. De argumentstring kan bestaan uit een niveaunaam of een geheel getal, bijvoorbeeld "SEVERE" of 1000.

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Werk het diagnoseniveau op de opgegeven loggernaam bij. De argumentstring kan bestaan uit een niveaunaam of een geheel getal, bijvoorbeeld "SEVERE" of 1000.

DiagnosticsUpdateDiagnosticLevelOperationDescription=Werk het diagnoseniveau bij. De argumentstring kan bestaan uit een niveaunaam of een geheel getal, bijvoorbeeld "SEVERE" of 1000.

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Werk de grootte van de buffer voor 'Eerste fout diagnosticeren' op de opgegeven prefix voor de verbindings-ID bij. De standaardbuffergrootte is 4000 logrecords.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Werk de grootte van de buffer voor 'Eerste fout diagnosticeren' op de opgegeven tenantnaam bij. De standaardbuffergrootte is 4000 logrecords.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Werk de grootte van de buffer voor 'Eerste fout diagnosticeren' op de opgegeven loggernaam bij. De standaardbuffergrootte is 4000 logrecords.

DiagnosticsUpdateBufferSizeOperationDescription=Werk de grootte van de buffer voor 'Eerste fout diagnosticeren' bij. De standaardbuffergrootte is 4000 logrecords.

DiagnosticsReadLoggingConfigFileOperationDescription=Hiermee worden de loggingeigenschappen opnieuw ge\u00EFnitialiseerd en wordt de loggingconfiguratie opnieuw gelezen uit het opgegeven bestand dat de indeling java.util.Properties moet hebben.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Voeg de foutcode toe die moeten worden gevolgd voor het volgende geval in de indeling ORA-XXXXX. Door de JDBC-driver worden de diagnostische gegevens van 'Eerste fout diagnosticeren' in de geconfigureerde loghandler geschreven bij het optreden van een fout in de watchlist.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Verwijder de gegeven foutcode uit de watchlist. De foutcode moet de indeling ORA-XXXXX hebben.

DiagnosticsShowErrorCodesWatchListOperationDescription=Hiermee worden de foutcodes getoond die worden gevolgd.

DiagnosticsResetErrorCodesWatchListOperationDescription=Configureer de watchlist met de standaardfoutcodes.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Dump de diagnostische gegevens van 'Eerste fout diagnosticeren' naar de doelhandler.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Dump de diagnostische gegevens van 'Eerste fout diagnosticeren' wanneer de toekomstige uitzondering een van de opgegeven sleutelwoorden bevat. De voorbeeldsleutelwoorden zijn reset, violation.

DiagnosticsShowExceptionKeywords=De uitzonderingssleutelwoorden die eerder zijn toegevoegd.

DiagnosticsShowRecentOperations=De meest recente bewerkingen die door de gebruiker op deze MBean zijn uitgevoerd.

DiagnosticsClearExceptionKeywordsOperationDescription=Wis de sleutelwoorden die eerder zijn toegevoegd.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Logs worden standaard naar de loghandler geschreven. Met deze bewerking wordt het schrijven van logs naar de diagnostische gegevens van 'Eerste fout diagnosticeren' geactiveerd of gedeactiveerd.

DiagnosticsConnectionIdPrefixParameterDescription=De prefix 'Verbindings-ID'

DiagnosticsTenantNameParameterDescription=De tenantnaam

DiagnosticsLoggerNameParameterDescription=De loggernaam

DiagnosticsLevelParameterDescription=Het loggerniveau

DiagnosticsBufferSizeParameterDescription=De grootte van de buffer voor 'Eerste fout diagnosticeren'

DiagnosticsConfigFileParameterDescription=Het configuratiebestand voor logging

DiagnosticsErrorCodesParameterDescription=Een foutcode met de indeling ORA-XXXXX

DiagnosticsEnabledParameterDescription=De waarde 'Waar' of 'Niet waar'

DiagnosticsCommaSeparatedKeywordsParameterDescription=De sleutelwoorden als waarden met een komma als scheidingsteken

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






