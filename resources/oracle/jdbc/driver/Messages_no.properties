#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Kontrollerer diagnostiserbarhetsfunksjonene til Oracle JDBC-driverne.

DiagnosabilityMBeanConstructor()=Null arg-konstrukt\u00F8ren for diagnostiserbarhets-MBean for Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Alle Oracle JDBC-loggekoder kontrolleres av dette boolske attributtet. Hvis usann produseres det ingen loggmeldinger. Hvis sann kontrolleres loggmeldingene av niv\u00E5er og filtre for java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC-driverne kan ikke startes og stoppes. Returnerer alltid usann.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC-driverne angir ikke statistikker via diagnostiserbarhets-MBean.

DiagnosabilityMBeanTraceControllerDescription=Sporingskontroller for Clio.

DiagnosabilityMBeanSuspendDescription=Suspenderingsoperasjon for Clio.

DiagnosabilityMBeanResumeDescription=Gjenopptakelsesoperasjon for Clio.

DiagnosabilityMBeanTraceDescription=Sporingsoperasjon for Clio.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktiverer sikker, kontinuerlig fillogging. Filtrene ServiceName og UserName kan brukes hvis de er angitt. Som standard er dette deaktivert.

DiagnosabilityMBeanDisableContinousLoggingDescription=Deaktiverer sikker, kontinuerlig fillogging. Som standard er dette deaktivert.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktiverer diagnose ved f\u00F8rste feil. Filtrene ServiceName og UserName kan brukes hvis de er angitt. Som standard er dette aktivert.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Deaktiverer diagnose ved f\u00F8rste feil. Som standard er dette aktivert.

DiagnosabilityMBeanServiceNameFilterDescription=Hvis filteret ServiceName er angitt, er kontinuerlig logging / i minnet aktivert bare for tilkoblingene til den konfigurerte tjenesten.

DiagnosabilityMBeanUserFilterDescription=Hvis filteret User er angitt, er kontinuerlig logging / i minnet aktivert bare for tilkoblingene til den konfigurerte brukeren.

ReplayStatisticsMBeanDescription=Viser statistikken for funksjonen for applikasjonskontinuitet (AC, Application Continuity).

ReplayStatisticsMBeanConstructor=Null arg-konstrukt\u00F8ren for AC-statistikk-MBean for Oracle JDBC

ReplayStatisticsMBeanAllStatisticsDescription=Henter AC-statistikk for alle kjente driverdatakildeforekomster.

ReplayStatisticsMBeanGetDSStatisticsDescription=Henter AC-statistikk for en bestemt driverdatakildeforekomst.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Kontrollerer diagnostiserbarhetsfunksjonene til Oracle JDBC-driverne.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Alle Oracle JDBC-loggekoder kontrolleres av dette boolske attributtet. Standardverdien er usann. Hvis det er aktivert, kontrolleres loggmeldingene av niv\u00E5er og filtre for java.util.logging. Hvis det er deaktivert, produseres ingen loggmeldinger.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Alle m\u00E5linger for hendelser som registreres av JDBC-driveren, styres av dette boolske attributtet. Standard er usann. Hvis dette er aktivert, registreres m\u00E5linger for hendelser av driveren til det deaktiveres.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Returnerer sann hvis skriving av logger til sporingsbufferen i minnet er aktivert. Standardverdien er usann.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen aktiverer diagnose ved f\u00F8rste feil med det angitte ID-prefikset for tilkobling hvis det var deaktivert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen deaktiverer diagnose ved f\u00F8rste feil med det angitte ID-prefikset for tilkobling.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen aktiverer diagnose ved f\u00F8rste feil med det angitte leiernavnet hvis det var deaktivert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen deaktiverer diagnose ved f\u00F8rste feil med det angitte leiernavnet.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen aktiverer diagnose ved f\u00F8rste feil med det angitte loggernavnet hvis det var deaktivert.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen deaktiverer diagnose ved f\u00F8rste feil med det angitte loggernavnet.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen aktiverer diagnose ved f\u00F8rste feil hvis det var deaktivert.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnose ved f\u00F8rste feil er som standard aktivert. Denne operasjonen deaktiverer diagnose ved f\u00F8rste feil.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Logging er som standard deaktivert. Denne operasjonen aktiverer logging med ID-prefikset for den angitte tilkoblingen.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Logging er som standard deaktivert. Denne operasjonen deaktiverer logging med ID-prefikset for den angitte tilkoblingen hvis logging var aktivert.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Logging er som standard deaktivert. Denne operasjonen aktiverer logging med navnet p\u00E5 den angitte leieren.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Logging er som standard deaktivert. Denne operasjonen deaktiverer logging med navnet p\u00E5 den angitte leieren hvis logging var aktivert.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Logging er som standard deaktivert. Denne operasjonen aktiverer logging med navnet p\u00E5 den angitte loggeren.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Logging er som standard deaktivert. Denne operasjonen deaktiverer logging med navnet p\u00E5 den angitte loggeren hvis logging var aktivert.

DiagnosticsEnableLoggingOperationDescription=Logging er som standard deaktivert. Denne operasjonen aktiverer logging.

DiagnosticsDisableLoggingOperationDescription=Logging er som standard deaktivert. Denne operasjonen deaktiverer logging hvis logging var aktivert.

DiagnosticsEnableMetricsOperationDescription=Start innsamling av tidsangivelsesm\u00E5linger under tilkoblingstiden.

DiagnosticsDisableMetricsOperationDescription=Stopp innsamling av tidsangivelsesm\u00E5linger under tilkoblingstiden.

DiagnosticsShowMetricsOperationDescription=Vis tidsangivelsesm\u00E5lingene som ble samlet inn under tilkoblingstiden.

DiagnosticsClearMetricsOperationDescription=Fjern tidsangivelsesm\u00E5lingene som ble samlet inn under tilkoblingstiden.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen aktiverer sensitiv diagnostikk med ID-prefikset for den angitte tilkoblingen.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen deaktiverer sensitiv diagnostikk med ID-prefikset for den angitte tilkoblingen hvis sensitiv diagnostikk var aktivert.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen aktiverer sensitiv diagnostikk med navnet p\u00E5 den angitte leieren.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen deaktiverer sensitiv diagnostikk med navnet p\u00E5 den angitte leieren hvis sensitiv diagnostikk var aktivert.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen aktiverer sensitiv diagnostikk med navnet p\u00E5 den angitte loggeren.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen deaktiverer sensitiv diagnostikk med navnet p\u00E5 den angitte loggeren hvis sensitiv diagnostikk var aktivert.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen aktiverer sensitiv diagnostikk.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Sensitiv diagnostikk er som standard deaktivert. Denne operasjonen deaktiverer sensitiv diagnostikk hvis sensitiv diagnostikk var aktivert.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Oppdater diagnostikkniv\u00E5et med det angitte ID-prefikset. Argumentstrengen kan best\u00E5 enten av et niv\u00E5navn eller en heltallsverdi. For eksempel: ALVORLIG eller 1000.

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Oppdater diagnostikkniv\u00E5et med det angitte leiernavnet. Argumentstrengen kan best\u00E5 enten av et niv\u00E5navn eller en heltallsverdi. For eksempel: ALVORLIG eller 1000.

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Oppdater diagnostikkniv\u00E5et med det angitte loggernavnet. Argumentstrengen kan best\u00E5 enten av et niv\u00E5navn eller en heltallsverdi. For eksempel: ALVORLIG eller 1000.

DiagnosticsUpdateDiagnosticLevelOperationDescription=Oppdater diagnostikkniv\u00E5et. Argumentstrengen kan best\u00E5 enten av et niv\u00E5navn eller en heltallsverdi. For eksempel: ALVORLIG eller 1000.

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Oppdater bufferst\u00F8rrelsen for diagnostikk for diagnose ved f\u00F8rste feil med ID-prefikset for den angitte tilkoblingen. Standard bufferst\u00F8rrelse er 4000 loggposter.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Oppdater bufferst\u00F8rrelsen for diagnostikk for diagnose ved f\u00F8rste feil med det angitte leiernavnet. Standard bufferst\u00F8rrelse er 4000 loggposter.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Oppdater bufferst\u00F8rrelsen for diagnostikk for diagnose ved f\u00F8rste feil med det angitte loggernavnet. Standard bufferst\u00F8rrelse er 4000 loggposter.

DiagnosticsUpdateBufferSizeOperationDescription=Oppdater bufferst\u00F8rrelsen for diagnostikk for diagnose ved f\u00F8rste feil. Standard bufferst\u00F8rrelse er 4000 loggposter.

DiagnosticsReadLoggingConfigFileOperationDescription=Initialiser loggingsegenskapene p\u00E5 nytt, og les loggingskonfigurasjonen p\u00E5 nytt fra den angitte filen, som skal ha formatet java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Legg til feilkoden som skal overv\u00E5kes for neste forekomst, med formatet ORA-XXXXX. JDBC-driveren skriver diagnostikk for diagnose ved f\u00F8rste feil til den konfigurerte loggbehandlingen n\u00E5r en feil i overv\u00E5kingslisten forekommer.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Fjern den angitte feilkoden fra overv\u00E5kingslisten. Feilkoden skal v\u00E6re i formatet ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Vis feilkodene som overv\u00E5kes.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurer overv\u00E5kingslisten med de standard feilkodene.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Dump diagnostikk for diagnose ved f\u00F8rste feil til m\u00E5lbehandlingen.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Dump diagnostikk for diagnose ved f\u00F8rste feil n\u00E5r det fremtidige unntaket inneholder ett av n\u00F8kkelordene som er angitt. Eksempeln\u00F8kkelordene er tilbakestilt, overtredelse.

DiagnosticsShowExceptionKeywords=Unntaksn\u00F8kkelordene som er lagt til tidligere.

DiagnosticsShowRecentOperations=De nyeste operasjonene som er utf\u00F8rt av brukeren p\u00E5 MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Fjern n\u00F8kkelordene som er lagt til tidligere.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Logger skrives som standard til loggbehandlingen. Denne operasjonen aktiverer eller deaktiverer logger til diagnostikk for diagnose ved f\u00F8rste feil.

DiagnosticsConnectionIdPrefixParameterDescription=ID-prefikset for tilkoblingen.

DiagnosticsTenantNameParameterDescription=Leiernavnet.

DiagnosticsLoggerNameParameterDescription=Loggernavnet.

DiagnosticsLevelParameterDescription=Loggerniv\u00E5et.

DiagnosticsBufferSizeParameterDescription=Bufferst\u00F8rrelsen for diagnostikk for diagnose ved f\u00F8rste feil.

DiagnosticsConfigFileParameterDescription=Konfigurasjonsfilen for loggingen.

DiagnosticsErrorCodesParameterDescription=En feilkode i formatet ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Verdien sann eller usann.

DiagnosticsCommaSeparatedKeywordsParameterDescription=N\u00F8kkelordene er kommadelte verdier.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






