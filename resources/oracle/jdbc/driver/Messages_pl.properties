#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Kontroluje funkcje diagnostyczne sterownik\u00F3w Oracle JDBC.

DiagnosabilityMBeanConstructor()=Konstruktor z zerowymi argumentami dla obiektu MBean diagnostyki Oracle JDBC

DiagnosabilityMBeanLoggingEnabledDescription=Ca\u0142o\u015B\u0107 kodu rejestruj\u0105cego Oracle JDBC jest kontrolowana za pomoc\u0105 tego argumentu logicznego. Je\u015Bli ma on warto\u015B\u0107 "false", to nie s\u0105 tworzone \u017Cadne komunikaty zapisywane w dzienniku. Je\u015Bli ma warto\u015B\u0107 "true", to komunikaty zapisywane w dzienniku b\u0119d\u0105 kontrolowane przy u\u017Cyciu poziom\u00F3w i filtr\u00F3w z java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Sterownik\u00F3w Oracle JDBC nie mo\u017Cna uruchamia\u0107 ani zatrzymywa\u0107. Dla opcji "always" jest zwracana warto\u015B\u0107 "false".

DiagnosabilityMBeanStatisticsProviderDescription=Sterowniki Oracle JDBC nie udost\u0119pniaj\u0105 statystyk za po\u015Brednictwem obiektu Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Clio - kontroler \u015Bledzenia.

DiagnosabilityMBeanSuspendDescription=Clio - operacja zawieszenia.

DiagnosabilityMBeanResumeDescription=Clio - operacja wznowienia Clio.

DiagnosabilityMBeanTraceDescription=Clio - operacja \u015Bledzenia.

DiagnosabilityMBeanEnableContinousLoggingDescription=W\u0142\u0105cza zabezpieczone ci\u0105g\u0142e rejestrowanie plik\u00F3w. S\u0105 stosowane filtry ServiceName i UserName, je\u015Bli zosta\u0142y ustawione. Domy\u015Blnie wy\u0142\u0105czone.

DiagnosabilityMBeanDisableContinousLoggingDescription=Wy\u0142\u0105cza zabezpieczone ci\u0105g\u0142e rejestrowanie plik\u00F3w. Domy\u015Blnie wy\u0142\u0105czone.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=W\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia". S\u0105 stosowane filtry ServiceName i UserName, je\u015Bli zosta\u0142y ustawione. Domy\u015Blnie w\u0142\u0105czona.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Wy\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia". Domy\u015Blnie w\u0142\u0105czona.

DiagnosabilityMBeanServiceNameFilterDescription=Je\u015Bli filtr ServiceName jest ustawiony, to rejestrowanie w pami\u0119ci lub rejestrowanie ci\u0105g\u0142e jest w\u0142\u0105czone tylko dla po\u0142\u0105cze\u0144 skonfigurowanej us\u0142ugi.

DiagnosabilityMBeanUserFilterDescription=Je\u015Bli filtr User jest ustawiony, to rejestrowanie w pami\u0119ci lub rejestrowanie ci\u0105g\u0142e jest w\u0142\u0105czone tylko dla po\u0142\u0105cze\u0144 skonfigurowanego u\u017Cytkownika.

ReplayStatisticsMBeanDescription=Eksponuje statystyki funkcji Application Continuity (AC).

ReplayStatisticsMBeanConstructor=Konstruktor z zerowymi argumentami dla obiektu MBean statystyk Oracle JDBC AC

ReplayStatisticsMBeanAllStatisticsDescription=Uzyskuje statystyki AC dla wszystkich znanych instancji \u017Ar\u00F3de\u0142 danych sterownika.

ReplayStatisticsMBeanGetDSStatisticsDescription=Uzyskuje statystyki AC dla konkretnej instancji \u017Ar\u00F3d\u0142a danych sterownika.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Kontroluje funkcje diagnostyczne sterownik\u00F3w Oracle JDBC.

DiagnosticsMBeanLoggingEnabledAttributeDescription=Ca\u0142o\u015B\u0107 kodu rejestruj\u0105cego Oracle JDBC jest kontrolowana za pomoc\u0105 tego atrybutu logicznego. Domy\u015Blnie ma on warto\u015B\u0107 "false". Je\u015Bli zostanie w\u0142\u0105czony, to komunikaty rejestrowane w dzienniku b\u0119d\u0105 kontrolowane przy u\u017Cyciu poziom\u00F3w i filtr\u00F3w z java.util.logging. Je\u015Bli zostanie wy\u0142\u0105czony, to w dzienniku nie b\u0119d\u0105 rejestrowane \u017Cadne komunikaty.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Wszystkie metryki zdarze\u0144 rejestrowane przez sterownik JDBC s\u0105 kontrolowane za pomoc\u0105 tego atrybutu logicznego. Domy\u015Blnie ma on warto\u015B\u0107 "false". Je\u015Bli zostanie w\u0142\u0105czony, to metryki zdarze\u0144 b\u0119d\u0105 rejestrowane przez ten sterownik, dop\u00F3ki ten atrybut nie zostanie wy\u0142\u0105czony.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Zwraca "true", je\u015Bli jest w\u0142\u0105czone zapisywanie dziennik\u00F3w w buforze \u015Bladu w pami\u0119ci. Domy\u015Blnie: false.

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja w\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanego prefiksu ID po\u0142\u0105czenia, je\u015Bli diagnostyka zosta\u0142a wy\u0142\u0105czona.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanego prefiksu ID po\u0142\u0105czenia.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja w\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy dzier\u017Cawy, je\u015Bli diagnostyka zosta\u0142a wy\u0142\u0105czona.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy dzier\u017Cawy.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja w\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego, je\u015Bli diagnostyka zosta\u0142a wy\u0142\u0105czona.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja w\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia", je\u015Bli zosta\u0142a ona wy\u0142\u0105czona.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnostyka "Diagnoza pierwszego niepowodzenia" jest domy\u015Blnie w\u0142\u0105czone. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia".

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja w\u0142\u0105cza rejestrowanie z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja wy\u0142\u0105cza rejestrowanie w dzienniku z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia, je\u015Bli rejestrowanie zosta\u0142o w\u0142\u0105czone.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja w\u0142\u0105cza rejestrowanie w dzienniku z u\u017Cyciem podanej nazwy dzier\u017Cawy.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja wy\u0142\u0105cza rejestrowanie w dzienniku z u\u017Cyciem podanej nazwy dzier\u017Cawy, je\u015Bli zosta\u0142o ono w\u0142\u0105czone.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja w\u0142\u0105cza rejestrowanie w dzienniku z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja wy\u0142\u0105cza rejestrowanie w dzienniku z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego, je\u015Bli zosta\u0142o ono w\u0142\u0105czone.

DiagnosticsEnableLoggingOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja w\u0142\u0105cza rejestrowanie w dzienniku.

DiagnosticsDisableLoggingOperationDescription=Rejestrowanie w dzienniku jest domy\u015Blnie wy\u0142\u0105czone. Ta operacja wy\u0142\u0105cza rejestrowanie w dzienniku, je\u015Bli zosta\u0142o ono w\u0142\u0105czone.

DiagnosticsEnableMetricsOperationDescription=Rozpocz\u0119cie gromadzenia metryk synchronizacji w czasie po\u0142\u0105czenia.

DiagnosticsDisableMetricsOperationDescription=Zatrzymanie gromadzenia metryk synchronizacji w czasie po\u0142\u0105czenia.

DiagnosticsShowMetricsOperationDescription=Pokazanie metryk synchronizacji zgromadzonych w czasie po\u0142\u0105czenia.

DiagnosticsClearMetricsOperationDescription=Czyszczenie metryk synchronizacji zgromadzonych w czasie po\u0142\u0105czenia.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja w\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia, je\u015Bli zosta\u0142a ona w\u0142\u0105czona.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja w\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanej nazwy dzier\u017Cawy.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanej nazwy dzier\u017Cawy, je\u015Bli diagnostyka zosta\u0142a w\u0142\u0105czona.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja w\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego, je\u015Bli diagnostyka zosta\u0142a w\u0142\u0105czona.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja w\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=Diagnostyka danych wra\u017Cliwych jest domy\u015Blnie wy\u0142\u0105czona. Ta operacja wy\u0142\u0105cza diagnostyk\u0119 danych wra\u017Cliwych, je\u015Bli zosta\u0142a ona w\u0142\u0105czona.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Aktualizacja poziomu diagnostyki z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia. Warto\u015Bci\u0105 argumentu mo\u017Ce by\u0107 nazwa poziomu albo liczba ca\u0142kowita. Na przyk\u0142ad: SEVERE albo 1000.

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Aktualizacja poziomu diagnostyki z u\u017Cyciem podanej nazwy dzier\u017Cawy. Warto\u015Bci\u0105 argumentu mo\u017Ce by\u0107 nazwa poziomu albo liczba ca\u0142kowita. Na przyk\u0142ad: SEVERE albo 1000.

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Aktualizacja poziomu diagnostyki z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego w dzienniku. Warto\u015Bci\u0105 argumentu mo\u017Ce by\u0107 nazwa poziomu albo liczba ca\u0142kowita. Na przyk\u0142ad: SEVERE albo 1000.

DiagnosticsUpdateDiagnosticLevelOperationDescription=Aktualizacja poziomu diagnostyki. Warto\u015Bci\u0105 argumentu mo\u017Ce by\u0107 nazwa poziomu albo liczba ca\u0142kowita. Na przyk\u0142ad: SEVERE albo 1000.

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Aktualizacja rozmiaru bufora diagnostyki "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanego ID prefiksu po\u0142\u0105czenia. Rozmiar domy\u015Blny bufora to 4000 rekord\u00F3w dziennika.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Aktualizacja rozmiaru bufora diagnostyki "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy dzier\u017Cawy. Rozmiar domy\u015Blny bufora to 4000 rekord\u00F3w dziennika.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Aktualizacja rozmiaru bufora diagnostyki "Diagnoza pierwszego niepowodzenia" z u\u017Cyciem podanej nazwy procesu rejestruj\u0105cego. Rozmiar domy\u015Blny bufora to 4000 rekord\u00F3w dziennika.

DiagnosticsUpdateBufferSizeOperationDescription=Aktualizacja rozmiaru bufora diagnostyki "Diagnoza pierwszego niepowodzenia". Rozmiar domy\u015Blny bufora to 4000 rekord\u00F3w dziennika.

DiagnosticsReadLoggingConfigFileOperationDescription=Ponowna inicjalizacja i ponowne wczytanie konfiguracji rejestrowania w dzienniku pochodz\u0105cej z okre\u015Blonego pliku, kt\u00F3ry powinien by\u0107 w formacie java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=Dodawanie kodu b\u0142\u0119du (w formacie ORA-XXXXX), kt\u00F3ry by\u0107 powinien by\u0107 obserwowany pod k\u0105tem nast\u0119pnego wyst\u0105pienia. Gdy wyst\u0105pi b\u0142\u0105d umieszczony na li\u015Bcie obserwowanych, sterownik JDBC zapisze diagnostyk\u0119 "Diagnoza pierwszego niepowodzenia" w skonfigurowanej procedurze obs\u0142ugi dziennik\u00F3w.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Usuwanie podanego kodu b\u0142\u0119du z listy obserwowanych. Kod b\u0142\u0119du powinien by\u0107 w formacie ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Pokazywanie obserwowanych kod\u00F3w b\u0142\u0119d\u00F3w.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurowanie listy obserwowanych z u\u017Cyciem domy\u015Blnych kod\u00F3w b\u0142\u0119d\u00F3w.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Zrzucanie diagnostyki "Diagnoza pierwszego niepowodzenia" do docelowej procedury obs\u0142ugi.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Zrzucanie diagnostyki "Diagnoza pierwszego niepowodzenia", gdy przysz\u0142e wyj\u0105tki b\u0119d\u0105 zawiera\u0107 jedno z podanych s\u0142\u00F3w kluczowych. Przyk\u0142adowe s\u0142owa kluczowe: reset, violation.

DiagnosticsShowExceptionKeywords=Wcze\u015Bniej dodane s\u0142owa kluczowe wyj\u0105tku.

DiagnosticsShowRecentOperations=Ostatnie operacje wykonane przez u\u017Cytkownika z tym obiektem MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Czyszczenie wcze\u015Bniej dodanych s\u0142\u00F3w kluczowych.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Domy\u015Blnie dzienniki s\u0105 zapisywane w procedurze obs\u0142ugi dziennik\u00F3w. Ta operacja mo\u017Ce w\u0142\u0105czy\u0107 lub wy\u0142\u0105czy\u0107 zapisywanie dziennik\u00F3w w diagnostyce "Diagnoza pierwszego niepowodzenia".

DiagnosticsConnectionIdPrefixParameterDescription=Prefiks ID po\u0142\u0105czenia.

DiagnosticsTenantNameParameterDescription=Nazwa dzier\u017Cawy

DiagnosticsLoggerNameParameterDescription=Nazwa procesu rejestruj\u0105cego.

DiagnosticsLevelParameterDescription=Poziom procesu rejestruj\u0105cego.

DiagnosticsBufferSizeParameterDescription=Rozmiar bufora diagnostyki "Diagnoza pierwszego niepowodzenia".

DiagnosticsConfigFileParameterDescription=Plik konfiguracji rejestrowania w dzienniku.

DiagnosticsErrorCodesParameterDescription=Kod b\u0142\u0119du w formacie ORA-XXXXX.

DiagnosticsEnabledParameterDescription=Warto\u015B\u0107 "true" lub "false"

DiagnosticsCommaSeparatedKeywordsParameterDescription=S\u0142owa kluczowe w postaci warto\u015Bci rozdzielonych przecinkiem.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






