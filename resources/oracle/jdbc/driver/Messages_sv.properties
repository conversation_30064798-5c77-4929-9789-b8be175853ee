#
# US English Error messages for JDBC
#
# Note:
# - Error codes are defined in DatabaseError.java.
#
# Message Guidelines:
# (The existing messages are not consistent, but do follow this guideline
# when you are creating new ones, or changing old ones.)
#
# - Messages start in lower-cases (eg. "invalid data type").
# - Do not put signs in message.  This is bad: "-> NULL".
# - Use past tense (eg. "failed to convert data").
#

#
# Add all error messages above here
#
# Below are MBean descriptions. They do not follow the message guidelines.
# Description of oracle.jdbc.driver.OracleDiagnosabilityMBean operations.

DiagnosabilityMBeanDescription=Kontrollerar fels\u00F6kningsfunktionerna f\u00F6r Oracle JDBC-drivrutiner.

DiagnosabilityMBeanConstructor()=zero arg-konstruktorn f\u00F6r Oracle JDBC Diagnosability MBean

DiagnosabilityMBeanLoggingEnabledDescription=All Oracle JDBC-loggningskod kontrolleras med hj\u00E4lp av det h\u00E4r booleska attributet. Om det har v\u00E4rdet false produceras inga loggmeddelanden. Om det har v\u00E4rdet true kontrolleras loggmeddelanden av niv\u00E5er och filter i java.util.logging.

DiagnosabilityMBeanStateManageableDescription=Oracle JDBC-drivrutinerna kan inte startas eller stoppas. Returnerar alltid v\u00E4rdet false.

DiagnosabilityMBeanStatisticsProviderDescription=Oracle JDBC-drivrutinerna tillhandah\u00E5ller inte statistik via Diagnosability MBean.

DiagnosabilityMBeanTraceControllerDescription=Clio-sp\u00E5rningsstyrenhet.

DiagnosabilityMBeanSuspendDescription=Clio-uppeh\u00E5lls\u00E5tg\u00E4rd.

DiagnosabilityMBeanResumeDescription=Clio-\u00E5terupptagnings\u00E5tg\u00E4rd.

DiagnosabilityMBeanTraceDescription=Clio-sp\u00E5rning\u00E5tg\u00E4rd.

DiagnosabilityMBeanEnableContinousLoggingDescription=Aktiverar skyddad kontinuerlig filloggning. ServiceName- och UserName-filter \u00E4r till\u00E4mpliga om de anges. Som standard \u00E4r alternativet inaktivt.

DiagnosabilityMBeanDisableContinousLoggingDescription=Avaktiverar skyddad kontinuerlig filloggning. Som standard \u00E4r alternativet inaktivt.

DiagnosabilityMBeanEnableDiagnoseFirstFailureDescription=Aktiverar diagnos av f\u00F6rsta fel. ServiceName- och UserName-filter \u00E4r till\u00E4mpliga om de anges. Som standard \u00E4r alternativet aktivt.

DiagnosabilityMBeanDisableDiagnoseFirstFailureDescription=Avaktiverar diagnos av f\u00F6rsta fel. Som standard \u00E4r alternativet aktivt.

DiagnosabilityMBeanServiceNameFilterDescription=Om filtret ServiceName anges aktiverats minnesintern/kontinuerlig loggning bara f\u00F6r den konfigurerade tj\u00E4nsten.

DiagnosabilityMBeanUserFilterDescription=Om filtret User anges aktiverats minnesintern/kontinuerlig loggning bara f\u00F6r den konfigurerade anv\u00E4ndaren.

ReplayStatisticsMBeanDescription=Exponerar statistiken f\u00F6r funktionen f\u00F6r applikationskontinuitet (AC).

ReplayStatisticsMBeanConstructor=zero arg-konstruktorn f\u00F6r MBean Oracle JDBC AC-statistik

ReplayStatisticsMBeanAllStatisticsDescription=H\u00E4mtar AC-statistik f\u00F6r alla k\u00E4nda instanser av drivrutinsdatak\u00E4llor.

ReplayStatisticsMBeanGetDSStatisticsDescription=H\u00E4mtar AC-statistik f\u00F6r en viss instans av drivrutinsdatak\u00E4lla.

# Description of oracle.jdbc.diagnostics.OracleDiagnosticsMXBean operations.

DiagnosticsMBeanDescription=Kontrollerar fels\u00F6kningsfunktionerna f\u00F6r Oracle JDBC-drivrutiner.

DiagnosticsMBeanLoggingEnabledAttributeDescription=All Oracle JDBC-loggningskoden kontrolleras med hj\u00E4lp av det h\u00E4r booleska attributet. Standardv\u00E4rdet \u00E4r "false". Om det \u00E4r aktiverat kontrolleras loggmeddelanden av niv\u00E5er och filter i java.util.logging. Om det avaktiveras produceras inga loggmeddelanden.

DiagnosticsMBeanMetricsEnabledAttributeDescription=Alla m\u00E4tetal f\u00F6r h\u00E4ndelser f\u00E5ngade av JDBC-drivrutinen kontrolleras med det h\u00E4r booleska attributet. Standardv\u00E4rdet \u00E4r "false". Om alternativet \u00E4r aktiverat f\u00E5ngas m\u00E4tetalen f\u00F6r h\u00E4ndelser av drivrutinen tills den avaktiveras.

DiagnosticsMBeanWriteLogsToTraceAttributeDescription=Returnerar "true" om skrivning av loggar till minnesintern sp\u00E5rningsbuffert \u00E4r aktiverat. Standardv\u00E4rdet \u00E4r "false".

DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar diagnos av f\u00F6rsta fel per angivet prefix f\u00F6r anslutnings-id om det har avaktiverats.

DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar diagnos av f\u00F6rsta fel per angivet prefix f\u00F6r anslutnings-id.

DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar diagnos av f\u00F6rsta fel per angivet klientnamn om det har avaktiverats.

DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar diagnos av f\u00F6rsta fel per angivet klientnamn.

DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar diagnos av f\u00F6rsta fel per angivet loggningsnamn om det har avaktiverats.

DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar diagnos av f\u00F6rsta fel per angivet loggningsnamn.

DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar diagnos av f\u00F6rsta fel om det har avaktiverats.

DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription=Diagnos av f\u00F6rsta fel \u00E4r aktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar diagnos av f\u00F6rsta fel.

DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar loggning per angivet prefix f\u00F6r anslutnings-id.

DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar loggning per angivet prefix f\u00F6r anslutnings-id om det har aktiverats.

DiagnosticsEnableLoggingByTenantNameOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar loggning per angivet tenantnamn.

DiagnosticsDisableLoggingByTenantNameOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar loggning per angivet tenantnamn om det har aktiverats.

DiagnosticsEnableLoggingByLoggerNameOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar loggning per angivet loggningsnamn.

DiagnosticsDisableLoggingByLoggerNameOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar loggning per angivet loggningsnamn om det har aktiverats.

DiagnosticsEnableLoggingOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar loggning.

DiagnosticsDisableLoggingOperationDescription=Loggning \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar loggning om det har aktiverats.

DiagnosticsEnableMetricsOperationDescription=Starta insamling av tidsm\u00E4tetal under anslutningstiden.

DiagnosticsDisableMetricsOperationDescription=Stoppa insamling av tidsm\u00E4tetal under anslutningstiden.

DiagnosticsShowMetricsOperationDescription=Visa tidsm\u00E4tetalen insamlade under anslutningstiden.

DiagnosticsClearMetricsOperationDescription=Rensa tidsm\u00E4tetalen insamlade under anslutningstiden.

DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar k\u00E4nslig diagnostik per angivet prefix f\u00F6r anslutnings-id.

DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar k\u00E4nslig diagnostik per angivet prefix f\u00F6r anslutnings-id om det har aktiverats.

DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar k\u00E4nslig diagnostik per angivet tenantnamn.

DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar k\u00E4nslig diagnostik per angivet tenantnamn om det har aktiverats.

DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden aktiverar k\u00E4nslig diagnostik per angivet loggningsnamn.

DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar k\u00E4nslig diagnostik per angivet loggningsnamn om det har aktiverats.

DiagnosticsEnableSensitiveDiagnosticsOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar k\u00E4nslig diagnostik.

DiagnosticsDisableSensitiveDiagnosticsOperationDescription=K\u00E4nslig diagnostik \u00E4r avaktiverat som standard. Den h\u00E4r \u00E5tg\u00E4rden avaktiverar k\u00E4nslig diagnostik om det har aktiverats.

DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription=Uppdatera diagnostikniv\u00E5n per angivet prefix f\u00F6r anslutnings-id. Argumentstr\u00E4ngen best\u00E5r antingen av ett niv\u00E5namn eller ett heltalsv\u00E4rde. Till exempel "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription=Uppdatera diagnostikniv\u00E5n per angivet klientnamn. Argumentstr\u00E4ngen best\u00E5r antingen av ett niv\u00E5namn eller ett heltalsv\u00E4rde. Till exempel "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription=Uppdatera diagnostikniv\u00E5n per angivet loggningsnamn. Argumentstr\u00E4ngen best\u00E5r antingen av ett niv\u00E5namn eller ett heltalsv\u00E4rde. Till exempel "SEVERE" eller "1000".

DiagnosticsUpdateDiagnosticLevelOperationDescription=Uppdatera diagnostikniv\u00E5n. Argumentstr\u00E4ngen best\u00E5r antingen av ett niv\u00E5namn eller ett heltalsv\u00E4rde. Till exempel "SEVERE" eller "1000".

DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription=Uppdatera storleken p\u00E5 diagnostikbuffert f\u00F6r diagnos av f\u00F6rsta fel per angivet prefix f\u00F6r anslutnings-id. Standardbuffertstorleken \u00E4r 4000 loggposter.

DiagnosticsUpdateBufferSizeByTenantNameOperationDescription=Uppdatera storleken p\u00E5 diagnostikbuffert f\u00F6r diagnos av f\u00F6rsta fel per angivet klientnamn. Standardbuffertstorleken \u00E4r 4000 loggposter.

DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription=Uppdatera storleken p\u00E5 diagnostikbuffert f\u00F6r diagnos av f\u00F6rsta fel per angivet loggningsnamn. Standardbuffertstorleken \u00E4r 4000 loggposter.

DiagnosticsUpdateBufferSizeOperationDescription=Uppdatera storleken p\u00E5 diagnostikbuffert f\u00F6r diagnos av f\u00F6rsta fel. Standardbuffertstorleken \u00E4r 4000 loggposter.

DiagnosticsReadLoggingConfigFileOperationDescription=Initiera om loggningsegenskaperna och l\u00E4s om loggningskonfigurationen fr\u00E5n den angivna filen, vilken ska ha formatet java.util.Properties.

DiagnosticsAddErrorCodeToWatchListOperationDescription=L\u00E4gg till de felkoder som ska bevakas f\u00F6r n\u00E4sta f\u00F6rekomst i formatet ORA-XXXXX. JDBC-drivrutinen skriver diagnostik f\u00F6r diagnos av f\u00F6rsta fel i den konfigurerade logghanteraren n\u00E4r ett fel i bevakningslistan intr\u00E4ffar.

DiagnosticsRemoveErrorCodeFromWatchListOperationDescription=Ta bort den angivna felkoden fr\u00E5n bevakningslistan. Felkoden ska vara i formatet ORA-XXXXX.

DiagnosticsShowErrorCodesWatchListOperationDescription=Visa de felkoder som bevakas.

DiagnosticsResetErrorCodesWatchListOperationDescription=Konfigurera bevakningslista med standardfelkoder.

DiagnosticsDumpDiagnoseFirstFailureOperationDescription=Dumpa diagnostik f\u00F6r diagnos av f\u00F6rsta fel i m\u00E5lhanteraren.

DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription=Dumpa diagnostik f\u00F6r diagnos av f\u00F6rsta fel n\u00E4r den kommande avvikelsen inneh\u00E5ller ett av de angivna nyckelorden. Exempelnyckelorden \u00E4r \u00E5terst\u00E4llda, \u00F6vertr\u00E4delse.

DiagnosticsShowExceptionKeywords=Tidigare tillagda undantagsnyckelord.

DiagnosticsShowRecentOperations=De senaste \u00E5tg\u00E4rderna som utf\u00F6rts av anv\u00E4ndaren p\u00E5 denna MBean.

DiagnosticsClearExceptionKeywordsOperationDescription=Rensa de nyss tillagda nyckelorden.

DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription=Loggar skrivs som standard till logghanteraren. Den h\u00E4r \u00E5tg\u00E4rden aktiverar eller avaktiverar skrivning av loggar till diagnostik f\u00F6r diagnos av f\u00F6rsta fel.

DiagnosticsConnectionIdPrefixParameterDescription=Anslutnings-id:ts prefix.

DiagnosticsTenantNameParameterDescription=Tenantnamnet.

DiagnosticsLoggerNameParameterDescription=Loggningsnamnet.

DiagnosticsLevelParameterDescription=Loggningsniv\u00E5n.

DiagnosticsBufferSizeParameterDescription=Buffertstorleken f\u00F6r diagnostik f\u00F6r diagnos av f\u00F6rsta fel.

DiagnosticsConfigFileParameterDescription=Loggningskonfigurationsfilen.

DiagnosticsErrorCodesParameterDescription=En felkod i formatet ORA-XXXXX.

DiagnosticsEnabledParameterDescription=V\u00E4rdet "true" eller "false".

DiagnosticsCommaSeparatedKeywordsParameterDescription=Nyckelordet som kommaavgr\u00E4nsade v\u00E4rden.

#     ^     ^     ^     ^
#     |     |     |     |        P L E A S E    R E A D
#
# Add new message above this comment.
# Before you add a new message, please read "Message Guideline" at the
# top of this file first.
#






