# This properties file sets the default value for connection properties.
# Entries in this file override the predefined defaults as specified
# in the JavaDoc for oracle.jdbc.OracleConnection. These defaults are
# themselves overridden by any values set via -D which are overridden
# by values passed in the Properties argument to getConnection.
#
# Controls whether getObject returns the Java standard type java.sql.SQLXML
# or the Oracle proprietary type oracle.xdb.XMLType.
#oracle.jdbc.getObjectReturnsXMLType=false
