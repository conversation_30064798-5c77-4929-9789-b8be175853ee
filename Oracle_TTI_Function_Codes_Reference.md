# Oracle TTI函数码详细对照表

## 基于源码分析的完整函数码列表

本文档基于Oracle JDBC驱动源码（T4CTTIfunCodes.java）提供完整的TTI函数码对照表和消息格式定义。

## 核心SQL操作函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x02 | OOPEN | 2 | 打开游标 | 基本游标操作格式 |
| 0x05 | OFETCH | 5 | 获取数据行 | 游标ID + 行数 |
| 0x08 | OCLOSE | 8 | 关闭游标 | 游标ID |
| 0x09 | OLOGOFF | 9 | 登出会话 | 无参数 |
| 0x0C | OCOMON | 12 | 开启提交模式 | 无参数 |
| 0x0D | OCOMOFF | 13 | 关闭提交模式 | 无参数 |
| 0x0E | OCOMMIT | 14 | 提交事务 | 无参数 |
| 0x0F | OROLLBACK | 15 | 回滚事务 | 无参数 |
| 0x14 | OCANCEL | 20 | 取消操作 | 无参数 |

## 版本和协议函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x30 | OV6STRT | 48 | V6启动操作 | 启动参数 |
| 0x31 | OV6STOP | 49 | V6停止操作 | 停止参数 |
| 0x3B | OVERSION | 59 | 版本信息获取 | 缓冲区指针+长度+格式标志 |

## 高级SQL操作函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x2B | ODSCRARR | 43 | 描述数组 | 数组描述信息 |
| 0x43 | OK2RPC | 67 | K2远程过程调用 | K2RPC类型+命令+参数 |
| 0x47 | OALL7 | 71 | Oracle 7复合操作 | 复合操作参数 |
| 0x4A | OSQL7 | 74 | Oracle 7 SQL执行 | SQL语句+参数 |
| 0x4E | OEXFEN | 78 | 执行操作结束 | 游标ID+执行标志 |
| 0x5C | OKOD | 92 | 内核操作描述符 | 操作描述符 |
| 0x5E | OALL8 | 94 | Oracle 8复合操作 | 详见OALL8格式 |
| 0x62 | ODNY | 98 | 动态SQL操作 | 动态SQL参数 |
| 0x77 | ODSY | 119 | SQL语句描述 | 对象名+类型+标志 |

## 事务和会话管理函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x67 | OTXSE | 103 | 事务开始 | 事务参数 |
| 0x68 | OTXEN | 104 | 事务结束 | 事务ID+标志 |
| 0x69 | OCCA | 105 | 取消分析 | 分析参数 |
| 0x6B | O80SES | 107 | Oracle 8会话操作 | 会话ID+操作码 |
| 0x73 | OAUTH | 115 | 认证操作 | 认证参数 |
| 0x76 | OSESSKEY | 118 | 会话密钥 | 密钥数据 |

## 队列和消息函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x79 | OAQEQ | 121 | AQ入队操作 | 队列参数+消息 |
| 0x7A | OAQDQ | 122 | AQ出队操作 | 队列参数 |
| 0x7E | OAQLS | 126 | AQ列表操作 | 列表参数 |
| 0x84 | OAQGPS | 132 | AQ获取属性 | 属性参数 |
| 0x91 | OAQXQ | 145 | AQ扩展队列 | 扩展参数 |

## 高级功能函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x78 | OCANA | 120 | 取消分析 | 分析ID |
| 0x7D | OKPN | 125 | 内核参数 | 参数名+值 |
| 0x7F | OOTCM | 127 | OTC管理 | 管理参数 |
| 0x87 | OSCID | 135 | 会话ID操作 | 会话参数 |
| 0x8A | OSPFPPUT | 138 | SPF参数设置 | 参数数据 |
| 0x8B | OKPFC | 139 | 内核参数获取 | 参数名 |
| 0x93 | OPING | 147 | Ping操作 | 无参数 |
| 0x9A | OKEYVAL | 154 | 键值对操作 | 键值数据 |

## XS安全相关函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x9B | OXSSCS | 155 | XS安全上下文 | 上下文数据 |
| 0x9C | OXSSRO | 156 | XS安全角色 | 角色数据 |
| 0x9D | OXSSPO | 157 | XS安全策略 | 策略数据 |
| 0xAC | OXSNSO | 172 | XS命名空间操作 | 命名空间数据 |
| 0xB2 | OXSNS | 178 | XS命名空间 | 命名空间参数 |
| 0xB0 | OXSSYNC | 176 | XS同步 | 同步参数 |
| 0xB4 | OXSATT | 180 | XS属性 | 属性数据 |
| 0xB3 | OXSCRE | 179 | XS创建 | 创建参数 |
| 0xB5 | OXSDET | 181 | XS分离 | 分离参数 |
| 0xB6 | OXSDES | 182 | XS销毁 | 销毁参数 |
| 0xB7 | OXSSET | 183 | XS设置 | 设置参数 |

## 会话管理扩展函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0xA2 | OSESSGET | 162 | 获取会话 | 会话参数 |
| 0xA3 | OSESSRLS | 163 | 释放会话 | 会话ID |
| 0xA4 | OSSTEMPLATE | 164 | 会话模板 | 模板数据 |
| 0xA7 | OQCSTA | 167 | 查询缓存状态 | 状态参数 |
| 0xA8 | OQCID | 168 | 查询缓存ID | 缓存ID |
| 0xB0 | OSESSSTATE | 176 | 会话状态 | 状态数据 |
| 0xB1 | OAPPCONTREPLAY | 177 | 应用连续重放 | 重放参数 |

## AQ扩展函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0xB8 | OAQENQ | 184 | AQ入队扩展 | 扩展入队参数 |
| 0xB9 | OAQDEQ | 185 | AQ出队扩展 | 扩展出队参数 |
| 0xBA | OAQEMNDEQ | 186 | AQ紧急出队 | 紧急参数 |
| 0xBB | OAQNFY | 187 | AQ通知 | 通知参数 |

## 特殊功能函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0xBE | OCHUNKINFO | 190 | 块信息 | 块参数 |
| 0xBF | OCLFEATURES | 191 | 客户端特性 | 特性数据 |
| 0xC3 | OSAGA | 195 | Saga操作 | Saga参数 |

## 直接路径操作函数码

| 函数码 | 常量名 | 十进制值 | 功能描述 | TTI消息格式 |
|--------|--------|----------|----------|-------------|
| 0x80 | ODPP | 128 | 直接路径准备 | 路径参数 |
| 0x81 | ODPLS | 129 | 直接路径加载开始 | 加载参数 |
| 0x82 | ODPMOP | 130 | 直接路径多操作 | 操作数组 |
| 0xC7 | OPLBGN | 199 | 操作日志开始 | 日志参数 |
| 0xC8 | OPLEND | 200 | 操作日志结束 | 日志ID |
| 0xCB | OPLOPN | 203 | 操作日志打开 | 打开参数 |

## OALL8函数码详细消息格式

OALL8（函数码94）是最重要的复合操作函数，支持多种子操作：

### 操作标志位（Options字段）：

```
#define UOPF_PRS    0x00000001  // 解析SQL语句
#define UOPF_BND    0x00000008  // 绑定变量
#define UOPF_DFN    0x00000010  // 定义输出列
#define UOPF_EXE    0x00000020  // 执行SQL
#define UOPF_FCH    0x00000040  // 获取结果
#define UOPF_CAN    0x00000080  // 取消操作
#define UOPF_COM    0x00000100  // 提交事务
#define UOPF_FEX    0x00000200  // 获取并执行
#define UOPF_SIO    0x00000400  // 流I/O
#define UOPF_NPL    0x00008000  // 非PL/SQL
#define UOPF_DSY    0x00020000  // 描述操作
#define UOPF_NCF    0x00040000  // 无游标获取
#define UOPF_BER    0x00080000  // 批量错误报告
```

### OALL8消息体结构：

```
Offset | Size | Field Name        | Description
-------|------|-------------------|------------------
0x00   | 4    | options          | 操作选项位掩码
0x04   | 4    | cursor           | 游标ID
0x08   | 1    | sql_ptr          | SQL语句指针标志
0x09   | 4    | sql_length       | SQL语句长度
0x0D   | 1    | al8i4_ptr        | AL8I4数组指针标志
0x0E   | 4    | al8i4_length     | AL8I4数组长度
0x12   | 1    | reserved1        | 保留字段1
0x13   | 1    | reserved2        | 保留字段2
0x14   | 4    | max_rows         | 最大行数
0x18   | 4    | rows_to_fetch    | 要获取的行数
0x1C   | 4    | max_long_size    | 最大LONG大小
0x20   | 1    | bind_ptr         | 绑定定义指针标志
0x21   | 4    | bind_count       | 绑定变量数量
0x25   | 1    | define_ptr       | 定义列指针标志（TTC>=2）
0x26   | 4    | define_count     | 定义列数量（TTC>=2）
...    | ...  | ...              | 其他版本相关字段
```

## 消息编码示例

### OVERSION消息示例：
```
03 3B 01 01 00 01 00 01 01 00 00 00 01
|  |  |  |     |     |  |  |        |
|  |  |  |     |     |  |  |        +-- Banner Format
|  |  |  |     |     |  |  +-- Return Version Pointer
|  |  |  |     |     |  +-- Return Length Pointer
|  |  |  |     |     +-- Buffer Length (256)
|  |  |  |     +-- Buffer Pointer
|  |  |  +-- Sequence Number
|  |  +-- Function Code (59)
|  +-- TTC Code (3)
+-- Message Start
```

### OALL8解析+执行消息示例：
```
03 5E 02 21 00 00 00 01 00 00 00 01 00 00 00 0D 01 ...
|  |  |  |           |           |           |  |
|  |  |  |           |           |           |  +-- AL8I4 Array Pointer
|  |  |  |           |           |           +-- SQL Length
|  |  |  |           |           +-- Cursor ID
|  |  |  |           +-- Options (PARSE | EXECUTE = 0x21)
|  |  |  +-- Sequence Number
|  |  +-- Function Code (94)
|  +-- TTC Code (3)
+-- Message Start
```

## 总结

本对照表基于Oracle JDBC驱动源码分析，提供了完整的TTI函数码定义和消息格式。这些信息对于：

1. **协议分析**：理解Oracle客户端-服务器通信
2. **性能调优**：识别不同操作的开销
3. **安全审计**：监控数据库访问模式
4. **故障排除**：分析网络通信问题

具有重要的参考价值。
