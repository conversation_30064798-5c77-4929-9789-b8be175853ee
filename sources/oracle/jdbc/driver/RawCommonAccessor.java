package oracle.jdbc.driver;

import java.io.ByteArrayInputStream;
import java.io.CharArrayReader;
import java.io.InputStream;
import java.io.Reader;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.util.RepConversion;
import oracle.sql.Datum;
import oracle.sql.RAW;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RawCommonAccessor.class */
class RawCommonAccessor extends Accessor {
    RawCommonAccessor(OracleStatement _statement, int _representationMaxLength, boolean isStoredInBindData) {
        super(Representation.RAW, _statement, _representationMaxLength, isStoredInBindData);
    }

    void init(OracleStatement stmt, int internal_type, int database_type, int max_len, short form, int external_type) throws SQLException {
        init(stmt, internal_type, database_type, form, false);
        initForDataAccess(external_type, max_len, null);
    }

    void init(OracleStatement stmt, int internal_type, int database_type, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        init(stmt, internal_type, database_type, form, false);
        initForDescribe(internal_type, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        int max_field_size = stmt.maxFieldSize;
        if (max_field_size > 0 && (max_len == 0 || max_field_size < max_len)) {
            max_len = max_field_size;
        }
        initForDataAccess(0, max_len, null);
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        byte[] b_array = getBytes(currentRow);
        if (b_array == null) {
            return null;
        }
        int len = b_array.length;
        if (len == 0) {
            return null;
        }
        return RepConversion.bArray2String(b_array);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    InputStream getAsciiStream(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        PhysicalConnection conn = this.statement.connection;
        return conn.conversion.ConvertStream(new ByteArrayInputStream(b), 2, conn);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    InputStream getUnicodeStream(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        PhysicalConnection conn = this.statement.connection;
        return conn.conversion.ConvertStream(new ByteArrayInputStream(b), 3, conn);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Reader getCharacterStream(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        return byteArrayToReader(b);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    InputStream getBinaryStream(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        return new ByteArrayInputStream(b);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return getBytes(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
        return getBytes(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return getRAW(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    RAW getRAW(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        return new RAW(b);
    }

    static Reader byteArrayToReader(byte[] byteArray) {
        int rlen = byteArray.length;
        char[] charBuf = new char[rlen << 1];
        int chars_read = DBConversion.RAWBytesToHexChars(byteArray, rlen, charBuf);
        return new CharArrayReader(charBuf, 0, chars_read);
    }
}
