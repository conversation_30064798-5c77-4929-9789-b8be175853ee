package oracle.jdbc.driver;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RawBinder.class */
class RawBinder extends DatumBinder {
    Binder theRawCopyingBinder;

    static void init(Binder x) {
        x.type = (short) 23;
    }

    RawBinder(byte[] val) {
        super(val);
        this.theRawCopyingBinder = null;
        init(this);
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theRawCopyingBinder == null) {
            this.theRawCopyingBinder = new RawCopyingBinder();
        }
        return this.theRawCopyingBinder;
    }
}
