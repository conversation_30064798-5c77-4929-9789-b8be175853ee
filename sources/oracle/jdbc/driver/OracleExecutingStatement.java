package oracle.jdbc.driver;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.Date;
import java.sql.NClob;
import java.sql.ParameterMetaData;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Statement;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.dcn.DatabaseChangeRegistration;
import oracle.jdbc.internal.OracleStatement;
import oracle.sql.ARRAY;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.CHAR;
import oracle.sql.CLOB;
import oracle.sql.CustomDatum;
import oracle.sql.CustomDatumFactory;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.NUMBER;
import oracle.sql.OPAQUE;
import oracle.sql.ORAData;
import oracle.sql.ORADataFactory;
import oracle.sql.RAW;
import oracle.sql.REF;
import oracle.sql.ROWID;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/OracleExecutingStatement.class */
final class OracleExecutingStatement implements oracle.jdbc.internal.OracleCallableStatement {
    private final oracle.jdbc.internal.OracleStatement statement;
    private final CountDownLatch latch = new CountDownLatch(1);

    OracleExecutingStatement(oracle.jdbc.internal.OracleStatement statement) {
        this.statement = statement;
    }

    void endExecution() {
        this.latch.countDown();
    }

    void awaitExecution() {
        Pipeline pipeline;
        try {
            if (this.statement instanceof T4CStatement) {
                pipeline = ((T4CStatement) this.statement).t4Connection.pipeline();
            } else if (this.statement instanceof T4CPreparedStatement) {
                pipeline = ((T4CPreparedStatement) this.statement).t4Connection.pipeline();
            } else {
                pipeline = this.statement instanceof T4CCallableStatement ? ((T4CCallableStatement) this.statement).t4Connection.pipeline() : null;
            }
            Pipeline pipeline2 = pipeline;
            if (pipeline2 != null) {
                while (this.latch.getCount() != 0) {
                    pipeline2.await(100L, TimeUnit.MILLISECONDS);
                }
            }
            this.latch.await();
        } catch (InterruptedException interruptedException) {
            throw new RuntimeException(interruptedException);
        }
    }

    private oracle.jdbc.internal.OraclePreparedStatement unwrapPreparedStatement() {
        try {
            return (oracle.jdbc.internal.OraclePreparedStatement) this.statement.unwrap(oracle.jdbc.internal.OraclePreparedStatement.class);
        } catch (SQLException sqlException) {
            throw new RuntimeException(sqlException);
        }
    }

    private oracle.jdbc.internal.OracleCallableStatement unwrapCallableStatement() {
        try {
            return (oracle.jdbc.internal.OracleCallableStatement) this.statement.unwrap(oracle.jdbc.internal.OracleCallableStatement.class);
        } catch (SQLException sqlException) {
            throw new RuntimeException(sqlException);
        }
    }

    @Override // java.sql.Statement
    public void cancel() throws SQLException {
        this.statement.cancel();
    }

    @Override // java.sql.Statement
    public boolean isClosed() throws SQLException {
        return this.statement.isClosed();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setFixedString(boolean fixedString) {
        awaitExecution();
        this.statement.setFixedString(fixedString);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public boolean getFixedString() {
        awaitExecution();
        return this.statement.getFixedString();
    }

    @Override // oracle.jdbc.OraclePreparedStatement, oracle.jdbc.internal.OracleStatement
    public int sendBatch() throws SQLException {
        awaitExecution();
        return this.statement.sendBatch();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public boolean getserverCursor() {
        awaitExecution();
        return this.statement.getserverCursor();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public int getcacheState() {
        awaitExecution();
        return this.statement.getcacheState();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public int getstatementType() {
        awaitExecution();
        return this.statement.getstatementType();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public OracleStatement.SqlKind getSqlKind() throws SQLException {
        awaitExecution();
        return this.statement.getSqlKind();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public long getChecksum() throws SQLException {
        awaitExecution();
        return this.statement.getChecksum();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void registerBindChecksumListener(OracleStatement.BindChecksumListener listener) throws SQLException {
        awaitExecution();
        this.statement.registerBindChecksumListener(listener);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setSnapshotSCN(long scn) throws SQLException {
        awaitExecution();
        this.statement.setSnapshotSCN(scn);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public long getQueryId() throws SQLException {
        awaitExecution();
        return this.statement.getQueryId();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public byte[] getCompileKey() throws SQLException {
        awaitExecution();
        return this.statement.getCompileKey();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setShardingKeyRpnTokens(byte[] shardingKeyRpnTokens) throws SQLException {
        awaitExecution();
        this.statement.setShardingKeyRpnTokens(shardingKeyRpnTokens);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public byte[] getShardingKeyRpnTokens() throws SQLException {
        awaitExecution();
        return this.statement.getShardingKeyRpnTokens();
    }

    @Override // oracle.jdbc.OracleStatement
    public void clearDefines() throws SQLException {
        awaitExecution();
        this.statement.clearDefines();
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnType(int columnIndex, int type) throws SQLException {
        awaitExecution();
        this.statement.defineColumnType(columnIndex, type);
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnType(int columnIndex, int type, int lobPrefetchSize) throws SQLException {
        awaitExecution();
        this.statement.defineColumnType(columnIndex, type, lobPrefetchSize);
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnType(int columnIndex, int type, int lobPrefetchSize, short formOfUse) throws SQLException {
        awaitExecution();
        this.statement.defineColumnType(columnIndex, type, lobPrefetchSize, formOfUse);
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnTypeBytes(int columnIndex, int type, int lobPrefetchSize) throws SQLException {
        awaitExecution();
        this.statement.defineColumnTypeBytes(columnIndex, type, lobPrefetchSize);
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnTypeChars(int columnIndex, int type, int lobPrefetchSize) throws SQLException {
        awaitExecution();
        this.statement.defineColumnTypeChars(columnIndex, type, lobPrefetchSize);
    }

    @Override // oracle.jdbc.OracleStatement
    public void defineColumnType(int columnIndex, int typeCode, String typeName) throws SQLException {
        awaitExecution();
        this.statement.defineColumnType(columnIndex, typeCode, typeName);
    }

    @Override // oracle.jdbc.OracleStatement
    public int getRowPrefetch() {
        awaitExecution();
        return this.statement.getRowPrefetch();
    }

    @Override // oracle.jdbc.OracleStatement
    public void setRowPrefetch(int value) throws SQLException {
        awaitExecution();
        this.statement.setRowPrefetch(value);
    }

    @Override // oracle.jdbc.OracleStatement
    public int getLobPrefetchSize() throws SQLException {
        awaitExecution();
        return this.statement.getLobPrefetchSize();
    }

    @Override // oracle.jdbc.OracleStatement
    public void setLobPrefetchSize(int value) throws SQLException {
        awaitExecution();
        this.statement.setLobPrefetchSize(value);
    }

    @Override // oracle.jdbc.OracleStatement
    public void closeWithKey(String key) throws SQLException {
        awaitExecution();
        this.statement.closeWithKey(key);
    }

    @Override // oracle.jdbc.OracleStatement
    public int creationState() {
        awaitExecution();
        return this.statement.creationState();
    }

    @Override // oracle.jdbc.OracleStatement
    public boolean isNCHAR(int index) throws SQLException {
        awaitExecution();
        return this.statement.isNCHAR(index);
    }

    @Override // oracle.jdbc.OracleStatement
    public void setDatabaseChangeRegistration(DatabaseChangeRegistration registration) throws SQLException {
        awaitExecution();
        this.statement.setDatabaseChangeRegistration(registration);
    }

    @Override // oracle.jdbc.OracleStatement
    public String[] getRegisteredTableNames() throws SQLException {
        awaitExecution();
        return this.statement.getRegisteredTableNames();
    }

    @Override // oracle.jdbc.OracleStatement
    public long getRegisteredQueryId() throws SQLException {
        awaitExecution();
        return this.statement.getRegisteredQueryId();
    }

    @Override // oracle.jdbc.OracleStatement
    public void closeOnCompletion() throws SQLException {
        awaitExecution();
        this.statement.closeOnCompletion();
    }

    @Override // oracle.jdbc.OracleStatement
    public String enquoteLiteral(String val) throws SQLException {
        awaitExecution();
        return this.statement.enquoteLiteral(val);
    }

    @Override // oracle.jdbc.OracleStatement
    public String enquoteNCharLiteral(String val) throws SQLException {
        awaitExecution();
        return this.statement.enquoteNCharLiteral(val);
    }

    @Override // oracle.jdbc.OracleStatement
    public boolean isSimpleIdentifier(String identifier) throws SQLException {
        awaitExecution();
        return this.statement.isSimpleIdentifier(identifier);
    }

    @Override // oracle.jdbc.OracleStatement
    public String enquoteIdentifier(String identifier, boolean alwaysQuote) throws SQLException {
        awaitExecution();
        return this.statement.enquoteIdentifier(identifier, alwaysQuote);
    }

    @Override // java.sql.Statement
    public ResultSet executeQuery(String sql) throws SQLException {
        awaitExecution();
        return this.statement.executeQuery(sql);
    }

    @Override // java.sql.Statement
    public int executeUpdate(String sql) throws SQLException {
        awaitExecution();
        return this.statement.executeUpdate(sql);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public int executeUpdateAndSuspend() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().executeUpdateAndSuspend();
    }

    @Override // java.sql.Statement, java.lang.AutoCloseable
    public void close() throws SQLException {
        awaitExecution();
        this.statement.close();
    }

    @Override // java.sql.Statement
    public int getMaxFieldSize() throws SQLException {
        awaitExecution();
        return this.statement.getMaxFieldSize();
    }

    @Override // java.sql.Statement
    public void setMaxFieldSize(int max) throws SQLException {
        awaitExecution();
        this.statement.setMaxFieldSize(max);
    }

    @Override // java.sql.Statement
    public int getMaxRows() throws SQLException {
        awaitExecution();
        return this.statement.getMaxRows();
    }

    @Override // java.sql.Statement
    public void setMaxRows(int max) throws SQLException {
        awaitExecution();
        this.statement.setMaxRows(max);
    }

    @Override // java.sql.Statement, oracle.jdbc.OracleStatement
    public void setEscapeProcessing(boolean enable) throws SQLException {
        awaitExecution();
        this.statement.setEscapeProcessing(enable);
    }

    @Override // java.sql.Statement
    public int getQueryTimeout() throws SQLException {
        awaitExecution();
        return this.statement.getQueryTimeout();
    }

    @Override // java.sql.Statement
    public void setQueryTimeout(int seconds) throws SQLException {
        awaitExecution();
        this.statement.setQueryTimeout(seconds);
    }

    @Override // java.sql.Statement
    public SQLWarning getWarnings() throws SQLException {
        awaitExecution();
        return this.statement.getWarnings();
    }

    @Override // java.sql.Statement
    public void clearWarnings() throws SQLException {
        awaitExecution();
        this.statement.clearWarnings();
    }

    @Override // java.sql.Statement
    public void setCursorName(String name) throws SQLException {
        awaitExecution();
        this.statement.setCursorName(name);
    }

    @Override // java.sql.Statement
    public boolean execute(String sql) throws SQLException {
        awaitExecution();
        return this.statement.execute(sql);
    }

    @Override // oracle.jdbc.OracleStatement
    public String getSqlId() throws SQLException {
        awaitExecution();
        return this.statement.getSqlId();
    }

    @Override // java.sql.Statement
    public ResultSet getResultSet() throws SQLException {
        awaitExecution();
        return this.statement.getResultSet();
    }

    @Override // java.sql.Statement
    public int getUpdateCount() throws SQLException {
        awaitExecution();
        return this.statement.getUpdateCount();
    }

    @Override // java.sql.Statement
    public boolean getMoreResults() throws SQLException {
        awaitExecution();
        return this.statement.getMoreResults();
    }

    @Override // java.sql.Statement
    public void setFetchDirection(int direction) throws SQLException {
        awaitExecution();
        this.statement.setFetchDirection(direction);
    }

    @Override // java.sql.Statement
    public int getFetchDirection() throws SQLException {
        awaitExecution();
        return this.statement.getFetchDirection();
    }

    @Override // java.sql.Statement
    public void setFetchSize(int rows) throws SQLException {
        awaitExecution();
        this.statement.setFetchSize(rows);
    }

    @Override // java.sql.Statement
    public int getFetchSize() throws SQLException {
        awaitExecution();
        return this.statement.getFetchSize();
    }

    @Override // java.sql.Statement
    public int getResultSetConcurrency() throws SQLException {
        awaitExecution();
        return this.statement.getResultSetConcurrency();
    }

    @Override // java.sql.Statement
    public int getResultSetType() throws SQLException {
        awaitExecution();
        return this.statement.getResultSetType();
    }

    @Override // java.sql.Statement
    public void addBatch(String sql) throws SQLException {
        awaitExecution();
        this.statement.addBatch(sql);
    }

    @Override // java.sql.Statement
    public void clearBatch() throws SQLException {
        awaitExecution();
        this.statement.clearBatch();
    }

    @Override // java.sql.Statement
    public int[] executeBatch() throws SQLException {
        awaitExecution();
        return this.statement.executeBatch();
    }

    @Override // java.sql.Statement
    public Connection getConnection() throws SQLException {
        awaitExecution();
        return this.statement.getConnection();
    }

    @Override // java.sql.Statement
    public boolean getMoreResults(int current) throws SQLException {
        awaitExecution();
        return this.statement.getMoreResults(current);
    }

    @Override // java.sql.Statement
    public ResultSet getGeneratedKeys() throws SQLException {
        awaitExecution();
        return this.statement.getGeneratedKeys();
    }

    @Override // java.sql.Statement
    public int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        awaitExecution();
        return this.statement.executeUpdate(sql, autoGeneratedKeys);
    }

    @Override // java.sql.Statement
    public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
        awaitExecution();
        return this.statement.executeUpdate(sql, columnIndexes);
    }

    @Override // java.sql.Statement
    public int executeUpdate(String sql, String[] columnNames) throws SQLException {
        awaitExecution();
        return this.statement.executeUpdate(sql, columnNames);
    }

    @Override // java.sql.Statement
    public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
        awaitExecution();
        return this.statement.execute(sql, autoGeneratedKeys);
    }

    @Override // java.sql.Statement
    public boolean execute(String sql, int[] columnIndexes) throws SQLException {
        awaitExecution();
        return this.statement.execute(sql, columnIndexes);
    }

    @Override // java.sql.Statement
    public boolean execute(String sql, String[] columnNames) throws SQLException {
        awaitExecution();
        return this.statement.execute(sql, columnNames);
    }

    @Override // java.sql.Statement
    public int getResultSetHoldability() throws SQLException {
        awaitExecution();
        return this.statement.getResultSetHoldability();
    }

    @Override // java.sql.Statement
    public void setPoolable(boolean poolable) throws SQLException {
        awaitExecution();
        this.statement.setPoolable(poolable);
    }

    @Override // java.sql.Statement
    public boolean isPoolable() throws SQLException {
        awaitExecution();
        return this.statement.isPoolable();
    }

    public boolean isCloseOnCompletion() throws SQLException {
        awaitExecution();
        return this.statement.isCloseOnCompletion();
    }

    public long getLargeUpdateCount() throws SQLException {
        awaitExecution();
        return this.statement.getLargeUpdateCount();
    }

    public void setLargeMaxRows(long max) throws SQLException {
        awaitExecution();
        this.statement.setLargeMaxRows(max);
    }

    public long getLargeMaxRows() throws SQLException {
        awaitExecution();
        return this.statement.getLargeMaxRows();
    }

    public long[] executeLargeBatch() throws SQLException {
        awaitExecution();
        return this.statement.executeLargeBatch();
    }

    public long executeLargeUpdate(String sql) throws SQLException {
        awaitExecution();
        return this.statement.executeLargeUpdate(sql);
    }

    public long executeLargeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        awaitExecution();
        return this.statement.executeLargeUpdate(sql, autoGeneratedKeys);
    }

    public long executeLargeUpdate(String sql, int[] columnIndexes) throws SQLException {
        awaitExecution();
        return this.statement.executeLargeUpdate(sql, columnIndexes);
    }

    public long executeLargeUpdate(String sql, String[] columnNames) throws SQLException {
        awaitExecution();
        return this.statement.executeLargeUpdate(sql, columnNames);
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> cls) throws SQLException {
        awaitExecution();
        return (T) this.statement.unwrap(cls);
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        awaitExecution();
        return this.statement.isWrapperFor(iface);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        awaitExecution();
        this.statement.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        awaitExecution();
        return this.statement.getACProxy();
    }

    @Override // java.sql.PreparedStatement
    public ResultSet executeQuery() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().executeQuery();
    }

    @Override // java.sql.PreparedStatement
    public int executeUpdate() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().executeUpdate();
    }

    @Override // java.sql.PreparedStatement
    public void setNull(int parameterIndex, int sqlType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNull(parameterIndex, sqlType);
    }

    @Override // java.sql.PreparedStatement
    public void setBoolean(int parameterIndex, boolean x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBoolean(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setByte(int parameterIndex, byte x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setByte(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setShort(int parameterIndex, short x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setShort(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setInt(int parameterIndex, int x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setInt(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setLong(int parameterIndex, long x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setLong(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setFloat(int parameterIndex, float x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setFloat(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setDouble(int parameterIndex, double x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDouble(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setBigDecimal(int parameterIndex, BigDecimal x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBigDecimal(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setString(int parameterIndex, String x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setString(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setBytes(int parameterIndex, byte[] x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBytes(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setDate(int parameterIndex, Date x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDate(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setTime(int parameterIndex, Time x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTime(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setTimestamp(int parameterIndex, Timestamp x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimestamp(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setAsciiStream(int parameterIndex, InputStream x, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStream(parameterIndex, x, length);
    }

    @Override // java.sql.PreparedStatement
    @Deprecated
    public void setUnicodeStream(int parameterIndex, InputStream x, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setUnicodeStream(parameterIndex, x, length);
    }

    @Override // java.sql.PreparedStatement
    public void setBinaryStream(int parameterIndex, InputStream x, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStream(parameterIndex, x, length);
    }

    @Override // java.sql.PreparedStatement
    public void clearParameters() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().clearParameters();
    }

    @Override // java.sql.PreparedStatement
    public void setObject(int parameterIndex, Object x, int targetSqlType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObject(parameterIndex, x, targetSqlType);
    }

    @Override // java.sql.PreparedStatement
    public void setObject(int parameterIndex, Object x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObject(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public boolean execute() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().execute();
    }

    @Override // java.sql.PreparedStatement
    public void addBatch() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().addBatch();
    }

    @Override // java.sql.PreparedStatement
    public void setCharacterStream(int parameterIndex, Reader reader, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStream(parameterIndex, reader, length);
    }

    @Override // java.sql.PreparedStatement
    public void setRef(int parameterIndex, Ref x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRef(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setBlob(int parameterIndex, Blob x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlob(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setClob(int parameterIndex, Clob x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClob(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setArray(int parameterIndex, Array x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setArray(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public ResultSetMetaData getMetaData() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().getMetaData();
    }

    @Override // java.sql.PreparedStatement
    public void setDate(int parameterIndex, Date x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDate(parameterIndex, x, cal);
    }

    @Override // java.sql.PreparedStatement
    public void setTime(int parameterIndex, Time x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTime(parameterIndex, x, cal);
    }

    @Override // java.sql.PreparedStatement
    public void setTimestamp(int parameterIndex, Timestamp x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimestamp(parameterIndex, x, cal);
    }

    @Override // java.sql.PreparedStatement
    public void setNull(int parameterIndex, int sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNull(parameterIndex, sqlType, typeName);
    }

    @Override // java.sql.PreparedStatement
    public void setURL(int parameterIndex, URL x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setURL(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public ParameterMetaData getParameterMetaData() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().getParameterMetaData();
    }

    @Override // java.sql.PreparedStatement
    public void setRowId(int parameterIndex, RowId x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRowId(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setNString(int parameterIndex, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNString(parameterIndex, value);
    }

    @Override // java.sql.PreparedStatement
    public void setNCharacterStream(int parameterIndex, Reader value, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNCharacterStream(parameterIndex, value, length);
    }

    @Override // java.sql.PreparedStatement
    public void setNClob(int parameterIndex, NClob value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClob(parameterIndex, value);
    }

    @Override // java.sql.PreparedStatement
    public void setClob(int parameterIndex, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClob(parameterIndex, reader, length);
    }

    @Override // java.sql.PreparedStatement
    public void setBlob(int parameterIndex, InputStream inputStream, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlob(parameterIndex, inputStream, length);
    }

    @Override // java.sql.PreparedStatement
    public void setNClob(int parameterIndex, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClob(parameterIndex, reader, length);
    }

    @Override // java.sql.PreparedStatement
    public void setSQLXML(int parameterIndex, SQLXML xmlObject) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setSQLXML(parameterIndex, xmlObject);
    }

    @Override // java.sql.PreparedStatement
    public void setObject(int parameterIndex, Object x, int targetSqlType, int scaleOrLength) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObject(parameterIndex, x, targetSqlType, scaleOrLength);
    }

    @Override // java.sql.PreparedStatement
    public void setAsciiStream(int parameterIndex, InputStream x, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStream(parameterIndex, x, length);
    }

    @Override // java.sql.PreparedStatement
    public void setBinaryStream(int parameterIndex, InputStream x, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStream(parameterIndex, x, length);
    }

    @Override // java.sql.PreparedStatement
    public void setCharacterStream(int parameterIndex, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStream(parameterIndex, reader, length);
    }

    @Override // java.sql.PreparedStatement
    public void setAsciiStream(int parameterIndex, InputStream x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStream(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setBinaryStream(int parameterIndex, InputStream x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStream(parameterIndex, x);
    }

    @Override // java.sql.PreparedStatement
    public void setCharacterStream(int parameterIndex, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStream(parameterIndex, reader);
    }

    @Override // java.sql.PreparedStatement
    public void setNCharacterStream(int parameterIndex, Reader value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNCharacterStream(parameterIndex, value);
    }

    @Override // java.sql.PreparedStatement
    public void setClob(int parameterIndex, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClob(parameterIndex, reader);
    }

    @Override // java.sql.PreparedStatement
    public void setBlob(int parameterIndex, InputStream inputStream) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlob(parameterIndex, inputStream);
    }

    @Override // java.sql.PreparedStatement
    public void setNClob(int parameterIndex, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClob(parameterIndex, reader);
    }

    public void setObject(int parameterIndex, Object x, SQLType targetSqlType, int scaleOrLength) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObject(parameterIndex, x, targetSqlType, scaleOrLength);
    }

    public void setObject(int parameterIndex, Object x, SQLType targetSqlType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObject(parameterIndex, x, targetSqlType);
    }

    public long executeLargeUpdate() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().executeLargeUpdate();
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void defineParameterTypeBytes(int param_index, int type, int max_size) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().defineParameterTypeBytes(param_index, type, max_size);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void defineParameterTypeChars(int param_index, int type, int max_size) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().defineParameterTypeChars(param_index, type, max_size);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void defineParameterType(int param_index, int type, int max_size) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().defineParameterType(param_index, type, max_size);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public int getExecuteBatch() {
        awaitExecution();
        return unwrapPreparedStatement().getExecuteBatch();
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setARRAY(int parameterIndex, ARRAY arr) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setARRAY(parameterIndex, arr);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBfile(int parameterIndex, BFILE file) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBfile(parameterIndex, file);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBFILE(int parameterIndex, BFILE file) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBFILE(parameterIndex, file);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBLOB(int parameterIndex, BLOB lob) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBLOB(parameterIndex, lob);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCHAR(int parameterIndex, CHAR ch) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCHAR(parameterIndex, ch);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCLOB(int parameterIndex, CLOB lob) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCLOB(parameterIndex, lob);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCursor(int parameterIndex, ResultSet rs) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCursor(parameterIndex, rs);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCustomDatum(int parameterIndex, CustomDatum x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCustomDatum(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setORAData(int parameterIndex, ORAData x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setORAData(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDATE(int parameterIndex, DATE date) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDATE(parameterIndex, date);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setExecuteBatch(int batchValue) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setExecuteBatch(batchValue);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setFixedCHAR(int parameterIndex, String x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setFixedCHAR(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNUMBER(int parameterIndex, NUMBER num) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNUMBER(parameterIndex, num);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryFloat(int parameterIndex, float value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryFloat(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryFloat(int parameterIndex, BINARY_FLOAT value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryFloat(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryDouble(int parameterIndex, double value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryDouble(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryDouble(int parameterIndex, BINARY_DOUBLE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryDouble(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setOPAQUE(int parameterIndex, OPAQUE val) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setOPAQUE(parameterIndex, val);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setOracleObject(int parameterIndex, Datum x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setOracleObject(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setStructDescriptor(int parameterIndex, StructDescriptor desc) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setStructDescriptor(parameterIndex, desc);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRAW(int parameterIndex, RAW raw) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRAW(parameterIndex, raw);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setREF(int parameterIndex, REF ref) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setREF(parameterIndex, ref);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRefType(int parameterIndex, REF ref) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRefType(parameterIndex, ref);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setROWID(int parameterIndex, ROWID rowid) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setROWID(parameterIndex, rowid);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setSTRUCT(int parameterIndex, STRUCT struct) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setSTRUCT(parameterIndex, struct);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMP(int parameterIndex, TIMESTAMP x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMP(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMPTZ(int parameterIndex, TIMESTAMPTZ x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMPTZ(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMPLTZ(int parameterIndex, TIMESTAMPLTZ x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMPLTZ(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setINTERVALYM(int parameterIndex, INTERVALYM x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setINTERVALYM(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setINTERVALDS(int parameterIndex, INTERVALDS x) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setINTERVALDS(parameterIndex, x);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNullAtName(String parameterName, int sqlType, String sqlName) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNullAtName(parameterName, sqlType, sqlName);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNullAtName(String parameterName, int sqlType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNullAtName(parameterName, sqlType);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBooleanAtName(String parameterName, boolean value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBooleanAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setByteAtName(String parameterName, byte value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setByteAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setShortAtName(String parameterName, short value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setShortAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setIntAtName(String parameterName, int value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setIntAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setLongAtName(String parameterName, long value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setLongAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setFloatAtName(String parameterName, float value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setFloatAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDoubleAtName(String parameterName, double value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDoubleAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryFloatAtName(String parameterName, float value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryFloatAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryFloatAtName(String parameterName, BINARY_FLOAT value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryFloatAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryDoubleAtName(String parameterName, double value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryDoubleAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryDoubleAtName(String parameterName, BINARY_DOUBLE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryDoubleAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBigDecimalAtName(String parameterName, BigDecimal value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBigDecimalAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setStringAtName(String parameterName, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setStringAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setStringForClob(int parameterIndex, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setStringForClob(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setStringForClobAtName(String parameterName, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setStringForClobAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setFixedCHARAtName(String parameterName, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setFixedCHARAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCursorAtName(String parameterName, ResultSet value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCursorAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setROWIDAtName(String parameterName, ROWID value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setROWIDAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setArrayAtName(String parameterName, Array value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setArrayAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setARRAYAtName(String parameterName, ARRAY value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setARRAYAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setOPAQUEAtName(String parameterName, OPAQUE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setOPAQUEAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setStructDescriptorAtName(String parameterName, StructDescriptor desc) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setStructDescriptorAtName(parameterName, desc);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setSTRUCTAtName(String parameterName, STRUCT value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setSTRUCTAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRAWAtName(String parameterName, RAW value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRAWAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCHARAtName(String parameterName, CHAR value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCHARAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDATEAtName(String parameterName, DATE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDATEAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNUMBERAtName(String parameterName, NUMBER value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNUMBERAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBLOBAtName(String parameterName, BLOB value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBLOBAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBlobAtName(String parameterName, Blob value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlobAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBlobAtName(String parameterName, InputStream stream, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlobAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBlobAtName(String parameterName, InputStream stream) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBlobAtName(parameterName, stream);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCLOBAtName(String parameterName, CLOB value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCLOBAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setClobAtName(String parameterName, Clob value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClobAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setClobAtName(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClobAtName(parameterName, reader, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setClobAtName(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setClobAtName(parameterName, reader);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBFILEAtName(String parameterName, BFILE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBFILEAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBfileAtName(String parameterName, BFILE value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBfileAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBytesAtName(String parameterName, byte[] value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBytesAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBytesForBlob(int parameterIndex, byte[] value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBytesForBlob(parameterIndex, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBytesForBlobAtName(String parameterName, byte[] value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBytesForBlobAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDateAtName(String parameterName, Date value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDateAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDateAtName(String parameterName, Date value, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setDateAtName(parameterName, value, cal);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTimeAtName(String parameterName, Time value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimeAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTimeAtName(String parameterName, Time value, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimeAtName(parameterName, value, cal);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTimestampAtName(String parameterName, Timestamp value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimestampAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTimestampAtName(String parameterName, Timestamp value, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTimestampAtName(parameterName, value, cal);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setINTERVALYMAtName(String parameterName, INTERVALYM value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setINTERVALYMAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setINTERVALDSAtName(String parameterName, INTERVALDS value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setINTERVALDSAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMPAtName(String parameterName, TIMESTAMP value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMPAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMPTZAtName(String parameterName, TIMESTAMPTZ value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMPTZAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setTIMESTAMPLTZAtName(String parameterName, TIMESTAMPLTZ value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setTIMESTAMPLTZAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setAsciiStreamAtName(String parameterName, InputStream stream, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStreamAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setAsciiStreamAtName(String parameterName, InputStream stream, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStreamAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setAsciiStreamAtName(String parameterName, InputStream stream) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setAsciiStreamAtName(parameterName, stream);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryStreamAtName(String parameterName, InputStream stream, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStreamAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryStreamAtName(String parameterName, InputStream stream, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStreamAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setBinaryStreamAtName(String parameterName, InputStream stream) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setBinaryStreamAtName(parameterName, stream);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCharacterStreamAtName(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStreamAtName(parameterName, reader, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCharacterStreamAtName(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStreamAtName(parameterName, reader);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setUnicodeStreamAtName(String parameterName, InputStream stream, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setUnicodeStreamAtName(parameterName, stream, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setCustomDatumAtName(String parameterName, CustomDatum value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCustomDatumAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setORADataAtName(String parameterName, ORAData value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setORADataAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setObjectAtName(String parameterName, Object value, int targetSqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObjectAtName(parameterName, value, targetSqlType, scale);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setObjectAtName(String parameterName, Object value, int targetSqlType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObjectAtName(parameterName, value, targetSqlType);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRefTypeAtName(String parameterName, REF value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRefTypeAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRefAtName(String parameterName, Ref value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRefAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setREFAtName(String parameterName, REF value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setREFAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setObjectAtName(String parameterName, Object value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setObjectAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setOracleObjectAtName(String parameterName, Datum value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setOracleObjectAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setURLAtName(String parameterName, URL value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setURLAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement, oracle.jdbc.internal.OraclePreparedStatement
    public void setCheckBindTypes(boolean flag) {
        awaitExecution();
        unwrapPreparedStatement().setCheckBindTypes(flag);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    @Deprecated
    public void setPlsqlIndexTable(int parameterIndex, Object arrayData, int maxLen, int curLen, int elemSqlType, int elemMaxLen) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setPlsqlIndexTable(parameterIndex, arrayData, maxLen, curLen, elemSqlType, elemMaxLen);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setFormOfUse(int parameterIndex, short formOfUse) {
        awaitExecution();
        unwrapPreparedStatement().setFormOfUse(parameterIndex, formOfUse);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setDisableStmtCaching(boolean cache) {
        awaitExecution();
        unwrapPreparedStatement().setDisableStmtCaching(cache);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public oracle.jdbc.OracleParameterMetaData OracleGetParameterMetaData() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().OracleGetParameterMetaData();
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().registerReturnParameter(paramIndex, externalType);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType, int maxSize) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().registerReturnParameter(paramIndex, externalType, maxSize);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType, String typeName) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().registerReturnParameter(paramIndex, externalType, typeName);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public ResultSet getReturnResultSet() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().getReturnResultSet();
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNCharacterStreamAtName(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNCharacterStreamAtName(parameterName, reader, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNCharacterStreamAtName(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNCharacterStreamAtName(parameterName, reader);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNClobAtName(String parameterName, NClob value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClobAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNClobAtName(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClobAtName(parameterName, reader, length);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNClobAtName(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNClobAtName(parameterName, reader);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setNStringAtName(String parameterName, String value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setNStringAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setRowIdAtName(String parameterName, RowId value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setRowIdAtName(parameterName, value);
    }

    @Override // oracle.jdbc.OraclePreparedStatement
    public void setSQLXMLAtName(String parameterName, SQLXML value) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setSQLXMLAtName(parameterName, value);
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void setInternalBytes(int paramIndex, byte[] x, int dbtype) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setInternalBytes(paramIndex, x, dbtype);
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void enterImplicitCache() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().enterImplicitCache();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void enterExplicitCache() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().enterExplicitCache();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void exitImplicitCacheToActive() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().exitImplicitCacheToActive();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void exitExplicitCacheToActive() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().exitExplicitCacheToActive();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void exitImplicitCacheToClose() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().exitImplicitCacheToClose();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void exitExplicitCacheToClose() throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().exitExplicitCacheToClose();
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement
    public void setCharacterStreamAtName(String name, Reader stream, int length) throws SQLException {
        awaitExecution();
        unwrapPreparedStatement().setCharacterStreamAtName(name, stream, length);
    }

    @Override // oracle.jdbc.internal.OraclePreparedStatement, oracle.jdbc.internal.OracleStatement
    public String getOriginalSql() throws SQLException {
        awaitExecution();
        return unwrapPreparedStatement().getOriginalSql();
    }

    @Override // oracle.jdbc.internal.OracleCallableStatement
    public byte[] privateGetBytes(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().privateGetBytes(parameterIndex);
    }

    @Override // oracle.jdbc.internal.OracleCallableStatement
    public BigDecimal getBigDecimal(String parameterName, int scale) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBigDecimal(parameterName, scale);
    }

    @Override // oracle.jdbc.internal.OracleCallableStatement
    public InputStream getAsciiStream(String columnName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getAsciiStream(columnName);
    }

    @Override // oracle.jdbc.internal.OracleCallableStatement, java.sql.CallableStatement
    public Reader getCharacterStream(String name) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCharacterStream(name);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public ARRAY getARRAY(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getARRAY(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public InputStream getAsciiStream(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getAsciiStream(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public BFILE getBFILE(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBFILE(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public BFILE getBfile(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBfile(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public InputStream getBinaryStream(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBinaryStream(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public InputStream getBinaryStream(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBinaryStream(parameterName);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public BLOB getBLOB(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBLOB(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public CHAR getCHAR(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCHAR(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public Reader getCharacterStream(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCharacterStream(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public CLOB getCLOB(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCLOB(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public ResultSet getCursor(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCursor(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public Object getCustomDatum(int parameterIndex, CustomDatumFactory factory) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getCustomDatum(parameterIndex, factory);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public Object getORAData(int parameterIndex, ORADataFactory factory) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getORAData(parameterIndex, factory);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public Object getObject(int parameterIndex, OracleDataFactory factory) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getObject(parameterIndex, factory);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public Object getAnyDataEmbeddedObject(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getAnyDataEmbeddedObject(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public DATE getDATE(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDATE(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public NUMBER getNUMBER(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNUMBER(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public OPAQUE getOPAQUE(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getOPAQUE(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public Datum getOracleObject(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getOracleObject(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public RAW getRAW(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getRAW(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public REF getREF(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getREF(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public ROWID getROWID(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getROWID(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public STRUCT getSTRUCT(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getSTRUCT(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public INTERVALYM getINTERVALYM(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getINTERVALYM(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public INTERVALDS getINTERVALDS(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getINTERVALDS(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public TIMESTAMP getTIMESTAMP(int paramIdx) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTIMESTAMP(paramIdx);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public TIMESTAMPTZ getTIMESTAMPTZ(int paramIdx) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTIMESTAMPTZ(paramIdx);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public TIMESTAMPLTZ getTIMESTAMPLTZ(int paramIdx) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTIMESTAMPLTZ(paramIdx);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public InputStream getUnicodeStream(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getUnicodeStream(parameterIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public InputStream getUnicodeStream(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getUnicodeStream(parameterName);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameter(int paramIndex, int sqlType, int scale, int maxLength) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(paramIndex, sqlType, scale, maxLength);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameterBytes(int paramIndex, int sqlType, int scale, int maxLength) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameterBytes(paramIndex, sqlType, scale, maxLength);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameterChars(int paramIndex, int sqlType, int scale, int maxLength) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameterChars(paramIndex, sqlType, scale, maxLength);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    @Deprecated
    public Object getPlsqlIndexTable(int paramIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getPlsqlIndexTable(paramIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    @Deprecated
    public Object getPlsqlIndexTable(int paramIndex, Class<?> primitiveType) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getPlsqlIndexTable(paramIndex, primitiveType);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    @Deprecated
    public Datum[] getOraclePlsqlIndexTable(int paramIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getOraclePlsqlIndexTable(paramIndex);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    @Deprecated
    public void registerIndexTableOutParameter(int paramIndex, int maxLen, int elemSqlType, int elemMaxLen) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerIndexTableOutParameter(paramIndex, maxLen, elemSqlType, elemMaxLen);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBinaryFloat(String parameterName, BINARY_FLOAT x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryFloat(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBinaryDouble(String parameterName, BINARY_DOUBLE x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryDouble(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setStringForClob(String parameterName, String x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setStringForClob(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBytesForBlob(String parameterName, byte[] x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBytesForBlob(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameter(String parameterName, int sqlType, int scale, int maxLength) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType, scale, maxLength);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setNull(String parameterName, int sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNull(parameterName, sqlType, typeName);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setNull(String parameterName, int sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNull(parameterName, sqlType);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setBoolean(String parameterName, boolean x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBoolean(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setByte(String parameterName, byte x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setByte(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setShort(String parameterName, short x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setShort(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setInt(String parameterName, int x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setInt(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setLong(String parameterName, long x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setLong(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setFloat(String parameterName, float x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setFloat(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBinaryFloat(String parameterName, float x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryFloat(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBinaryDouble(String parameterName, double x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryDouble(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setDouble(String parameterName, double x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setDouble(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setBigDecimal(String parameterName, BigDecimal x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBigDecimal(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setString(String parameterName, String x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setString(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setFixedCHAR(String parameterName, String x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setFixedCHAR(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setCursor(String parameterName, ResultSet x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCursor(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setROWID(String parameterName, ROWID x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setROWID(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setRAW(String parameterName, RAW x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setRAW(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setCHAR(String parameterName, CHAR x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCHAR(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setDATE(String parameterName, DATE x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setDATE(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setNUMBER(String parameterName, NUMBER x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNUMBER(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBLOB(String parameterName, BLOB x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBLOB(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setBlob(String parameterName, Blob x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBlob(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setCLOB(String parameterName, CLOB x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCLOB(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setClob(String parameterName, Clob x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setClob(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBFILE(String parameterName, BFILE x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBFILE(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setBfile(String parameterName, BFILE x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBfile(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setBytes(String parameterName, byte[] x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBytes(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setDate(String parameterName, Date x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setDate(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setTime(String parameterName, Time x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTime(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setTimestamp(String parameterName, Timestamp x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTimestamp(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setINTERVALYM(String parameterName, INTERVALYM x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setINTERVALYM(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setINTERVALDS(String parameterName, INTERVALDS x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setINTERVALDS(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setTIMESTAMP(String parameterName, TIMESTAMP x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTIMESTAMP(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setTIMESTAMPTZ(String parameterName, TIMESTAMPTZ x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTIMESTAMPTZ(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setTIMESTAMPLTZ(String parameterName, TIMESTAMPLTZ x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTIMESTAMPLTZ(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setAsciiStream(String parameterName, InputStream x, int y) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setAsciiStream(parameterName, x, y);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setBinaryStream(String parameterName, InputStream x, int y) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryStream(parameterName, x, y);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setUnicodeStream(String parameterName, InputStream x, int y) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setUnicodeStream(parameterName, x, y);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setCharacterStream(String parameterName, Reader x, int y) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCharacterStream(parameterName, x, y);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setDate(String parameterName, Date x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setDate(parameterName, x, cal);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setTime(String parameterName, Time x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTime(parameterName, x, cal);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setTimestamp(String parameterName, Timestamp x, Calendar cal) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setTimestamp(parameterName, x, cal);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setURL(String parameterName, URL x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setURL(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setArray(String parameterName, Array x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setArray(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setARRAY(String parameterName, ARRAY x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setARRAY(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setOPAQUE(String parameterName, OPAQUE x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setOPAQUE(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setStructDescriptor(String parameterName, StructDescriptor x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setStructDescriptor(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setSTRUCT(String parameterName, STRUCT x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setSTRUCT(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setCustomDatum(String parameterName, CustomDatum x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCustomDatum(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setORAData(String parameterName, ORAData x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setORAData(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setObject(String parameterName, Object x, int targetSqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setObject(parameterName, x, targetSqlType, scale);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setObject(String parameterName, Object x, int y) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setObject(parameterName, x, y);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setRefType(String parameterName, REF x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setRefType(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setRef(String parameterName, Ref x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setRef(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setREF(String parameterName, REF x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setREF(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement, java.sql.CallableStatement
    public void setObject(String parameterName, Object x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setObject(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void setOracleObject(String parameterName, Datum x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setOracleObject(parameterName, x);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameterAtName(String parameterMarkerName, int sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameterAtName(parameterMarkerName, sqlType);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameterAtName(String parameterMarkerName, int sqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameterAtName(parameterMarkerName, sqlType, scale);
    }

    @Override // oracle.jdbc.OracleCallableStatement
    public void registerOutParameterAtName(String parameterMarkerName, int sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameterAtName(parameterMarkerName, sqlType, typeName);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(int parameterIndex, int sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(int parameterIndex, int sqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType, scale);
    }

    @Override // java.sql.CallableStatement
    public boolean wasNull() throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().wasNull();
    }

    @Override // java.sql.CallableStatement
    public String getString(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getString(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public boolean getBoolean(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBoolean(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public byte getByte(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getByte(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public short getShort(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getShort(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public int getInt(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getInt(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public long getLong(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getLong(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public float getFloat(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getFloat(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public double getDouble(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDouble(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    @Deprecated
    public BigDecimal getBigDecimal(int parameterIndex, int scale) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBigDecimal(parameterIndex, scale);
    }

    @Override // java.sql.CallableStatement
    public byte[] getBytes(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBytes(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Date getDate(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDate(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Time getTime(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTime(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Timestamp getTimestamp(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTimestamp(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Object getObject(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getObject(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public BigDecimal getBigDecimal(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBigDecimal(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Object getObject(int parameterIndex, Map<String, Class<?>> map) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getObject(parameterIndex, map);
    }

    @Override // java.sql.CallableStatement
    public Ref getRef(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getRef(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Blob getBlob(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBlob(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Clob getClob(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getClob(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Array getArray(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getArray(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Date getDate(int parameterIndex, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDate(parameterIndex, cal);
    }

    @Override // java.sql.CallableStatement
    public Time getTime(int parameterIndex, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTime(parameterIndex, cal);
    }

    @Override // java.sql.CallableStatement
    public Timestamp getTimestamp(int parameterIndex, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTimestamp(parameterIndex, cal);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(int parameterIndex, int sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType, typeName);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(String parameterName, int sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(String parameterName, int sqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType, scale);
    }

    @Override // java.sql.CallableStatement
    public void registerOutParameter(String parameterName, int sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType, typeName);
    }

    @Override // java.sql.CallableStatement
    public URL getURL(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getURL(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public String getString(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getString(parameterName);
    }

    @Override // java.sql.CallableStatement
    public boolean getBoolean(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBoolean(parameterName);
    }

    @Override // java.sql.CallableStatement
    public byte getByte(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getByte(parameterName);
    }

    @Override // java.sql.CallableStatement
    public short getShort(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getShort(parameterName);
    }

    @Override // java.sql.CallableStatement
    public int getInt(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getInt(parameterName);
    }

    @Override // java.sql.CallableStatement
    public long getLong(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getLong(parameterName);
    }

    @Override // java.sql.CallableStatement
    public float getFloat(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getFloat(parameterName);
    }

    @Override // java.sql.CallableStatement
    public double getDouble(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDouble(parameterName);
    }

    @Override // java.sql.CallableStatement
    public byte[] getBytes(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBytes(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Date getDate(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDate(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Time getTime(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTime(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Timestamp getTimestamp(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTimestamp(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Object getObject(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getObject(parameterName);
    }

    @Override // java.sql.CallableStatement
    public BigDecimal getBigDecimal(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBigDecimal(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Object getObject(String parameterName, Map<String, Class<?>> map) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getObject(parameterName, map);
    }

    @Override // java.sql.CallableStatement
    public Ref getRef(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getRef(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Blob getBlob(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getBlob(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Clob getClob(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getClob(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Array getArray(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getArray(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Date getDate(String parameterName, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getDate(parameterName, cal);
    }

    @Override // java.sql.CallableStatement
    public Time getTime(String parameterName, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTime(parameterName, cal);
    }

    @Override // java.sql.CallableStatement
    public Timestamp getTimestamp(String parameterName, Calendar cal) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getTimestamp(parameterName, cal);
    }

    @Override // java.sql.CallableStatement
    public URL getURL(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getURL(parameterName);
    }

    @Override // java.sql.CallableStatement
    public RowId getRowId(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getRowId(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public RowId getRowId(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getRowId(parameterName);
    }

    @Override // java.sql.CallableStatement
    public void setRowId(String parameterName, RowId x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setRowId(parameterName, x);
    }

    @Override // java.sql.CallableStatement
    public void setNString(String parameterName, String value) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNString(parameterName, value);
    }

    @Override // java.sql.CallableStatement
    public void setNCharacterStream(String parameterName, Reader value, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNCharacterStream(parameterName, value, length);
    }

    @Override // java.sql.CallableStatement
    public void setNClob(String parameterName, NClob value) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNClob(parameterName, value);
    }

    @Override // java.sql.CallableStatement
    public void setClob(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setClob(parameterName, reader, length);
    }

    @Override // java.sql.CallableStatement
    public void setBlob(String parameterName, InputStream inputStream, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBlob(parameterName, inputStream, length);
    }

    @Override // java.sql.CallableStatement
    public void setNClob(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNClob(parameterName, reader, length);
    }

    @Override // java.sql.CallableStatement
    public NClob getNClob(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNClob(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public NClob getNClob(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNClob(parameterName);
    }

    @Override // java.sql.CallableStatement
    public void setSQLXML(String parameterName, SQLXML xmlObject) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setSQLXML(parameterName, xmlObject);
    }

    @Override // java.sql.CallableStatement
    public SQLXML getSQLXML(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getSQLXML(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public SQLXML getSQLXML(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getSQLXML(parameterName);
    }

    @Override // java.sql.CallableStatement
    public String getNString(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNString(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public String getNString(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNString(parameterName);
    }

    @Override // java.sql.CallableStatement
    public Reader getNCharacterStream(int parameterIndex) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNCharacterStream(parameterIndex);
    }

    @Override // java.sql.CallableStatement
    public Reader getNCharacterStream(String parameterName) throws SQLException {
        awaitExecution();
        return unwrapCallableStatement().getNCharacterStream(parameterName);
    }

    @Override // java.sql.CallableStatement
    public void setAsciiStream(String parameterName, InputStream x, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setAsciiStream(parameterName, x, length);
    }

    @Override // java.sql.CallableStatement
    public void setBinaryStream(String parameterName, InputStream x, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryStream(parameterName, x, length);
    }

    @Override // java.sql.CallableStatement
    public void setCharacterStream(String parameterName, Reader reader, long length) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCharacterStream(parameterName, reader, length);
    }

    @Override // java.sql.CallableStatement
    public void setAsciiStream(String parameterName, InputStream x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setAsciiStream(parameterName, x);
    }

    @Override // java.sql.CallableStatement
    public void setBinaryStream(String parameterName, InputStream x) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBinaryStream(parameterName, x);
    }

    @Override // java.sql.CallableStatement
    public void setCharacterStream(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setCharacterStream(parameterName, reader);
    }

    @Override // java.sql.CallableStatement
    public void setNCharacterStream(String parameterName, Reader value) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNCharacterStream(parameterName, value);
    }

    @Override // java.sql.CallableStatement
    public void setClob(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setClob(parameterName, reader);
    }

    @Override // java.sql.CallableStatement
    public void setBlob(String parameterName, InputStream inputStream) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setBlob(parameterName, inputStream);
    }

    @Override // java.sql.CallableStatement
    public void setNClob(String parameterName, Reader reader) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setNClob(parameterName, reader);
    }

    public <T> T getObject(int i, Class<T> cls) throws SQLException {
        awaitExecution();
        return (T) unwrapCallableStatement().getObject(i, cls);
    }

    public <T> T getObject(String str, Class<T> cls) throws SQLException {
        awaitExecution();
        return (T) unwrapCallableStatement().getObject(str, cls);
    }

    public void setObject(String parameterName, Object x, SQLType targetSqlType, int scaleOrLength) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setObject(parameterName, x, targetSqlType, scaleOrLength);
    }

    public void setObject(String parameterName, Object x, SQLType targetSqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().setObject(parameterName, x, targetSqlType);
    }

    public void registerOutParameter(int parameterIndex, SQLType sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType);
    }

    public void registerOutParameter(int parameterIndex, SQLType sqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType, scale);
    }

    public void registerOutParameter(int parameterIndex, SQLType sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterIndex, sqlType, typeName);
    }

    public void registerOutParameter(String parameterName, SQLType sqlType) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType);
    }

    public void registerOutParameter(String parameterName, SQLType sqlType, int scale) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType, scale);
    }

    public void registerOutParameter(String parameterName, SQLType sqlType, String typeName) throws SQLException {
        awaitExecution();
        unwrapCallableStatement().registerOutParameter(parameterName, sqlType, typeName);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public long getSSSCursorChecksum() throws SQLException {
        awaitExecution();
        return this.statement.getSSSCursorChecksum();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setSSSCursorPosition(long pos) throws SQLException {
        awaitExecution();
        this.statement.setSSSCursorPosition(pos);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public boolean allRowsFetched() throws SQLException {
        awaitExecution();
        return this.statement.allRowsFetched();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void markSSSCursor() throws SQLException {
        awaitExecution();
        this.statement.markSSSCursor();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public boolean isAutoKeyUsed() throws SQLException {
        awaitExecution();
        return this.statement.isAutoKeyUsed();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public boolean doesBatchExist() throws SQLException {
        awaitExecution();
        return this.statement.doesBatchExist();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public oracle.jdbc.internal.OracleConnection getInternalConnection() throws SQLException {
        awaitExecution();
        return this.statement.getInternalConnection();
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public int copyBinds(Statement toStmt, int offset) throws SQLException {
        awaitExecution();
        return this.statement.copyBinds(toStmt, offset);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setLongPrefetch(boolean isEnabled) {
        awaitExecution();
        this.statement.setLongPrefetch(isEnabled);
    }

    @Override // oracle.jdbc.internal.OracleStatement
    public void setRowDataLimit(long numBytes) throws SQLException {
        try {
            awaitExecution();
        } finally {
            this.statement.setRowDataLimit(numBytes);
        }
    }
}
