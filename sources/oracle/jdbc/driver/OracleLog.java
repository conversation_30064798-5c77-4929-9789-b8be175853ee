package oracle.jdbc.driver;

import java.io.InputStream;
import java.util.logging.Level;
import java.util.logging.LogManager;
import oracle.jdbc.diagnostics.AbstractDiagnosable;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/OracleLog.class */
public class OracleLog {
    private static final String DEFAULT_LOGGING_CONFIG_RESOURCE_NAME = "/oracle/jdbc/defaultLoggingConfig.properties";
    private static final int maxPrintBytes = 512;
    public static final boolean TRACE = false;
    public static final Level INTERNAL_ERROR = OracleLevel.INTERNAL_ERROR;
    public static final Level TRACE_1 = OracleLevel.TRACE_1;
    public static final Level TRACE_10 = OracleLevel.TRACE_10;
    public static final Level TRACE_16 = OracleLevel.TRACE_16;
    public static final Level TRACE_20 = OracleLevel.TRACE_20;
    public static final Level TRACE_30 = OracleLevel.TRACE_30;
    public static final Level TRACE_32 = OracleLevel.TRACE_32;
    static boolean securityExceptionWhileGettingSystemProperties;

    @Deprecated
    public static void enableContinousLogging() {
    }

    @Deprecated
    public static void disableContinousLogging() {
    }

    @Deprecated
    public static boolean isContinousLoggingEnabled() {
        return false;
    }

    @Deprecated
    public static void enableInMemoryLogging() {
    }

    @Deprecated
    public static void disableInMemoryLogging() {
    }

    @Deprecated
    public static boolean isInMemoryLoggingEnabled() {
        return false;
    }

    @Deprecated
    public static void setUserNameFilter(String userName) {
    }

    @Deprecated
    public static void setServiceNameFilter(String serviceName) {
    }

    public static boolean isEnabled() {
        return AbstractDiagnosable.isGlobalDebugEnabled();
    }

    public static boolean registerClassNameAndGetCurrentTraceSetting(Class<?> classObj) {
        return false;
    }

    public static void setTrace(boolean enable) {
        AbstractDiagnosable.enableGlobalDebug(enable);
    }

    private static void initialize() {
        setupFromSystemProperties();
        enableDefaultTrace();
    }

    public static void setupFromSystemProperties() {
        boolean turnLoggingOn = false;
        securityExceptionWhileGettingSystemProperties = false;
        try {
            String propStr = GeneratedPhysicalConnection.getSystemPropertyTrace();
            if ("true".equals(propStr)) {
                turnLoggingOn = true;
            }
        } catch (SecurityException e) {
            securityExceptionWhileGettingSystemProperties = true;
        }
        setTrace(turnLoggingOn);
    }

    public static void enableDefaultTrace() {
        try {
            InputStream in = PhysicalConnection.class.getResourceAsStream(DEFAULT_LOGGING_CONFIG_RESOURCE_NAME);
            if (in != null) {
                LogManager.getLogManager().readConfiguration(in);
                setTrace(true);
            }
        } catch (Throwable th) {
        }
    }

    public static String argument() {
        return "";
    }

    public static String argument(boolean x) {
        return Boolean.toString(x);
    }

    public static String argument(byte x) {
        return Byte.toString(x);
    }

    public static String argument(short x) {
        return Short.toString(x);
    }

    public static String argument(int x) {
        return Integer.toString(x);
    }

    public static String argument(long x) {
        return Long.toString(x);
    }

    public static String argument(float x) {
        return Float.toString(x);
    }

    public static String argument(double x) {
        return Double.toString(x);
    }

    public static String argument(Object x) {
        return x == null ? "null" : x instanceof String ? "\"" + ((String) x) + "\"" : x.toString();
    }

    @Deprecated
    public static String byteToHexString(byte b) {
        StringBuffer buf = new StringBuffer("");
        int b_value = 255 & b;
        if (b_value <= 15) {
            buf.append("0x0");
        } else {
            buf.append("0x");
        }
        buf.append(Integer.toHexString(b_value));
        return buf.toString();
    }

    @Deprecated
    public static String bytesToPrintableForm(String header, byte[] bytes) {
        int bytes_len = bytes == null ? 0 : bytes.length;
        return bytesToPrintableForm(header, bytes, bytes_len);
    }

    @Deprecated
    public static String bytesToPrintableForm(String header, byte[] bytes, int nbytes) {
        String ret_str;
        if (bytes == null) {
            ret_str = header + ": null";
        } else {
            ret_str = header + " (" + bytes.length + " bytes):\n" + bytesToFormattedStr(bytes, nbytes, "  ");
        }
        return ret_str;
    }

    @Deprecated
    public static String bytesToFormattedStr(byte[] bytes, int nbytes, String margin) {
        StringBuffer buf = new StringBuffer("");
        if (margin == null) {
            margin = new String("");
        }
        buf.append(margin);
        if (bytes == null) {
            buf.append("byte [] is null");
            return buf.toString();
        }
        int i = 0;
        while (true) {
            if (i >= nbytes) {
                break;
            }
            if (i >= 512) {
                buf.append("\n" + margin + "... last " + (nbytes - 512) + " bytes were not printed to limit the output size");
                break;
            }
            if (i > 0 && i % 20 == 0) {
                buf.append("\n" + margin);
            }
            if (i % 20 == 10) {
                buf.append(" ");
            }
            int b_value = 255 & bytes[i];
            if (b_value <= 15) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(b_value) + " ");
            i++;
        }
        return buf.toString();
    }

    @Deprecated
    public static byte[] strToUcs2Bytes(String str) {
        if (str == null) {
            return null;
        }
        return charsToUcs2Bytes(str.toCharArray());
    }

    @Deprecated
    public static byte[] charsToUcs2Bytes(char[] chars) {
        if (chars == null) {
            return null;
        }
        return charsToUcs2Bytes(chars, chars.length);
    }

    @Deprecated
    public static byte[] charsToUcs2Bytes(char[] chars, int nchars) {
        if (chars == null || nchars < 0) {
            return null;
        }
        return charsToUcs2Bytes(chars, nchars, 0);
    }

    @Deprecated
    public static byte[] charsToUcs2Bytes(char[] chars, int nchars, int offset) {
        if (chars == null) {
            return null;
        }
        if (nchars > chars.length - offset) {
            nchars = chars.length - offset;
        }
        if (nchars < 0) {
            return null;
        }
        byte[] bytes = new byte[2 * nchars];
        int byte_i = 0;
        for (int char_i = offset; char_i < nchars; char_i++) {
            int i = byte_i;
            int byte_i2 = byte_i + 1;
            bytes[i] = (byte) ((chars[char_i] >> '\b') & 255);
            byte_i = byte_i2 + 1;
            bytes[byte_i2] = (byte) (chars[char_i] & 255);
        }
        return bytes;
    }

    @Deprecated
    public static String toPrintableStr(String str, int maxchars) {
        if (str == null) {
            return "null";
        }
        if (str.length() > maxchars) {
            return str.substring(0, maxchars - 1) + "\n ... the actual length was " + str.length();
        }
        return str;
    }

    static {
        initialize();
    }

    public static String toHex(long value, int bytes) {
        String result;
        switch (bytes) {
            case 1:
                result = "00" + Long.toString(value & 255, 16);
                break;
            case 2:
                result = oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_RESOURCE_MANAGER_ID_DEFAULT + Long.toString(value & 65535, 16);
                break;
            case 3:
                result = "000000" + Long.toString(value & 16777215, 16);
                break;
            case 4:
                result = "00000000" + Long.toString(value & SQLnetDef.NSPDDLSLMAX, 16);
                break;
            case 5:
                result = "0000000000" + Long.toString(value & 1099511627775L, 16);
                break;
            case 6:
                result = "000000000000" + Long.toString(value & 281474976710655L, 16);
                break;
            case 7:
                result = "00000000000000" + Long.toString(value & 72057594037927935L, 16);
                break;
            case 8:
                return toHex(value >> 32, 4) + toHex(value, 4).substring(2);
            default:
                return "more than 8 bytes";
        }
        return "0x" + result.substring(result.length() - (2 * bytes));
    }

    public static String toHex(byte value) {
        String result = "00" + Integer.toHexString(value & 255);
        return "0x" + result.substring(result.length() - 2);
    }

    public static String toHex(short value) {
        return toHex(value, 2);
    }

    public static String toHex(int value) {
        return toHex(value, 4);
    }

    public static String toHex(byte[] value, int length) {
        if (value == null) {
            return "null";
        }
        if (length > value.length) {
            return "byte array not long enough";
        }
        String result = "[";
        int len = Math.min(64, length);
        for (int i = 0; i < len; i++) {
            result = result + toHex(value[i]) + " ";
        }
        if (len < length) {
            result = result + "...";
        }
        return result + "]";
    }

    public static String toHex(byte[] value) {
        if (value == null) {
            return "null";
        }
        return toHex(value, value.length);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/OracleLog$OracleLevel.class */
    private static class OracleLevel extends Level {
        private static final long serialVersionUID = -2613875615050961941L;
        static final OracleLevel INTERNAL_ERROR = new OracleLevel("INTERNAL_ERROR", 1100);
        static final OracleLevel TRACE_1 = new OracleLevel("TRACE_1", Level.FINE.intValue());
        static final OracleLevel TRACE_10 = new OracleLevel("TRACE_10", DatabaseError.TTC0219);
        static final OracleLevel TRACE_16 = new OracleLevel("TRACE_16", Level.FINER.intValue());
        static final OracleLevel TRACE_20 = new OracleLevel("TRACE_20", DatabaseError.NO_REPLAY_END_REPLAY_FAILED);
        static final OracleLevel TRACE_30 = new OracleLevel("TRACE_30", 316);
        static final OracleLevel TRACE_32 = new OracleLevel("TRACE_32", Level.FINEST.intValue());

        OracleLevel(String name, int n) {
            super(name, n);
        }
    }
}
