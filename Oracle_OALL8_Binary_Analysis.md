# Oracle OALL8 TTI消息二进制数据深度分析

## 概述

基于对Oracle JDBC驱动源码的深入分析，本文档详细解析OALL8 TTI消息的实际网络编码格式，并验证之前文档中格式定义的准确性。

## 源码分析：OALL8消息编码流程

### 1. 消息编码入口点

根据T4C8Oall.java源码分析，OALL8消息的编码流程如下：

```java
// T4CTTIfun.java - marshalFunHeader()
private final void marshalFunHeader(long tokenNumber) throws IOException {
    marshalTTCcode();                           // 1. 编码TTC类型码
    this.meg.marshalUB1(this.funCode);         // 2. 编码函数码
    this.sequenceNumber = this.connection.getNextSeqNumber();
    this.meg.marshalUB1(this.sequenceNumber);  // 3. 编码序列号
    if (this.connection.getTTCVersion() >= 18) {
        this.meg.marshalUB8(tokenNumber);      // 4. 编码令牌号（可选）
    }
}

// T4C8Oall.java - marshal()
void marshal() throws IOException {
    // ... 其他函数码处理
    else {
        marshalPisdef();                        // 5. 编码OALL8参数定义
        this.meg.marshalCHR(this.sqlStmt);     // 6. 编码SQL语句
        this.meg.marshalUB4Array(this.al8i4);  // 7. 编码AL8I4数组
        // ... 绑定和定义信息
    }
}
```

### 2. marshalPisdef()方法详细分析

```java
void marshalPisdef() throws IOException {
    this.meg.marshalUB4(this.options);         // 操作选项（4字节）
    this.meg.marshalSWORD(this.cursor);        // 游标ID（4字节）
    
    // SQL语句指针和长度
    if (this.sqlStmt.length == 0) {
        this.meg.marshalNULLPTR();             // 空指针（1字节：0x00）
    } else {
        this.meg.marshalPTR();                 // 非空指针（1字节：0x01）
    }
    this.meg.marshalSWORD(this.sqlStmt.length); // SQL长度（4字节）
    
    // AL8I4数组指针和长度
    if (this.al8i4.length == 0) {
        this.meg.marshalNULLPTR();
    } else {
        this.meg.marshalPTR();                 // 非空指针（1字节：0x01）
    }
    this.meg.marshalSWORD(this.al8i4.length); // AL8I4数组长度（4字节）
    
    this.meg.marshalNULLPTR();                 // 保留字段1（1字节：0x00）
    this.meg.marshalNULLPTR();                 // 保留字段2（1字节：0x00）
    
    // 行数相关字段
    if ((this.options & 64) == 0 && (this.options & 32) != 0 && 
        (this.options & 1) != 0 && this.typeOfStatement.isSELECT()) {
        this.meg.marshalUB4(Long.MAX_VALUE);   // 最大行数
        this.meg.marshalUB4(this.rowsToFetch); // 要获取的行数
    } else {
        this.meg.marshalUB4(0L);               // 最大行数（4字节：0）
        this.meg.marshalUB4(0L);               // 要获取的行数（4字节：0）
    }
    
    // 最大LONG大小
    if (!this.typeOfStatement.isPlsqlOrCall()) {
        this.meg.marshalUB4(2147483647L);      // 0x7FFFFFFF
    } else {
        this.meg.marshalUB4(32760L);           // 0x7FF8
    }
    
    // 绑定变量定义
    if ((this.options & 8) != 0 && this.numberOfBindPositions > 0 && 
        this.sendBindsDefinition) {
        this.meg.marshalPTR();                 // 绑定定义指针（1字节：0x01）
        this.meg.marshalSWORD(this.numberOfBindPositions); // 绑定变量数量
    } else {
        this.meg.marshalNULLPTR();             // 空指针（1字节：0x00）
        this.meg.marshalSWORD(0);              // 绑定变量数量（4字节：0）
    }
    
    // 5个保留的空指针字段
    this.meg.marshalNULLPTR();                 // 保留字段3-7
    this.meg.marshalNULLPTR();
    this.meg.marshalNULLPTR();
    this.meg.marshalNULLPTR();
    this.meg.marshalNULLPTR();
    
    // TTC版本相关字段
    if (this.connection.getTTCVersion() >= 2) {
        if (this.defCols > 0 && (this.options & 16) != 0) {
            this.meg.marshalPTR();             // 定义列指针
            this.meg.marshalSWORD(this.defCols); // 定义列数量
        } else {
            this.meg.marshalNULLPTR();         // 空指针
            this.meg.marshalSWORD(0);          // 定义列数量（0）
        }
    }
    
    // 更多TTC版本相关字段...
}
```

## 二进制数据解析

### 提供的原始数据：
```
003E 0661 8000 0000 0000 00FE FFFF FFFF FFFF 6F00 0000 FEFF FFFF FFFF FFFF 0D00 0000 FEFF FFFF FFFF FFFF FEFF FFFF FFFF FFFF 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 FEFF FFFF FFFF FFFF 0000 0000 0000 0000 FEFF FFFF FFFF FFFF FEFF FFFF FFFF FFFF FEFF FFFF FFFF FFFF 0000 0000 0000 0000 FEFF FFFF FFFF FFFF FEFF FFFF FFFF FFFF 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 2553 454C 4543 5420 5553 4552 454E 5628 2753 4553 5349 4F4E 4944 2729 2046 524F 4D20 4455 414C 0100 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0100 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000
```

### 逐字节解析：

首先，让我找到SQL语句的位置来验证数据结构：

**SQL语句定位：**
在数据中搜索"SELECT USERENV('SESSIONID') FROM DUAL"的十六进制表示：
- "SELECT" = 53454C454354
- 找到位置：偏移量0x90处开始

**完整的逐字节解析：**

```
偏移量  字节数据        字段含义                    源码对应
------  ------------   ---------------------      ------------------
0x00    003E           消息长度（62字节）           网络层长度前缀
0x02    06             未知字段
0x03    61             可能的序列号或标志
0x04    80000000       Options = 0x80000000       marshalUB4(options)
0x08    00000000       游标ID = 0                 marshalSWORD(cursor)
0x0C    FE             SQL指针标志？               marshalPTR()?
0x0D    FFFFFFFF       异常数据模式开始
0x11    FFFF6F00
0x15    0000FE
0x18    FFFFFFFF
0x1C    FFFFFF0D
0x1F    000000FE
0x22    FFFFFFFF
0x26    FFFFFE
0x29    FFFFFFFF
0x2D    FFFF0000
0x31    00000000
0x35    00000000
0x39    00000000
0x3D    00000000
0x41    00000000
0x45    0000FE
0x48    FFFFFFFF
0x4C    FFFF0000
0x50    00000000
0x54    00FE
0x56    FFFFFFFF
0x5A    FFFFFE
0x5D    FFFFFFFF
0x61    FFFFFE
0x64    FFFFFFFF
0x68    FFFF0000
0x6C    00000000
0x70    00FE
0x72    FFFFFFFF
0x76    FFFFFE
0x79    FFFFFFFF
0x7D    FFFF0000
0x81    00000000
0x85    00000000
0x89    00000000
0x8D    00000000
0x91    00000000

0x95    25             SQL语句长度 = 37字节        marshalSWORD(sqlLength)
0x96    53454C4543     "SELEC"                    SQL语句开始
0x9B    5420555345     "T USE"
0xA0    52454E5628     "RENV("
0xA5    2753455353     "'SESS"
0xAA    494F4E4944     "IONI"
0xAF    2729204652     "') FR"
0xB4    4F4D204455     "OM DU"
0xB9    414C           "AL"                       SQL语句结束

0xBB    01000000       AL8I4[0] = 1               marshalUB4Array开始
0xBF    00000000       AL8I4[1] = 0
0xC3    00000000       AL8I4[2] = 0
0xC7    00000000       AL8I4[3] = 0
0xCB    00000000       AL8I4[4] = 0
0xCF    00000000       AL8I4[5] = 0
0xD3    00000000       AL8I4[6] = 0
0xD7    00000000       AL8I4[7] = 0
0xDB    00000000       AL8I4[8] = 0
0xDF    00000000       AL8I4[9] = 0
0xE3    00000000       AL8I4[10] = 0
0xE7    00000000       AL8I4[11] = 0
0xEB    00000000       AL8I4[12] = 0              AL8I4数组结束

0xEF    01000000       可能的附加字段
0xF3    00000000
0xF7    00000000
0xFB    00000000
0xFF    00000000
0x103   00000000
0x107   00000000
0x10B   00000000
```

**重要发现：**

1. **SQL语句位置正确**：在偏移量0x95处找到长度字段（0x25 = 37字节），随后是完整的SQL语句
2. **AL8I4数组位置正确**：紧跟SQL语句后，包含13个4字节整数
3. **异常数据模式**：0x0C到0x94之间的数据包含大量0xFE和0xFF，这不符合正常的marshalPisdef()输出

**数据格式重新分析：**

这个数据可能是：
1. **调试或测试环境的输出**，包含了额外的调试信息
2. **网络层封装后的数据**，包含了协议头部信息
3. **部分损坏的数据**，中间部分可能被填充或替换

### 正确的OALL8消息格式（基于源码）：

```
OALL8 TTI Message Format (实际网络格式):

+------------------+
| TTC Code (1)     |  0x03 (TTIFUN)
+------------------+
| Function Code(1) |  0x5E (OALL8 = 94)
+------------------+
| Sequence Num(1)  |  序列号
+------------------+
| Token Number(0-8)|  令牌号（TTC版本>=18）
+------------------+
| Options (4)      |  操作选项位掩码
+------------------+
| Cursor ID (4)    |  游标ID
+------------------+
| SQL Ptr Flag(1)  |  SQL指针标志（0x00或0x01）
+------------------+
| SQL Length (4)   |  SQL语句长度
+------------------+
| AL8I4 Ptr Flag(1)|  AL8I4指针标志（0x00或0x01）
+------------------+
| AL8I4 Length (4) |  AL8I4数组长度（通常是13）
+------------------+
| Reserved1 (1)    |  保留字段（0x00）
+------------------+
| Reserved2 (1)    |  保留字段（0x00）
+------------------+
| Max Rows (4)     |  最大行数
+------------------+
| Rows To Fetch(4) |  要获取的行数
+------------------+
| Max Long Size(4) |  最大LONG大小
+------------------+
| Bind Ptr Flag(1) |  绑定定义指针标志
+------------------+
| Bind Count (4)   |  绑定变量数量
+------------------+
| Reserved3-7 (5)  |  5个保留字段（各1字节）
+------------------+
| TTC Version      |  TTC版本相关字段（可变）
| Fields (Var)     |
+------------------+
| SQL Statement    |  SQL语句文本
| (Variable)       |
+------------------+
| AL8I4 Array      |  AL8I4数组（13个4字节整数）
| (52 bytes)       |
+------------------+
| Bind Definitions |  绑定变量定义（可选）
| (Variable)       |
+------------------+
| Define Columns   |  定义列信息（可选）
| (Variable)       |
+------------------+
| Bind Data        |  绑定变量数据（可选）
| (Variable)       |
+------------------+
```

## 编码方式验证

### 1. 数据类型编码

根据T4CMAREngineNIO.java源码：

```java
// UB1 (1字节无符号整数) - 直接编码
marshalUB1(value) -> put((byte)value)

// UB4 (4字节无符号整数) - 可变长度编码
marshalUB4(value):
  if (types.rep[2] != 1) {
    putInt((int)value)              // 固定4字节，网络字节序
  } else {
    // 可变长度编码
    if (value == 0) put((byte)0)
    else if (value <= 255) { put((byte)1); put((byte)value); }
    else if (value <= 65535) { put((byte)2); putShort((short)value); }
    else { put((byte)4); putInt((int)value); }
  }

// SWORD (有符号字) - 等同于SB4
marshalSWORD(value) -> marshalSB4(value)

// PTR/NULLPTR (指针标志)
marshalPTR() -> addPtr((byte)1)
marshalNULLPTR() -> addPtr((byte)0)
```

### 2. 关键发现

**❌ 之前文档中的错误假设：**

1. **TTI Header不包含长度字段**：源码中没有显式的消息长度编码
2. **TTI Header不包含校验和**：源码中没有校验和计算
3. **序列号和令牌号确实存在**：这些是TTIFUN消息的标准字段

**✅ 正确的理解：**

1. **消息长度**：可能在更底层的网络协议中处理（如TLS记录层）
2. **校验和**：可能在TCP层或TLS层处理
3. **可变长度编码**：Oracle使用复杂的可变长度编码优化传输

## 格式定义修正

### 修正后的标准TTI消息格式：

```
Oracle TTI Message Format (网络层实际格式):

1. TTIFUN Message Header:
   +------------------+
   | TTC Code (1)     |  0x03 (TTIFUN)
   +------------------+
   | Function Code(1) |  具体函数码
   +------------------+
   | Sequence Num(1)  |  序列号
   +------------------+
   | Token Number     |  令牌号（TTC版本>=18时，8字节）
   | (0 or 8 bytes)   |
   +------------------+

2. OALL8 Message Body:
   +------------------+
   | Options (4)      |  操作选项位掩码
   +------------------+
   | Cursor ID (4)    |  游标ID  
   +------------------+
   | Parameter Block  |  参数定义块（marshalPisdef输出）
   | (Variable)       |
   +------------------+
   | SQL Statement    |  SQL语句（marshalCHR输出）
   | (Variable)       |
   +------------------+
   | AL8I4 Array      |  参数数组（marshalUB4Array输出）
   | (52 bytes)       |
   +------------------+
   | Optional Blocks  |  可选的绑定/定义块
   | (Variable)       |
   +------------------+
```

## 验证结论

### ✅ 正确的观点：
- "Sequence Number"和"Token Number"确实是TTIFUN消息的标准字段
- OALL8消息体确实包含options、cursor、AL8I4数组等字段
- 消息编码使用可变长度优化

### ❌ 需要修正的观点：
- TTI Header不包含显式的长度字段（长度可能在网络层处理）
- TTI Header不包含校验和字段（校验可能在其他层处理）
- 提供的二进制数据可能不是标准的OALL8消息格式

### ⚠️ 需要进一步研究：
- 网络层的消息封装格式
- 可变长度编码的具体规则
- 不同TTC版本的格式差异

这个分析揭示了Oracle TTI协议的复杂性，以及网络层实现与JDBC抽象层之间的差异。
