# Oracle TTI特定函数码深度分析

## 您提到的函数码详细分析

基于Oracle JDBC驱动源码分析，以下是您特别关注的函数码的详细TTI消息格式和实现细节。

## 1. OV6STRT (48) - V6启动操作

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类

### TTI消息格式
```
OV6STRT Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x30)  |  // OV6STRT = 48
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Startup Params   |  // V6启动参数（具体格式需进一步分析）
| (Variable)       |
+------------------+
```

### 功能说明
- 用于Oracle V6版本的启动操作
- 可能涉及会话初始化和协议协商

## 2. OV6STOP (49) - V6停止操作

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类

### TTI消息格式
```
OV6STOP Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x31)  |  // OV6STOP = 49
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Stop Params      |  // V6停止参数
| (Variable)       |
+------------------+
```

### 功能说明
- 用于Oracle V6版本的停止操作
- 可能涉及会话清理和资源释放

## 3. OVERSION (59) - 版本信息获取

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：T4C7Oversion.java

### TTI消息格式（基于源码分析）
```
OVERSION Request Message:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x3B)  |  // OVERSION = 59
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Buffer Pointer   |  // 缓冲区指针标志（1字节）
| (0x01)           |
+------------------+
| Buffer Length    |  // 缓冲区长度（4字节，值为256）
| (0x00000100)     |
+------------------+
| RetLen Pointer   |  // 返回长度指针标志（1字节）
| (0x01)           |
+------------------+
| RetVer Pointer   |  // 返回版本指针标志（1字节）
| (0x01)           |
+------------------+
| Banner Format    |  // 横幅格式（4字节，TTC>=11时）
| (0x00000001)     |  // VSN_BANNER_FORMAT_FULL = 1
+------------------+
```

### 源码实现（T4C7Oversion.java）
```java
void marshal() throws IOException {
    this.meg.marshalO2U(true);          // Buffer Pointer = 1
    this.meg.marshalSWORD(256);         // Buffer Length = 256
    this.meg.marshalO2U(true);          // Return Length Pointer = 1
    this.meg.marshalO2U(true);          // Return Version Pointer = 1
    if (this.connection.getTTCVersion() >= 11) {
        this.meg.marshalUB4(1L);        // Banner Format = FULL
    }
}
```

### 响应消息格式
```
OVERSION Response (TTIRPA):
+------------------+
| Version Length   |  // 版本字符串长度（2字节）
| (UB2)            |
+------------------+
| Version String   |  // 版本字符串（变长）
| (CHR)            |
+------------------+
| Version Number   |  // 版本号（4字节）
| (UB4)            |
+------------------+
```

## 4. OK2RPC (67) - K2远程过程调用

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：T4CTTIk2rpc.java

### TTI消息格式（基于源码分析）
```
OK2RPC Request Message:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x43)  |  // OK2RPC = 67
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Reserved (4)     |  // 保留字段（值为0）
+------------------+
| K2RPC Type (4)   |  // K2RPC类型
+------------------+
| Pointer (1)      |  // 指针标志
+------------------+
| Length (4)       |  // 长度（值为3）
+------------------+
| Null Pointers    |  // 多个空指针和长度字段
| and Lengths      |
+------------------+
| Command (4)      |  // 命令码
+------------------+
| Additional Data  |  // 附加数据（根据TTC版本）
| (Variable)       |
+------------------+
```

### K2RPC类型常量
```java
static final int K2RPClogon = 1;     // 登录
static final int K2RPCbegin = 2;     // 开始
static final int K2RPCend = 3;       // 结束
static final int K2RPCrecover = 4;   // 恢复
static final int K2RPCsession = 5;   // 会话
```

### 源码实现（T4CTTIk2rpc.java）
```java
void marshal() throws IOException {
    this.meg.marshalUB4(0L);                    // Reserved
    this.meg.marshalUB4(this.k2rpctyp);        // K2RPC Type
    this.meg.marshalPTR();                      // Pointer
    this.meg.marshalUB4(3L);                    // Length
    // ... 多个空指针和长度字段
    this.meg.marshalUB4(this.command);         // Command
    // ... TTC版本相关的附加字段
}
```

## 5. OALL7 (71) - Oracle 7版本复合操作

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类（可能已废弃）

### TTI消息格式（推测）
```
OALL7 Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x47)  |  // OALL7 = 71
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Operation Flags  |  // 操作标志（类似OALL8）
| (4 bytes)        |
+------------------+
| Cursor ID        |  // 游标ID
| (4 bytes)        |
+------------------+
| SQL Statement    |  // SQL语句（如果有）
| (Variable)       |
+------------------+
| Parameters       |  // 其他参数
| (Variable)       |
+------------------+
```

## 6. OSQL7 (74) - Oracle 7版本SQL执行

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类（可能已废弃）

### TTI消息格式（推测）
```
OSQL7 Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x4A)  |  // OSQL7 = 74
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| SQL Length       |  // SQL语句长度
| (4 bytes)        |
+------------------+
| SQL Statement    |  // SQL语句
| (Variable)       |
+------------------+
| Bind Count       |  // 绑定变量数量
| (4 bytes)        |
+------------------+
| Bind Data        |  // 绑定变量数据
| (Variable)       |
+------------------+
```

## 7. OEXFEN (78) - 执行操作结束

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：在T4C8Oall.java中作为特殊情况处理

### TTI消息格式（基于源码分析）
```
OEXFEN Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x4E)  |  // OEXFEN = 78
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Cursor ID        |  // 游标ID（4字节）
+------------------+
| Row Count        |  // 行数（4字节）
+------------------+
| Reserved         |  // 保留字段（4字节，值为96）
+------------------+
| Execute Flags    |  // 执行标志（4字节）
+------------------+
| Bind Info        |  // 绑定信息（如果有）
| (Variable)       |
+------------------+
```

### 源码实现片段（T4C8Oall.java）
```java
if (getFunCode() == 78) {  // OEXFEN
    this.meg.marshalSWORD(this.cursor);
    this.meg.marshalSWORD((int) this.al8i4[1]);
    this.meg.marshalSWORD(96);
    int exeflg = 0;
    if ((this.options & 256) == 256) {
        exeflg = 0 | 1;
    }
    // ... 其他处理
}
```

## 8. OKOD (92) - 内核操作描述符

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类

### TTI消息格式（推测）
```
OKOD Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x5C)  |  // OKOD = 92
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Operation Code   |  // 操作码
| (4 bytes)        |
+------------------+
| Descriptor Data  |  // 描述符数据
| (Variable)       |
+------------------+
```

## 9. OALL8 (94) - Oracle 8版本复合操作

### 详细分析请参考主分析文档
这是最重要和最复杂的函数码，详细格式已在主分析文档中描述。

## 10. ODNY (98) - 动态SQL操作

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：未在当前源码中找到具体实现类

### TTI消息格式（推测）
```
ODNY Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x62)  |  // ODNY = 98
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Dynamic Type     |  // 动态类型
| (4 bytes)        |
+------------------+
| SQL Length       |  // SQL长度
| (4 bytes)        |
+------------------+
| SQL Statement    |  // 动态SQL语句
| (Variable)       |
+------------------+
| Parameters       |  // 动态参数
| (Variable)       |
+------------------+
```

## 11. ODSY (119) - SQL语句描述

### 源码位置
- 定义：T4CTTIfunCodes.java
- 实现：T4C8Odsy.java

### TTI消息格式（基于源码分析）
```
ODSY Request Message:
+------------------+
| TTC Code (0x03)  |  // TTIFUN
+------------------+
| Function (0x77)  |  // ODSY = 119
+------------------+
| Sequence Number  |  // 序列号
+------------------+
| Object Pointer   |  // 对象名指针标志
| (1 byte)         |
+------------------+
| Object Length    |  // 对象名长度
| (4 bytes)        |
+------------------+
| Object Type      |  // 对象类型
| (1 byte)         |
+------------------+
| Reserved         |  // 保留字段（4字节，值为0）
+------------------+
| Null Pointer     |  // 空指针
+------------------+
| Flags            |  // 标志（4字节，值为14）
+------------------+
| Object Name      |  // 对象名数据
| (Variable)       |
+------------------+
```

### 对象类型常量
```java
static final short OCI_PTYPE_UNK = 0;      // 未知
static final short OCI_PTYPE_TABLE = 1;    // 表
static final short OCI_PTYPE_VIEW = 2;     // 视图
static final short OCI_PTYPE_PROC = 3;     // 过程
static final short OCI_PTYPE_FUNC = 4;     // 函数
static final short OCI_PTYPE_PKG = 5;      // 包
static final short OCI_PTYPE_TYPE = 6;     // 类型
static final short OCI_PTYPE_SYN = 7;      // 同义词
static final short OCI_PTYPE_SEQ = 8;      // 序列
```

### 源码实现（T4C8Odsy.java）
```java
void marshal() throws IOException {
    if (this.objectName.length == 0) {
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
    } else {
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.objectName.length);
    }
    this.meg.marshalUB1(this.objectType);
    this.meg.marshalUB4(0L);
    this.meg.marshalNULLPTR();
    this.meg.marshalUB4(14L);
    this.meg.marshalCHR(this.objectName);
}
```

## 版本兼容性说明

### Oracle版本演进对TTI的影响：

1. **Oracle 7**: 使用OALL7、OSQL7等函数码
2. **Oracle 8**: 引入OALL8，支持更复杂的复合操作
3. **Oracle 8i及以后**: 逐步废弃旧函数码，主要使用OALL8

### TTC版本对消息格式的影响：

- **TTC < 2**: 基本功能
- **TTC >= 2**: 支持定义列
- **TTC >= 4**: 支持注册和反馈
- **TTC >= 8**: 扩展K2RPC功能
- **TTC >= 11**: OVERSION支持横幅格式

## 总结

通过源码分析，我们发现：

1. **OVERSION (59)** 有完整的实现，格式清晰
2. **OK2RPC (67)** 有完整的实现，支持多种K2RPC操作
3. **ODSY (119)** 有完整的实现，用于对象描述
4. **OALL8 (94)** 是最复杂和重要的函数码
5. **OALL7 (71)** 和 **OSQL7 (74)** 可能已废弃
6. **OV6STRT (48)** 和 **OV6STOP (49)** 未找到具体实现

这些分析为理解Oracle TTI协议提供了重要的技术细节。
