{"metadata": {"sample_description": "OALL8 (TTIFUN 0x5E) sample — annotated by field", "notes": "部分字段在网络格式上使用不同字节序或为内嵌短字段+填充。大量 0xFF/0xFE 为哨兵（-1/-2）或填充。请按 field-level semantics 选择字节序。"}, "fields": [{"offset_hex": "0x00", "offset_dec": 0, "length": 1, "raw_hex": "03", "unsigned": 3, "signed": 3, "name": "TTI Type", "interpretation": "TTI frame type; 0x03 == TTIFUN (Function call)", "parse_action": "verify == 0x03 then dispatch to TTIFUN parser", "suggested_endianness": "N/A"}, {"offset_hex": "0x01", "offset_dec": 1, "length": 1, "raw_hex": "5E", "unsigned": 94, "signed": 94, "name": "Function Code", "interpretation": "0x5E == OALL8 (composite operation: parse/bind/execute/... )", "parse_action": "enter OALL8 body parser", "suggested_endianness": "N/A"}, {"offset_hex": "0x02", "offset_dec": 2, "length": 1, "raw_hex": "06", "unsigned": 6, "signed": 6, "name": "Small control/seq byte #1", "interpretation": "driver/version dependent small control/sequence byte", "parse_action": "preserve; may be part of driver seq/flags", "suggested_endianness": "N/A"}, {"offset_hex": "0x03", "offset_dec": 3, "length": 1, "raw_hex": "61", "unsigned": 97, "signed": 97, "name": "Small control/seq byte #2", "interpretation": "driver/version dependent small control/sequence byte", "parse_action": "preserve; see driver traces if needed", "suggested_endianness": "N/A"}, {"offset_hex": "0x04", "offset_dec": 4, "length": 4, "raw_hex": "80000000", "unsigned": 2147483648, "signed": -2147483648, "name": "Options (marshalUB4)", "interpretation": "bitfield of OALL8 options; 0x80000000 (high bit) set — semantics depend on JDBC/OCI version", "parse_action": "interpret bits if mapping available; else store raw options for downstream behavior", "suggested_endianness": "big-endian (network order)"}, {"offset_hex": "0x08", "offset_dec": 8, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Cursor / stmt handle (possible marshalSWORD/marshalUB4)", "interpretation": "0 = no allocated cursor id / implicit cursor", "parse_action": "record stmt_handle = 0 (may be assigned later by server response)", "suggested_endianness": "big-endian"}, {"section": "pointer_indicator_table", "description": "Block from 0x0C to 0x94 — pointer/indicator table and small-field-packed entries. Many 0xFF/0xFE are sentinels or padding."}, {"offset_hex": "0x0C", "offset_dec": 12, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator[0]", "interpretation": "0xFFFFFFFF => sentinel NULL pointer / absent segment", "parse_action": "treat as 'no segment' (skip)", "suggested_endianness": "big-endian"}, {"offset_hex": "0x10", "offset_dec": 16, "length": 4, "raw_hex": "FFFFFF6F", "unsigned": 4294967151, "signed": -145, "name": "Pointer/Indicator[1] (packed)", "interpretation": "high bytes 0xFF padding; low byte 0x6F likely a meaningful 1-byte field (0x6F = 111 decimal / ASCII 'o')", "parse_action": "extract low_byte = 0x6F as possible flag/count; treat high bytes as padding/sentinel", "suggested_endianness": "big-endian for pointer-table semantics; low-byte interpreted as 8-bit"}, {"offset_hex": "0x14", "offset_dec": 20, "length": 4, "raw_hex": "000000FE", "unsigned": 254, "signed": 254, "name": "Indicator / short-flag", "interpretation": "0xFE used frequently as indicator / special short code", "parse_action": "treat as 1-byte indicator (value 0xFE); do not treat as valid pointer", "suggested_endianness": "big-endian (but semantics are 1-byte)"}, {"offset_hex": "0x18", "offset_dec": 24, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x1C", "offset_dec": 28, "length": 4, "raw_hex": "FFFFFF0D", "unsigned": 4294967053, "signed": -243, "name": "Packed small-field (likely count)", "interpretation": "low byte = 0x0D (13 decimal) — matches AL8I4 array length 13 observed later", "parse_action": "extract low_byte as element_count = 13; treat high bytes as padding", "suggested_endianness": "big-endian for pointer-table semantics; low-byte parsed as 8-bit"}, {"offset_hex": "0x20", "offset_dec": 32, "length": 4, "raw_hex": "000000FE", "unsigned": 254, "signed": 254, "name": "Indicator / short-flag", "interpretation": "0xFE indicator/flag", "parse_action": "record indicator"}, {"offset_hex": "0x24", "offset_dec": 36, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x28", "offset_dec": 40, "length": 4, "raw_hex": "FFFFFFFE", "unsigned": 4294967294, "signed": -2, "name": "Pointer/Indicator", "interpretation": "0xFFFFFFFE (-2) — special marker used by marshal as alternative sentinel", "parse_action": "treat as 'special placeholder' (not a valid offset)"}, {"offset_hex": "0x2C", "offset_dec": 44, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x30", "offset_dec": 48, "length": 4, "raw_hex": "FFFFFF00", "unsigned": 4294967040, "signed": -256, "name": "Packed small-field / padding", "interpretation": "low byte 0x00, high bytes FF — likely padding for an absent short field", "parse_action": "treat as padding / no-op"}, {"offset_hex": "0x34", "offset_dec": 52, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word (padding/placeholder)", "interpretation": "explicit zero, often reserved or cleared pointer", "parse_action": "record zero"}, {"offset_hex": "0x38", "offset_dec": 56, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x3C", "offset_dec": 60, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x40", "offset_dec": 64, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x44", "offset_dec": 68, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x48", "offset_dec": 72, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x4C", "offset_dec": 76, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x50", "offset_dec": 80, "length": 4, "raw_hex": "FFFFFF00", "unsigned": 4294967040, "signed": -256, "name": "Packed field / padding", "interpretation": "low byte 0x00, likely padding", "parse_action": "treat as padding"}, {"offset_hex": "0x54", "offset_dec": 84, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding/placeholder", "parse_action": "record zero"}, {"offset_hex": "0x58", "offset_dec": 88, "length": 4, "raw_hex": "000000FE", "unsigned": 254, "signed": 254, "name": "Indicator / short-flag", "interpretation": "0xFE indicator", "parse_action": "record indicator"}, {"offset_hex": "0x5C", "offset_dec": 92, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x60", "offset_dec": 96, "length": 4, "raw_hex": "FFFFFFFE", "unsigned": 4294967294, "signed": -2, "name": "Pointer/Indicator", "interpretation": "-2 sentinel (special placeholder)", "parse_action": "treat as special placeholder"}, {"offset_hex": "0x64", "offset_dec": 100, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x68", "offset_dec": 104, "length": 4, "raw_hex": "FFFFFFFE", "unsigned": 4294967294, "signed": -2, "name": "Pointer/Indicator", "interpretation": "-2 sentinel", "parse_action": "special placeholder"}, {"offset_hex": "0x6C", "offset_dec": 108, "length": 4, "raw_hex": "FFFFFFFF", "unsigned": 4294967295, "signed": -1, "name": "Pointer/Indicator", "interpretation": "NULL sentinel", "parse_action": "skip"}, {"offset_hex": "0x70", "offset_dec": 112, "length": 4, "raw_hex": "FFFFFF00", "unsigned": 4294967040, "signed": -256, "name": "Packed / padding", "interpretation": "likely padding", "parse_action": "ignore"}, {"offset_hex": "0x74", "offset_dec": 116, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x78", "offset_dec": 120, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x7C", "offset_dec": 124, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x80", "offset_dec": 128, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x84", "offset_dec": 132, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x88", "offset_dec": 136, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x8C", "offset_dec": 140, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word", "interpretation": "padding", "parse_action": "ignore"}, {"offset_hex": "0x90", "offset_dec": 144, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Zero word (end of pointer/indicator area)", "interpretation": "padding / end of pointer block", "parse_action": "next should be SQL length or SQL marker"}, {"offset_hex": "0x95", "offset_dec": 149, "length": 1, "raw_hex": "25", "unsigned": 37, "signed": 37, "name": "SQL-length-or-leading-char", "interpretation": "0x25 could be (A) SQL length = 37, or (B) ASCII '%' character (0x25) that is first char of SQL text. In the raw sample the SQL text begins with '%' followed by 'SELECT ...' so this byte is ambiguous: it is both 0x25 and ASCII '%'.", "parse_action": "If next bytes form printable ASCII (e.g. 'SELECT'), treat 0x25 as ASCII '%' and set sql_bytes starting at this offset; else if protocol demands a length field here, treat as 1-byte length and read following N bytes as SQL. Implement parser fallback that checks for ASCII printability.", "suggested_endianness": "N/A"}, {"offset_hex": "0x96", "offset_dec": 150, "length": 37, "raw_hex": "53454C4543542055534552454E56282753455353494F4E494427292046524F4D204455414C", "unsigned": null, "signed": null, "name": "SQL text (or from 0x96 if 0x95 is length)", "interpretation": "ASCII: \"SELECT USERENV('SESSIONID') FROM DUAL\" — in the raw sample preceding byte 0x25 is '%' character, so full SQL payload observed as \"%SELECT USERENV('SESSIONID') FROM DUAL\"", "parse_action": "decode bytes as charset (usually ASCII/UTF-8) and record SQL; if 0x95 used as length, start SQL at 0x96; if 0x95 is ASCII %, then SQL starts at 0x95 (include '%').", "suggested_endianness": "N/A"}, {"offset_hex": "0xBB", "offset_dec": 187, "length": 52, "raw_hex": "01000000000000000000000000000000000000000000000000000000000000000000000000000000", "unsigned": null, "signed": null, "name": "AL8I4 array (13 × 4 bytes)", "interpretation": "13 elements; sample values (little-endian interpretation per element): [1,0,0,0,0,0,0,0,0,0,0,0,0]", "parse_action": "parse as 13 little-endian 32-bit integers; element[0]=1, element[1..12]=0. This array is a standard control/length/offset array in OJDBC marshaling.", "suggested_endianness": "little-endian for this AL8I4 block"}, {"offset_hex": "0xEF", "offset_dec": 239, "length": 4, "raw_hex": "01000000", "unsigned": 1, "signed": 1, "name": "Tail/aux field #1", "interpretation": "small flag/placeholder possibly indicating presence of subsequent optional blocks", "parse_action": "record; likely a boolean/flag"}, {"offset_hex": "0xF3", "offset_dec": 243, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #2", "interpretation": "zero placeholder (no binds/defines in this sample)", "parse_action": "record"}, {"offset_hex": "0xF7", "offset_dec": 247, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #3", "interpretation": "zero placeholder", "parse_action": "record"}, {"offset_hex": "0xFB", "offset_dec": 251, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #4", "interpretation": "zero placeholder", "parse_action": "record"}, {"offset_hex": "0xFF", "offset_dec": 255, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #5", "interpretation": "zero placeholder", "parse_action": "record"}, {"offset_hex": "0x103", "offset_dec": 259, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #6", "interpretation": "zero placeholder", "parse_action": "record"}, {"offset_hex": "0x107", "offset_dec": 263, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #7", "interpretation": "zero placeholder", "parse_action": "record"}, {"offset_hex": "0x10B", "offset_dec": 267, "length": 4, "raw_hex": "00000000", "unsigned": 0, "signed": 0, "name": "Tail/aux field #8", "interpretation": "zero placeholder (end of sample)", "parse_action": "record; end of current message"}], "final_notes": {"key_takeaways": ["0x0C–0x94 region is a pointer/indicator table containing many sentinel values (0xFFFFFFFF == -1 for NULL, 0xFFFFFFFE == -2 special), packed small fields and padding.", "AL8I4 array (13 int32s) begins immediately after SQL and is encoded little-endian in this sample; element[0]=1, rest zero.", "The single byte 0x25 at 0x95 is ambiguous: it is both ASCII '%' and numeric 37. Use a fall-back parser: prefer 'printable ASCII scan' to locate SQL text, else interpret as length field.", "Implement per-field endianness rules: pointer table interpreted as big-endian (with low-byte significance), AL8I4 as little-endian, SQL as raw bytes decoded by charset."], "parser_recommendations": ["Scan pointer/indicator area as 4-byte words; handle -1/-2 specially; if high 3 bytes == 0xFF and low byte != 0, treat low byte as 8-bit count/flag.", "Detect SQL robustly by checking for printable ASCII sequences (\"SELECT\", \"%SELECT\"), but also support explicit length decoding where protocol version indicates it.", "Keep statement handle / pointer table / AL8I4 values in session state so subsequent TTIRPA/TTIRXH responses can be correlated."]}}