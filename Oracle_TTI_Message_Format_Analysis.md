# Oracle JDBC驱动TTI（Two-Task Interface）消息格式深度分析

## 概述

基于对Oracle JDBC驱动源码的深入分析，本文档详细描述了TTI（Two-Task Interface）消息格式的实现细节。TTI是Oracle客户端与服务器之间通信的核心协议。

## TTI消息基本结构

### 1. TTI消息头部（TTI Header）

根据源码分析，TTI消息的基本结构如下：

```
TTI Message Header:
+------------------+
| TTC Code (1 byte)|  // 消息类型码
+------------------+
| Function Code    |  // 函数码（对于TTIFUN类型）
| (1 byte)         |
+------------------+
| Sequence Number  |  // 序列号（对于TTIFUN类型）
| (1 byte)         |
+------------------+
| Token Number     |  // 令牌号（TTC版本>=18时，8字节）
| (8 bytes)        |
+------------------+
| Message Body     |  // 消息体（变长）
| (Variable)       |
+------------------+
```

### 2. TTC消息类型码（来自T4CTTIMsgCodes.java）

```java
public static final byte TTIPRO = 1;    // 协议消息
public static final byte TTIDTY = 2;    // 数据类型消息
public static final byte TTIFUN = 3;    // 函数调用消息
public static final byte TTIOER = 4;    // 错误消息
public static final byte TTIRXH = 6;    // 行头消息
public static final byte TTIRXD = 7;    // 行数据消息
public static final byte TTIRPA = 8;    // 返回参数消息
public static final byte TTISTA = 9;    // 状态消息
public static final byte TTIIOV = 11;   // I/O向量消息
public static final byte TTISLG = 12;   // 流长消息
public static final byte TTIOAC = 13;   // Oracle访问控制消息
public static final byte TTILOBD = 14;  // LOB数据消息
public static final byte TTIWRN = 15;   // 警告消息
public static final byte TTIDCB = 16;   // 描述控制块消息
public static final byte TTIPFN = 17;   // 捎带函数消息
public static final byte TTIFOB = 19;   // 函数对象消息
public static final byte TTIQC = 24;    // 查询缓存消息
public static final byte TTIRSH = 25;   // 结果集头消息
public static final byte TTICOOKIE = 30; // Cookie消息
```

## 主要函数码分析（来自T4CTTIfunCodes.java）

### 1. 核心SQL执行函数码

```java
public static final short OOPEN = 2;      // 打开游标
public static final short OFETCH = 5;     // 获取数据
public static final short OCLOSE = 8;     // 关闭游标
public static final short OLOGOFF = 9;    // 登出
public static final short OCOMMIT = 14;   // 提交事务
public static final short OROLLBACK = 15; // 回滚事务
public static final short OCANCEL = 20;   // 取消操作
public static final short ODSCRARR = 43;  // 描述数组
public static final short OV6STRT = 48;   // V6启动操作
public static final short OV6STOP = 49;   // V6停止操作
public static final short OVERSION = 59;  // 版本信息获取
public static final short OK2RPC = 67;    // K2远程过程调用
public static final short OALL7 = 71;     // Oracle 7版本复合操作
public static final short OSQL7 = 74;     // Oracle 7版本SQL执行
public static final short OEXFEN = 78;    // 执行操作结束
public static final short OKOD = 92;      // 内核操作描述符
public static final short OALL8 = 94;     // Oracle 8版本复合操作
public static final short ODNY = 98;      // 动态SQL操作
public static final short ODSY = 119;     // SQL语句描述
```

### 2. 扩展功能函数码

```java
public static final short OAUTH = 115;    // 认证
public static final short OSESSKEY = 118; // 会话密钥
public static final short OCANA = 120;    // 取消分析
public static final short OKPN = 125;     // 内核参数
public static final short OPING = 147;    // Ping操作
public static final short OKEYVAL = 154;  // 键值对
public static final short OXSSCS = 155;   // XS安全上下文
public static final short OXSSRO = 156;   // XS安全角色
public static final short OXSSPO = 157;   // XS安全策略
```

## TTIFUN消息格式详细分析

### 1. TTIFUN消息头部编码（来自T4CTTIfun.java）

```java
private final void marshalFunHeader(long tokenNumber) throws IOException {
    marshalTTCcode();                           // 编码TTC类型码（TTIFUN = 3）
    this.connection.lastExecutedFunCode(this.funCode);
    this.meg.marshalUB1(this.funCode);         // 编码函数码（1字节）
    this.sequenceNumber = this.connection.getNextSeqNumber();
    this.meg.marshalUB1(this.sequenceNumber);  // 编码序列号（1字节）
    
    // TTC版本>=18时包含令牌号
    if (this.connection.getTTCVersion() >= 18) {
        this.meg.marshalUB8(tokenNumber);      // 编码令牌号（8字节）
    }
}
```

### 2. OALL8函数码（94）消息格式

OALL8是Oracle 8及以后版本的主要SQL执行函数，支持解析、绑定、执行、获取等复合操作。

#### 消息体结构（来自T4C8Oall.java）：

```
OALL8 Message Body:
+------------------+
| Options (4 bytes)|  // 操作选项位掩码
+------------------+
| Cursor (4 bytes) |  // 游标ID
+------------------+
| SQL Statement    |  // SQL语句（变长）
| Pointer & Length |
+------------------+
| AL8I4 Array      |  // 参数数组（13个4字节整数）
| (52 bytes)       |
+------------------+
| Bind Definitions |  // 绑定变量定义（可选）
| (Variable)       |
+------------------+
| Define Columns   |  // 定义列信息（可选）
| (Variable)       |
+------------------+
| Bind Data        |  // 绑定变量数据（可选）
| (Variable)       |
+------------------+
```

#### 操作选项位掩码：

```java
static final int UOPF_PRS = 1;      // 解析操作
static final int UOPF_BND = 8;      // 绑定操作
static final int UOPF_DFN = 16;     // 定义操作
static final int UOPF_EXE = 32;     // 执行操作
static final int UOPF_FCH = 64;     // 获取操作
static final int UOPF_CAN = 128;    // 取消操作
static final int UOPF_COM = 256;    // 提交操作
static final int UOPF_FEX = 512;    // 获取并执行
static final int UOPF_SIO = 1024;   // 流I/O操作
static final int UOPF_NPL = 32768;  // 非PL/SQL操作
static final int UOPF_DSY = 131072; // 描述操作
static final int UOPF_NCF = 262144; // 无游标获取
static final int UOPF_BER = 524288; // 批量错误报告
```

## 数据类型编码方式

### 1. 基本数据类型编码（来自T4CMAREngine.java）

```java
// 1字节无符号整数
abstract void marshalUB1(short value) throws IOException;

// 2字节无符号整数  
abstract void marshalUB2(int value) throws IOException;

// 4字节有符号整数
abstract void marshalSB4(int value) throws IOException;

// 4字节无符号整数
abstract void marshalUB4(long value) throws IOException;

// 8字节无符号整数
abstract void marshalUB8(long value) throws IOException;

// 8字节有符号整数
abstract void marshalSB8(long value) throws IOException;

// 字节数组
abstract void marshalB1Array(byte[] value) throws IOException;
```

### 2. 字符串编码（CHR类型）

```java
final void marshalCHR(byte[] value) throws IOException {
    marshalCHR(value, 0, value.length);
}
```

### 3. 指针编码

```java
final void marshalNULLPTR() throws IOException {
    addPtr((byte) 0);  // 空指针
}

final void marshalPTR() throws IOException {
    addPtr((byte) 1);  // 非空指针
}
```

## 具体函数码消息格式

### 1. OVERSION（59）- 版本信息获取

```
OVERSION Message Body:
+------------------+
| Buffer Pointer   |  // 缓冲区指针（1字节）
| (1 byte)         |
+------------------+
| Buffer Length    |  // 缓冲区长度（4字节）
| (4 bytes)        |
+------------------+
| Return Length    |  // 返回长度指针（1字节）
| Pointer (1 byte) |
+------------------+
| Return Version   |  // 返回版本指针（1字节）
| Pointer (1 byte) |
+------------------+
| Banner Format    |  // 横幅格式（4字节，TTC版本>=11）
| (4 bytes)        |
+------------------+
```

### 2. OK2RPC（67）- K2远程过程调用

```
OK2RPC Message Body:
+------------------+
| Reserved (4 bytes)|  // 保留字段（值为0）
+------------------+
| K2RPC Type       |  // K2RPC类型（4字节）
| (4 bytes)        |
+------------------+
| Command (4 bytes)|  // 命令码（4字节）
+------------------+
| Additional Fields|  // 其他字段（根据TTC版本变化）
| (Variable)       |
+------------------+
```

### 3. ODSY（119）- SQL语句描述

```
ODSY Message Body:
+------------------+
| Object Name      |  // 对象名指针和长度
| Pointer & Length |
+------------------+
| Object Type      |  // 对象类型（1字节）
| (1 byte)         |
+------------------+
| Reserved (4 bytes)|  // 保留字段
+------------------+
| Null Pointer     |  // 空指针
| (1 byte)         |
+------------------+
| Flags (4 bytes)  |  // 标志位（值为14）
+------------------+
| Object Name Data |  // 对象名数据（变长）
| (Variable)       |
+------------------+
```

## TTI消息编码示例

### OVERSION消息编码示例：

```
Hex: 03 3B 01 01 00 01 00 01 01 00 00 00 01
     |  |  |  |     |     |  |  |        |
     |  |  |  |     |     |  |  |        +-- Banner Format (4 bytes)
     |  |  |  |     |     |  |  +-- Return Version Pointer
     |  |  |  |     |     |  +-- Return Length Pointer  
     |  |  |  |     |     +-- Buffer Length (256)
     |  |  |  |     +-- Buffer Pointer
     |  |  |  +-- Sequence Number
     |  |  +-- Function Code (OVERSION = 59)
     |  +-- TTC Code (TTIFUN = 3)
     +-- 消息开始
```

## 版本差异分析

### TTC版本对消息格式的影响：

1. **TTC版本 >= 2**: 支持定义列信息
2. **TTC版本 >= 4**: 支持注册ID和反馈
3. **TTC版本 >= 5**: 扩展参数支持
4. **TTC版本 >= 7**: 支持DML批量操作
5. **TTC版本 >= 8**: 扩展K2RPC支持
6. **TTC版本 >= 11**: OVERSION支持横幅格式
7. **TTC版本 >= 18**: 支持令牌号

## 错误处理

TTI协议使用TTIOER消息类型处理错误：

```
TTIOER Message Structure:
+------------------+
| Error Code       |  // 错误码（4字节）
| (4 bytes)        |
+------------------+
| Error Message    |  // 错误消息（变长）
| (Variable)       |
+------------------+
| Additional Info  |  // 附加信息（可选）
| (Variable)       |
+------------------+
```

## 总结

通过对Oracle JDBC驱动源码的深入分析，我们可以看到TTI协议是一个高度优化的二进制协议，支持：

1. **多种消息类型**：函数调用、数据传输、错误处理等
2. **复合操作**：OALL8支持在单个消息中组合多个操作
3. **版本兼容性**：通过TTC版本号实现向后兼容
4. **高效编码**：使用紧凑的二进制格式减少网络开销
5. **扩展性**：支持新功能和数据类型的添加

这种设计使得Oracle客户端与服务器之间能够进行高效、可靠的通信。
