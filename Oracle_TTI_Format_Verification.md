# Oracle TTI消息格式验证报告

## 概述

本文档对比分析您提供的TTI消息格式定义与Oracle JDBC驱动源码的实际实现，验证格式定义的准确性并指出差异。

## 1. TTI Header格式验证

### 您提供的格式定义：
```
TTI Header:
- tti_type (1 byte): 消息类型
- tti_length (2 bytes): 消息长度
- tti_flags (1 byte): 标志位
- tti_checksum (2 bytes): 校验和
```

### 源码实际实现：
```
TTI Message Header (基于T4CTTIfun.java):
- TTC Code (1 byte): 消息类型码（TTIFUN = 3）
- Function Code (1 byte): 函数码
- Sequence Number (1 byte): 序列号
- Token Number (8 bytes): 令牌号（TTC版本>=18时）
```

### 验证结果：
❌ **格式定义不完全准确**

**主要差异：**
1. **长度字段缺失**: 源码中没有显式的2字节长度字段
2. **标志位字段缺失**: 源码中没有1字节标志位字段
3. **校验和字段缺失**: 源码中没有2字节校验和字段
4. **序列号字段**: 您的定义中未包含序列号字段
5. **令牌号字段**: 您的定义中未包含令牌号字段（新版本特性）

## 2. TTIFUN消息类型验证

### 您提供的定义：
```
TTIFUN消息类型（0x03）的结构正确
```

### 源码验证：
```java
// T4CTTIMsgCodes.java
public static final byte TTIFUN = 3;  // ✅ 正确
```

### 验证结果：
✅ **TTIFUN消息类型定义正确**

## 3. OALL8函数码验证

### 您提供的定义：
```
OALL8常量（值为94）
```

### 源码验证：
```java
// T4CTTIfunCodes.java
public static final short OALL8 = 94;  // ✅ 正确

// T4C8Oall.java
setFunCode((short) 94);  // ✅ 正确
```

### 验证结果：
✅ **OALL8函数码定义正确**

## 4. OALL8消息体结构验证

### 您提到的子块结构：
- PARSE Block
- BIND Block  
- EXECUTE Block
- DESCRIBE Block

### 源码实际实现：

OALL8并非使用独立的子块结构，而是使用**操作标志位**来控制不同操作：

```java
// T4C8Oall.java - 操作标志位
static final int UOPF_PRS = 1;      // 解析操作
static final int UOPF_BND = 8;      // 绑定操作  
static final int UOPF_DFN = 16;     // 定义操作
static final int UOPF_EXE = 32;     // 执行操作
static final int UOPF_FCH = 64;     // 获取操作
```

### 实际消息体结构：
```
OALL8 Message Body:
+------------------+
| Options (4 bytes)|  // 操作选项位掩码（包含PARSE、BIND、EXECUTE等标志）
+------------------+
| Cursor (4 bytes) |  // 游标ID
+------------------+
| SQL Pointer      |  // SQL语句指针和长度
| & Length         |
+------------------+
| AL8I4 Array      |  // 参数数组（13个4字节整数）
| (52 bytes)       |
+------------------+
| Bind Definitions |  // 绑定变量定义（条件性包含）
| (Variable)       |
+------------------+
| Define Columns   |  // 定义列信息（条件性包含）
| (Variable)       |
+------------------+
| Bind Data        |  // 绑定变量数据（条件性包含）
| (Variable)       |
+------------------+
```

### 验证结果：
⚠️ **部分正确，但结构描述不准确**

**主要差异：**
1. **不是独立子块**: OALL8使用统一的消息体结构，通过标志位控制操作
2. **操作组合**: 可以在单个消息中组合多个操作（如PARSE+BIND+EXECUTE）
3. **条件性字段**: 某些字段只在特定操作标志位设置时才包含

## 5. 特定函数码验证

### 您询问的函数码验证结果：

| 函数码 | 您的描述 | 源码验证 | 准确性 |
|--------|----------|----------|--------|
| OV6STRT (48) | V6启动操作 | ✅ 定义存在 | ✅ 正确 |
| OV6STOP (49) | V6停止操作 | ✅ 定义存在 | ✅ 正确 |
| OVERSION (59) | 版本信息获取 | ✅ 完整实现 | ✅ 正确 |
| OK2RPC (67) | K2远程过程调用 | ✅ 完整实现 | ✅ 正确 |
| OALL7 (71) | Oracle 7版本复合操作 | ✅ 定义存在 | ✅ 正确 |
| OSQL7 (74) | Oracle 7版本SQL执行 | ✅ 定义存在 | ✅ 正确 |
| OEXFEN (78) | 执行操作结束 | ✅ 完整实现 | ✅ 正确 |
| OKOD (92) | 内核操作描述符 | ✅ 定义存在 | ✅ 正确 |
| OALL8 (94) | Oracle 8版本复合操作 | ✅ 完整实现 | ✅ 正确 |
| ODNY (98) | 动态SQL操作 | ✅ 定义存在 | ✅ 正确 |
| ODSY (119) | SQL语句描述 | ✅ 完整实现 | ✅ 正确 |

## 6. 标准TTI消息格式定义（基于源码分析）

### 修正后的TTI Header格式：

```
TTI Message Header:
+------------------+
| TTC Code (1 byte)|  // 消息类型码（如TTIFUN=3）
+------------------+
| Function Code    |  // 函数码（仅TTIFUN类型）
| (1 byte)         |
+------------------+
| Sequence Number  |  // 序列号（仅TTIFUN类型）
| (1 byte)         |
+------------------+
| Token Number     |  // 令牌号（TTC版本>=18时，8字节）
| (8 bytes)        |  // 仅TTIFUN类型且TTC版本>=18
+------------------+
```

### 完整的TTIFUN消息格式：

```
TTIFUN Message Format:
+------------------+
| TTC Code (0x03)  |  // TTIFUN消息类型
+------------------+
| Function Code    |  // 具体函数码（如OALL8=94）
| (1 byte)         |
+------------------+
| Sequence Number  |  // 序列号
| (1 byte)         |
+------------------+
| Token Number     |  // 令牌号（可选，取决于TTC版本）
| (0 or 8 bytes)   |
+------------------+
| Function Body    |  // 函数特定的消息体
| (Variable)       |
+------------------+
```

## 7. 数据编码方式验证

### 您可能假设的编码方式：
- 网络字节序（大端序）
- 固定长度字段

### 源码实际编码方式：

```java
// T4CMAREngineNIO.java - 实际编码实现
final void marshalUB1(short value) throws IOException {
    // 1字节无符号整数
}

final void marshalUB2(int value) throws IOException {
    // 2字节无符号整数，支持可变长度编码
}

final void marshalUB4(long value) throws IOException {
    // 4字节无符号整数，支持可变长度编码
}
```

### 验证结果：
⚠️ **编码方式比预期复杂**

**关键发现：**
1. **可变长度编码**: Oracle使用可变长度编码优化网络传输
2. **类型表示**: 通过T4CTypeRep控制编码方式
3. **字节序**: 支持不同的字节序设置

## 8. 版本差异验证

### 您可能未考虑的版本差异：

```java
// TTC版本对消息格式的影响
if (this.connection.getTTCVersion() >= 18) {
    this.meg.marshalUB8(tokenNumber);  // 令牌号字段
}

if (this.connection.getTTCVersion() >= 11) {
    this.meg.marshalUB4(1L);  // OVERSION横幅格式
}

if (this.connection.getTTCVersion() >= 2) {
    // 支持定义列信息
}
```

### 验证结果：
❌ **版本差异未充分考虑**

## 9. 总体验证结论

### ✅ 准确的部分：
1. **TTIFUN消息类型**: 0x03正确
2. **OALL8函数码**: 94正确
3. **函数码含义**: 大部分函数码描述准确
4. **基本概念**: TTI作为Oracle通信协议的理解正确

### ❌ 需要修正的部分：
1. **TTI Header结构**: 缺少序列号和令牌号字段
2. **长度和校验和字段**: 源码中未发现这些字段
3. **OALL8子块结构**: 实际使用标志位而非独立子块
4. **版本兼容性**: 需要考虑TTC版本对格式的影响
5. **编码方式**: 实际使用可变长度编码

### ⚠️ 需要进一步研究的部分：
1. **消息长度计算**: 可能在更底层的网络协议中处理
2. **校验和机制**: 可能在TLS/SSL层或更底层实现
3. **旧版本函数码**: OALL7、OSQL7等的具体实现

## 10. 建议的标准TTI格式定义

基于源码分析，建议使用以下标准格式定义：

```
Oracle TTI Message Format (Standard):

1. Message Header:
   - TTC Code (1 byte): 消息类型
   - [Function Code (1 byte)]: 函数码（仅TTIFUN）
   - [Sequence Number (1 byte)]: 序列号（仅TTIFUN）
   - [Token Number (8 bytes)]: 令牌号（可选）

2. Message Body:
   - 根据TTC Code和Function Code确定具体格式
   - 使用可变长度编码优化传输
   - 支持版本相关的字段变化

3. Encoding Rules:
   - 支持可变长度编码
   - 字节序可配置
   - 版本兼容性考虑
```

这个定义更准确地反映了Oracle JDBC驱动的实际实现。
